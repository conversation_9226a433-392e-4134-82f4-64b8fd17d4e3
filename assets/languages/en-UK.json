{"auth": {"password": "Password", "welcome": "Welcome", "back": "Back", "pleaseEnterYourEmail": "Please enter your email & password to access your account.", "email": "Email", "enterYourEmail": "Enter your email", "Password": "Password", "enterYourPassword": "Enter Your Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot password?", "forgot": "Forgot", "confirmationCode": "  Please enter your email and we will send a confirmation code to your email", "send": "send", "otp": "OTP", "enterVerification": "Enter Verification", "code": "Code", "codeToYyourEmail": "We’ve sent an OTP code to <NAME_EMAIL>", "verify": "Verify", "wrongOTP": "Wrong OTP", "codeWillExpire": "The code will expire", "seconds": "seconds", "resend": "Resend", "createANew": "Create ", "strongPassword": "Set a strong password to keep secure your account", "newPassword": "New password", "ConfirmNewPassword": "Confirm New password", "enterYourConfirm": "Enter Your Confirm New Password", "min8Characters": "Min 8 characters", "2passwords": "The 2 passwords are the same", "leastoneCase": "At least one Case Character", "oneNumericCharacter": "At least one Numeric Character", "submit": "Submit", "congratulation!": "Congratulation!", "passwordhasbeenchanged": "your password has been changed", "trytokeepthepassword": "Try to keep the password away to avoid theft of your account and data", "loginToAccount": "Login to your account", "phoneNumber": "Phone number", "login": "<PERSON><PERSON>", "dontHaveAccount": "Don't have an account?", "createAccount": "Create account", "registerWelcome": "Happy to have you join us!", "registerDescription": "Enter your phone number to create your account", "fullName": "Full name", "confirmPassword": "Confirm password", "confirmationCodeMessage": "You will receive a verification code on your phone. Make sure you have your phone with you.", "confirm": "Confirm", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in"}, "splash": {"jECERP": "JEC ERP", "poweringEfficiency": "Powering Efficiency, Driving Growth!"}, "onboarding": {"skip": "<PERSON><PERSON>", "next": "Next", "getStarted": "Get Started", "easilytrack": "Easily track and organize your working hours", "gettheattendance": "Get the attendance and absence report quickly", "followthestatus ": "Follow the status of requests for vacations and others", "startnowandwork": "Start now and work to make your daily routine and time easier", "knowthedates": "Know the dates of attendance and absence easily in just a few minutes", "saveMoney": "Save Money and Pay with Your Preferred Currency"}, "validation": {"required": "{field} is required.", "username": {"required": "Username is required.", "invalid": "Invalid username."}, "email": {"required": "Email is required.", "invalid": "Invalid email address."}, "password": {"required": "Password is required.", "minLength": "Password must be at least 6 characters long.", "uppercase": "Password must contain at least one uppercase letter.", "number": "Password must contain at least one number.", "specialChar": "Password must contain at least one special character."}, "confirmPassword": {"required": "Confirm password is required.", "mismatch": "Password and confirmation do not match."}, "phone": {"required": "Phone number is required.", "invalid": "Invalid phone number. It must be in the format +9665XXXXXXXX."}, "otp": {"required": "OTP is required.", "invalid": "OTP must be exactly 6 digits."}}, "otp": {"accountVerification": "Account verification code!", "enterCodeSentTo": "Enter the verification code sent to", "validOtp": "Enter a valid OTP", "confirm": "Confirm", "resendCode": "Resend code", "didNotReceiveCode": "If you did not receive a message", "resend": "Resend code", "seconds": "seconds"}, "search": {"search": "Search", "searchResults": "Search results", "searchHistory": "Search history", "resultsAreOut": "No more results"}, "support": {"noTickets": "No Tickets", "noTicketsDescription": "You don't have any tickets", "supportText": "Support", "ticketNumber": "Ticket Number", "contactSupport": "Contact Support", "messageStatus": "Message Status", "messageTitle": "Message Title", "messageDetails": "Message Details", "send": "send", "urgent": "<PERSON><PERSON>", "medium": "Medium", "normal": "Normal", "SentSuccessfully": "Your request has been sent successfully.", "willReply": "We will reply to you within 24 hours ", "next": "Next"}, "absence": {"absence": "Absence", "reject": "Reject", "accept": "Accept", "Pending": "Pending", "requestAbsence": "Request Absence", "haventSubmitted": "You Haven’t Submitted Any Absence Request Yet", "submitYourAbsenceRequest": "Submit Your Absence Request Now, And It Will Be Reviewed As Soon As Possible", "absencePending": "Absence Pending", "absenceType": "Absence Type", "normal": "Normal", "sick": "Sick", "meetingDate": "Meeting Date", "enteryourMeetingDate": "Enter Your Meeting Date", "image": "Image", "novalidattachmentfound": "No valid attachment found", "uploadPicture": "Upload Picture", "reason": "Reason", "enterTheReason": "Enter The Reason", "send": "Send", "selectAbsenceType": "Select Absence Type", "requestedOn": "Requested On", "enteryourRequestedOnDate": "Enter your Requested On Date ", "Permissionrequestpending": "Asking <PERSON><PERSON><PERSON>", "timefrom": "Time from", "timeon": "Time on", "timeto": "Time to", "date": "Date", "enterdate": "Enter date", "novalidattachment": "No valid attachment found.", "askingPermisson": "Asking <PERSON><PERSON>", "successfully!": "Successfully!", "yourabsence": "Your absence request has been sent", "youcanTrackyour": "You can track your absence request, and you will receive a response soon", "tracking": "Tracking", "sendanabsencerequest": "Send an absence request", "reasonOfRejection": "Reason of Rejection", "resend": "Resend", "rejection": "Rejection", "absenceRejection": "Absence Rejection", "absenceAccept": "Absence Accept", "askingPermissonRejection": "Asking <PERSON><PERSON><PERSON>", "askingPermissonAccept": "Asking <PERSON><PERSON><PERSON> Accept", "from": "From", "to": "To"}, "clientsDetails": {"accountDetails": "Account Details", "projects": "Projects", "delete": "Delete", "edit": "Edit", "email": "Email", "phone": "Phone", "gender": "Gender", "country": "Country", "companyName": "Company Name", "companyAddress": "Company Address", "officePhoneNumber": "Office Phone Number", "notes": "Notes", "companyLogo": "Company Logo", "noProjectsFound": "No Projects Found", "toAddAQuote": "To Add A Quote, Make Sure You’ve Created A Project First.", "toIssueAnInvoice": "To Issue An Invoice, You Need To Create A Project First.", "addProject": "Add Project", "projectsDetails": "Projects Details", "quote": "Quote", "invoice": "Invoice", "projectName": "Project Name", "dateofreceipt": "Date of receipt of the project", "projectclosing": "Project closing date", "download": "Download", "addQuote": "Add Quote", "addInvoice": "Add Invoice", "quoteName": "Quote Name", "enterQuoteName": "Enter Quote name", "upLoadPDF": "Upload PDF File", "noFileSelected": "No file selected", "quoteAddedSuccessfully": "Quote added successfully", "failedToAddQuote": "Failed to add quote", "enterNameAndSelect": "Please enter name and select a PDF file", "invoiceName": "Invoice Name", "enterInvoiceName": "Enter Invoice name", "quantity": "Quantity", "enterQuantity": "Enter Quantity", "price": "Price", "enterPrice": "Enter Price", "vat": "VAT", "enterVat": "Enter VAT (%)", "invoicetotal": "Invoice Total", "enterTotal": "Enter invoice total", "dueDate": "Due Date", "enterDueDate": "Enter due date (YYYY-MM-DD)", "clientVat": "Client VAT", "enterClientVat": "Enter client VAT", "total": "Total", "entertotal": "Enter total", "Status": "Status", "enterStatus": "Enter status", "invoiceAddedSuccessfully": "Invoice added successfully", "submitInvoice": "Submit Invoice", "projectCode": "Project Code*", "ProjectName": "Project Name*", "enterProjectName": "Enter project name", "StartDate": "Start Date*", "selectStartDate": "Please select Start Date", "Deadline": "Deadline*", "selectDeadline": "Please select Deadline", "project": "Project", "task": "Task", "details": "Details", "comment": "Comment", "status": "Status", "statusTask": "Status Task", "myStatus": "My Status"}, "lead": {"addLead": "Add Lead", "accountDetails": "Account Details", "compantDetails": "Company Details", "next": "Next", "add": "Add", "clientName": "Client Name*", "enterClientName": "Enter Client Name", "emailAddress": "Email Address*", "enterEmailAddress": "Enter Email Address Client", "gender": "Gender", "selectGender": "Select Gender", "phoneNumber": "Phone Number", "enterPhoneNumber": "Enter Phone Number", "country": "Country", "enterCountry": "Enter Country", "companyName": "Company Name", "enterCompanyName": "Enter Company Name", "companyAddress": "Company Address", "enterCompanyAddress": "Enter Company Address", "officePhoneNumber": "Office Phone Number", "enterOfficePhoneNumber": "Enter Office Phone Number", "notes": "Notes", "enterNotes": "Enter Notes", "companyLogo": "Company Logo", "uploadPicture": "Upload Picture", "projects": "Projects", "delete": "Delete", "edit": "Edit", "email": "Email", "phone": "Phone Number", "changeToClient": "Change To Client", "addProject": "Add Project"}, "profile": {"profile": "Profile", "jobTitle": "Job Title", "phonenumber": "Phone Number", "email": "Email", "baseSalary": "Base Salary", "address": "Address", "employmenthistory": "Employment History", "contracttype": "Contract Type", "workinghours": "Working Hours", "editPassword": "Edit password", "newPassword": "New password", "oldPassword": "Old password", "confirmNewPassword": "Confirm New password", "forgetYourPassword": "Forgot your password ?", "editProfile": "Edit Profile", "changePassword": "Change Password", "favorite": "Favorites", "support": "Support", "termsAndCondation": "Terms and Conditions", "usagePolicy": "Usage Policy", "logout": "Logout", "deleteAccount": "Delete Account", "fullName": "Full Name", "city": "City", "update": "Update", "changeimage": "Change Image", "pickfromdevice": "Pick From Device", "pickfromCamera": "Pick From Camera"}, "filter": {"filterAndSelect": "Filter and select", "price": "Price", "who": "From", "to": "To", "evaluation": "Rating", "sortByMostToLeastRated": "Sort by highest to lowest rating", "theBrand": "Brand", "allBrands": "All brands", "filtering": "Filtering", "theOffer": "Offer", "riyal": "Riyal"}, "home": {"goodMorning": "Good morning", "Statistics": "Statistics", "clockingIN": "Clocking-In", "clockingOUT": "Clocking-Out", "clockingSuccessfully": "Clocking in successfully", "clockingoutSuccessfully": "Clocking out successfully", "bye": "Bye, See You Tomorrow"}, "homeHr": {"statisticsoftheyears": "Statistics of the years", "absence": "Absence"}, "homeEmployee": {"statisticsoftheyears": "Statistics of the years", "absence": "Absence", "projects": "Projects", "seeMore": "See More", "employeePolicies&Requests": "Employee Policies & Requests", "askingpermission": "Asking permission", "rewards": "Rewards", "noprojectsavailable": "No projects available", "projectName": "Project Name", "dateofreceiptoftheproject": "Date of receipt of the project", "projectclosingdate": "Project closing date", "tasks": "Tasks", "projectTasks": "Project Tasks", "addMeeting": "Add Meeting", "meetingName": "Meeting Name", "enteryourMeetingName": "Enter your Meeting Name", "meetingDate": "Meeting Date", "enteryourMeetingDate": "Enter your Meeting Date", "meetingTime": "Meeting Time", "enteryourMeetingTime": "Enter your Meeting Time", "description": "Description", "enterTheDescription": "Enter The Description", "add": "Add", "allMettings": "All Meetings", "meetingStartDateTime": "Meeting Start Date & Time", "selectStartDateTime": "Select start date and time", "meetingEndDateTime": "Meeting End Date & Time", "selectEndDateTime": "Select end date and time", "attendees": "Attendees", "enterAttendeeEmail": "Enter attendee's email", "noMeetings": "No meetings found"}, "leads": {"noLeadsFound": "No Leads Found", "addLead": "Add Lead"}, "clients": {"clients": "Clients", "noClientsFound": "No Clients Found"}, "mainLayout": {"home": "Home", "more": "More", "leads": "Leads", "clients": "Clients"}, "mainLayoutHr": {"absence": "Absence", "askingpermission": "Asking permission"}, "mainLayoutEmployee": {"projects": "Projects", "calender": "<PERSON><PERSON>"}, "moreEmployee": {"teamAbsence": "Team Absence", "teamAskingpermisson": "Team Asking permisson"}, "askingPermission": {"askingpermission": "Asking permission", "sendanAskingpermissionrequest": "Send an Asking permission request", "rejectReason": "Reject Reason", "reson": "<PERSON><PERSON>", "enterTheReason": "Enter The Reason", "Send": "Send", "pleaseenterthereason": "Please enter the reason"}, "moreSales": {"contracts": "Contracts", "invoices": "Invoices", "absence": "Absence", "askingpermisson": "Permission Request", "darkmode": "Dark Mode", "notEffective": "Not Effective", "language": "Language", "Companypolicies": "Company Policies", "tickets": "Tickets", "changePassword": "Change Password", "logout": "Logout", "askingpermission": "Asking permission", "reject": "Reject", "approve": "Approve", "accept": "Accept", "Pending": "Pending", "youhavensubmittedany": "You haven't submitted any permission request yet", "submityourpermission": "Submit your permission request now, and it will be reviewed and responded to shortly", "requestAskingpermission": "Request Asking permission", "companyProfile": "Company Profile", "changepassword": "Change Password", "oldPassword": "Old Password", "enterOldPassword": "Enter Your Old Password", "newPassword": "New Password", "enterNewPassword": "Enter Your New Password", "confirmPassword": "Confirm Password", "enterConfirmPassword": "Enter Your Confirm Password", "forgotPassword": "Forgot password?", "next": "Next", "update": "Update", "allemployees": "All employees", "alldepartments": "All departments", "failedtoloademployees": "Failed to load employees.", "noInvoices": "No invoices available", "dueDate": "Due Date", "status": "Status", "currency": "SAR", "confirmed": "Confirmed", "pending": "Pending"}, "allDepartmentsHr": {"alldepartments": "All departments", "viewAll": "View All"}, "notification": {"notification": " Notifications", "day": "Today", "aWeekAgo": "A week ago"}, "favorite": {"favorite": "Favorites"}, "address": {"address": "Address", "addNewAddress": "Add New Address", "addAddress": "Add Address", "saveAddress": "Save Address", "fullName": "Full Name", "phoneNumber": "Phone Number", "city": "City", "appartmentNumber": "Apartment Number", "buildingNumber": "Building Number", "notes": "Notes", "update": "Update Address"}, "good": "Good", "morning": "Morning", "evening": "Evening", "am": "AM", "pm": "PM", "no_check_in": "You haven't checked in yet", "check_in": "Check In", "check_out": "Check Out", "attendance": "Attendance", "bye_see_you_tomorrow": "Bye, See You Tomorrow", "location_disabled": "Location is disabled", "enable_location_services_description": "Please enable location services to use this feature.", "location_features_explanation": "We use your location to confirm attendance and improve service accuracy.", "enable_location": "Enable Location", "pleaseaddQuotesToChaneTclient": "Please add quotes to change the client", "time": "TIME", "from": "From", "to": "To", "description": "DESCRIPTION", "organizer": "ORGANIZER", "meeting_link": "MEETING LINK", "attendees": "ATTENDEES ({0})", "join_meeting": "Join Meeting"}