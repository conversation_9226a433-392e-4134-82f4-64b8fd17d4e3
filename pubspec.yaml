name: erp
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  cached_network_image: ^3.4.1
  carousel_slider: ^5.0.0
  change_app_package_name: ^1.5.0
  cupertino_icons: ^1.0.8
  dio: ^5.7.0
  dots_indicator: ^3.0.0
  easy_localization: ^3.0.7
  file_picker: ^9.0.0
  firebase_core: ^3.15.1
  firebase_crashlytics: ^4.3.9
  firebase_messaging: ^15.2.9
  flutter:
    sdk: flutter
  flutter_bloc: ^9.0.0
  flutter_easyloading: ^3.0.5
  flutter_launcher_icons: ^0.14.4
  flutter_local_notifications: ^19.3.0
  flutter_phoenix: ^1.1.1
  flutter_screenutil: ^5.9.3
  flutter_secure_storage: ^9.2.4
  flutter_svg: ^2.0.17
  fluttertoast: ^8.2.10
  freezed: ^2.5.8
  freezed_annotation: ^2.4.4
  get_it: ^8.0.3
  google_fonts: ^6.2.1
  image_picker: ^1.1.2
  json_annotation: ^4.9.0
  loading_indicator: ^3.1.1
  lottie: ^3.3.1
  page_transition: ^2.2.1
  path_provider: ^2.1.5
  permission_handler: ^11.4.0
  pinput: ^5.0.1
  pretty_dio_logger: ^1.4.0
  shared_preferences: ^2.3.5
  skeletonizer: ^1.4.3 
  smooth_page_indicator: ^1.2.0+3
  table_calendar: null
  url_launcher: ^6.3.1
dev_dependencies:
  build_runner: ^2.4.14
  flutter_lints: ^5.0.0
  flutter_test:
    sdk: flutter

  # Location
  geolocator: ^10.0.0
  json_serializable: ^6.9.3

flutter:

  uses-material-design: true
  assets:
    - assets/images/pngs/
    - assets/images/svgs/
    - assets/languages/

  fonts:
    # FontWight
    - family: DINNextLTArabic
      fonts:
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-2.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-3.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Black-4.ttf
          weight: 900
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-2.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-3.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Bold-4.ttf
          weight: 700
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy2-1.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy2-2.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Heavy-1.ttf
          weight: 800
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTARABIC-LIGHT-1.ttf
          weight: 300
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-2.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-3.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Medium-4.ttf
          weight: 500
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-2.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-3.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-Regular-4.ttf
          weight: 400
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-1.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-2.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-3.ttf
          weight: 200
        - asset: assets/fonts/ar/ArbFONTS-DINNextLTArabic-UltraLight-4.ttf
          weight: 200
flutter_assets:
  assets_path: assets/images/
  output_path: lib/core/themes/
  filename: assets.dart

# use this command to run the package >> flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/pngs/app_icon.png"
  adaptive_icon_background: "#2aa69c"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/images/pngs/app_icon.png"
    background_color: "#2aa69c"
    theme_color: "#2aa69c"
  windows:
    generate: true
    image_path: "assets/images/pngs/app_icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/images/pngs/app_icon.png"
