import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/features/auth/data/api_services/api_services.dart';
import 'package:erp/features/auth/data/repos/auth_repo.dart';
import 'package:erp/features/chat/data/api_services/api_services.dart';
import 'package:erp/features/chat/data/repo/chat_repo.dart';
import 'package:erp/features/check%20in%20&%20checkOut/data/api%20services/check_in_api_services.dart';
import 'package:erp/features/check%20in%20&%20checkOut/data/repo/check_in_repo.dart';
import 'package:erp/features/emplyee%20flow/absence/data/api_services/api_services.dart';
import 'package:erp/features/emplyee%20flow/absence/data/repo/leave_repo.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/api_services/api_services.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/repo/permison_repo.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/data/api%20servies/company_profile_api_seriveces.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/data/repo/company_profile_repo.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/api%20services/home_api_services.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/repo/home_repo.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/api_services/meetings_api_services.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/repo/all_meetings_repo.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/api%20services/projects_details_api_services.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/repo/projects_repo.dart';
import 'package:erp/features/emplyee%20flow/projects/data/api%20services/projects_api_services.dart';
import 'package:erp/features/emplyee%20flow/projects/data/repo/projects_repo.dart';
import 'package:erp/features/emplyee%20flow/support/data/api_services/api_services.dart';
import 'package:erp/features/emplyee%20flow/support/data/repos/support_repo.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/api%20services/task_details_api_services.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/repo/task_details_repo.dart';
import 'package:erp/features/hr/absence/data/api_services/hr_absence_api_services.dart';
import 'package:erp/features/hr/absence/data/repo/hr_absence_repo.dart';
import 'package:erp/features/hr/all%20department/data/api_services/api_services.dart';
import 'package:erp/features/hr/all%20department/data/repos/department_repo.dart';
import 'package:erp/features/hr/all%20empolyee/data/api_services/api_services.dart';
import 'package:erp/features/hr/all%20empolyee/data/repos/all_emplyee_repo.dart';
import 'package:erp/features/hr/asking_permission/data/api_services/hr_api_services.dart';
import 'package:erp/features/hr/asking_permission/data/repo/hr_permison_repo.dart';
import 'package:erp/features/profile/data/api_services/api_services.dart';
import 'package:erp/features/profile/data/repos/profile_repo.dart';
import 'package:erp/features/sales%20flow/add%20lead/data/api%20services/create_lead_api_services.dart';
import 'package:erp/features/sales%20flow/add%20lead/data/repo/create_lead_repo.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/api%20services/all_invoice_api_services.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/repo/all_invoice_repo.dart';
import 'package:erp/features/sales%20flow/home/<USER>/api%20services/sales_api_services.dart';
import 'package:erp/features/sales%20flow/home/<USER>/repo/sales_repo.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/api%20services/lead_details_api_services.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/repo/lead_details_repo.dart';
import 'package:erp/features/sales%20flow/leads/data/api%20services/lead_api_services.dart';
import 'package:erp/features/sales%20flow/leads/data/repo/leads_repo.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/data/api_services/api_services.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/data/repo/create_projects_sales_repo.dart';
import 'package:get_it/get_it.dart';

final getIt = GetIt.instance;

Future<void> setupDependencyInjection() async {
  getIt.registerLazySingleton<DioHelper>(() => DioHelper());

  getIt.registerLazySingleton<AuthApiServices>(() => AuthApiServices(getIt()));
  getIt.registerLazySingleton<AuthRepository>(() => AuthRepository(getIt()));
  getIt
      .registerLazySingleton<LeaveApiServices>(() => LeaveApiServices(getIt()));
  getIt.registerLazySingleton<LeaveRepository>(() => LeaveRepository(getIt()));
  getIt.registerLazySingleton<AskingPermsionApiServices>(
      () => AskingPermsionApiServices(getIt()));
  getIt.registerLazySingleton<AskingPermsionRepository>(
      () => AskingPermsionRepository(getIt()));

  getIt.registerLazySingleton<HomeApiServices>(() => HomeApiServices(getIt()));
  getIt.registerLazySingleton<HomeRepository>(() => HomeRepository(getIt()));

  getIt.registerLazySingleton<ProfileApiServices>(
      () => ProfileApiServices(getIt()));
  getIt.registerLazySingleton<ProfileRepo>(() => ProfileRepo(getIt()));

  getIt.registerLazySingleton<DepartmentApiServices>(
      () => DepartmentApiServices(getIt()));
  getIt.registerLazySingleton<DepartmentRepo>(() => DepartmentRepo(getIt()));

  getIt.registerLazySingleton<ProjectsApiServices>(
      () => ProjectsApiServices(getIt()));
  getIt.registerLazySingleton<ProjectsRepository>(
      () => ProjectsRepository(getIt()));
  getIt.registerLazySingleton<ProjectsDetailsRepository>(
      () => ProjectsDetailsRepository(getIt()));
  getIt.registerLazySingleton<ProjectsDetailsApiServices>(
      () => ProjectsDetailsApiServices(getIt()));
  getIt.registerLazySingleton<TaskDetailsApiServices>(
      () => TaskDetailsApiServices(getIt()));
  getIt.registerLazySingleton<TaskDetailsRepository>(
      () => TaskDetailsRepository(getIt()));

  getIt.registerLazySingleton<SupportApiServices>(
      () => SupportApiServices(getIt()));
  getIt.registerLazySingleton<SupportRepository>(
      () => SupportRepository(getIt()));

  getIt.registerLazySingleton<ChatRepo>(() => ChatRepo(getIt()));
  getIt.registerLazySingleton<ChatApiServices>(() => ChatApiServices(getIt()));

  getIt.registerLazySingleton<CreateLeadRepo>(() => CreateLeadRepo(getIt()));
  getIt.registerLazySingleton<CreateLeadApiServices>(
      () => CreateLeadApiServices(getIt()));
  getIt.registerLazySingleton<LeadDetailsApiServices>(
      () => LeadDetailsApiServices(getIt()));
  getIt.registerLazySingleton<LeadRepo>(() => LeadRepo(getIt()));
  getIt.registerLazySingleton<LeadApiServices>(() => LeadApiServices(getIt()));
  getIt.registerLazySingleton<LeadDetailsRepo>(() => LeadDetailsRepo(getIt()));

  getIt.registerLazySingleton<HrPermisonRepo>(() => HrPermisonRepo(getIt()));
  getIt.registerLazySingleton<HRAskingPermsionApiServices>(
      () => HRAskingPermsionApiServices(getIt()));

  getIt.registerLazySingleton<HrAbsneceRepo>(() => HrAbsneceRepo(getIt()));
  getIt.registerLazySingleton<HRAskingAbsneceApiServices>(
      () => HRAskingAbsneceApiServices(getIt()));
  getIt.registerLazySingleton<AllEmpleyeeApiServices>(
      () => AllEmpleyeeApiServices(getIt()));
  getIt.registerLazySingleton<AllEmplyeeRepo>(() => AllEmplyeeRepo(getIt()));

  getIt.registerLazySingleton<CompanyProfileRepository>(
      () => CompanyProfileRepository(getIt()));
  getIt.registerLazySingleton<CompanyProfileApiService>(
      () => CompanyProfileApiService(getIt()));
  getIt.registerLazySingleton<SalesRepo>(() => SalesRepo(getIt()));
  getIt
      .registerLazySingleton<SalesApiServices>(() => SalesApiServices(getIt()));
  getIt.registerLazySingleton<ProjectsApiSalesServices>(
      () => ProjectsApiSalesServices(getIt()));
  getIt.registerLazySingleton<ProjectsSalesRepo>(
      () => ProjectsSalesRepo(getIt()));
  getIt.registerLazySingleton<CheckInRepo>(() => CheckInRepo(getIt()));
  getIt.registerLazySingleton<CheckInApiServices>(
      () => CheckInApiServices(getIt()));
  getIt.registerLazySingleton<AllInvoiceRepo>(() => AllInvoiceRepo(getIt()));
  getIt.registerLazySingleton<AllInvoiceApiServices>(
      () => AllInvoiceApiServices(getIt()));

  getIt.registerLazySingleton<AllMeetingRepo>(() => AllMeetingRepo(getIt()));
  getIt.registerLazySingleton<AllMeatingsApiServices>(
      () => AllMeatingsApiServices(getIt()));
}
