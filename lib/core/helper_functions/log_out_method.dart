import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/utils/app_constants.dart';

void logoutMethod() async {
  await CacheHelper.clearAllSecuredData();
  // await CacheHelper.clearAllData();
  await CacheHelper.removeData(key: CacheKeys.role);
  await CacheHelper.removeData(key: CacheKeys.isTeamLead);
  await CacheHelper.removeData(key: CacheKeys.userId);
  await CacheHelper.removeData(key: CacheKeys.userName);
  await CacheHelper.removeData(key: CacheKeys.userImage);

  AppConstants.userToken = null;
  AppConstants.navigatorKey.currentContext
      ?.pushNamedAndRemoveUntil(Routes.loginScreen);
}
