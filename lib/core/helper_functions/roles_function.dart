import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/utils/app_constants.dart';

void navigateBasedOnRole() {
  if (AppConstants.userToken == null) {
    if (CacheHelper.getData(key: CacheKeys.isFirstOpen) == true) {
      AppConstants.navigatorKey.currentContext
          ?.pushNamedAndRemoveUntil(Routes.loginScreen);
    } else {
      AppConstants.navigatorKey.currentContext
          ?.pushNamedAndRemoveUntil(Routes.onBoardingScreen);
    }
  } else {
    final role = CacheHelper.getData(key: CacheKeys.role);
    switch (role) {
      case 'employee':
        AppConstants.navigatorKey.currentContext
            ?.pushNamedAndRemoveUntil(Routes.emplyessMainlayoutScreen);
        break;
      case 'hr':
        AppConstants.navigatorKey.currentContext
            ?.pushNamedAndRemoveUntil(Routes.hrMainlayoutScreen);
        break;
      case 'sales':
        AppConstants.navigatorKey.currentContext
            ?.pushNamedAndRemoveUntil(Routes.salesMainlayoutScreen);
        break;
      case 'noRole':
        AppConstants.navigatorKey.currentContext
            ?.pushNamedAndRemoveUntil(Routes.noRoleScreen);
        break;
      default:
    }
  }
}
