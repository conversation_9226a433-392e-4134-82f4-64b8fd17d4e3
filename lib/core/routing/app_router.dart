import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/features/admin%20flow/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:erp/features/admin%20flow/main%20layout/presentation/main_layout_admin.dart';
import 'package:erp/features/auth/business_logic/auth_cubit.dart';
import 'package:erp/features/auth/presentation/screens/create_new_password_screen.dart';
import 'package:erp/features/auth/presentation/screens/forgot_password_screen.dart';
import 'package:erp/features/auth/presentation/screens/login_screen.dart';
import 'package:erp/features/auth/presentation/screens/otp_forgot_password_screen.dart';
import 'package:erp/features/chat/bloc/cubit/chat_cubit.dart';
import 'package:erp/features/chat/presentation/screens/chat_screen.dart';
import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/data/model/leave_request.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/screens/absence_rejected_or_accept_screen.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/screens/absence_screen.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/model/request_model.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/screens/asking_permisson_rejected_or_accept_screen.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/screens/asking_permisson_screen.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/screens/request_asking_permisson_screen.dart';
import 'package:erp/features/emplyee%20flow/calendar/bloc/cubit/calendar_cubit.dart';
import 'package:erp/features/emplyee%20flow/calendar/calander/screen/calendar_screen.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/bloc/cubit/company_profile_cubit.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/presentation/screen/company_profile.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/cubit/home_cubit.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/screens/home_screen.dart';
import 'package:erp/features/emplyee%20flow/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:erp/features/emplyee%20flow/main%20layout/presentation/main_layout.dart';
import 'package:erp/features/emplyee%20flow/mettings/bloc/cubit/all_meetings_cubit.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/model/all_meetings_model.dart';
import 'package:erp/features/emplyee%20flow/mettings/presentation/screen/add_meetings_screen.dart';
import 'package:erp/features/emplyee%20flow/mettings/presentation/screen/meeting_details.dart';
import 'package:erp/features/emplyee%20flow/mettings/presentation/screen/mettings_screen.dart';
import 'package:erp/features/emplyee%20flow/more/presentation/screens/more_screen.dart';
import 'package:erp/features/emplyee%20flow/project%20details/bloc/cubit/projects_details_cubit.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/screen/project_details_screen.dart';
import 'package:erp/features/emplyee%20flow/projects/bloc/cubit/projects_cubit.dart';
import 'package:erp/features/emplyee%20flow/projects/presentation/screen/projects_screen.dart';
import 'package:erp/features/emplyee%20flow/support/business_logic/support_cubit.dart';
import 'package:erp/features/emplyee%20flow/support/presentation/screens/contact_support.dart';
import 'package:erp/features/emplyee%20flow/support/presentation/screens/support_screen.dart';
import 'package:erp/features/emplyee%20flow/task%20details/bloc/cubit/task_details_cubit.dart';
import 'package:erp/features/emplyee%20flow/task%20details/presentation/screen/task_details_screen.dart';
import 'package:erp/features/emplyee%20flow/tasks/presentation/screen/tasks_screen.dart';
import 'package:erp/features/hr/absence/bloc/cubit/hr_absence_cubit.dart';
import 'package:erp/features/hr/absence/data/model/hr_leave_model.dart';
import 'package:erp/features/hr/absence/presentation/screen/hr_absence_details.dart';
import 'package:erp/features/hr/absence/presentation/screen/hr_absences_screen.dart';
import 'package:erp/features/hr/absence/presentation/screen/send_absence_reason_screen.dart';
import 'package:erp/features/hr/all%20department/business_logic/cubit/department_cubit.dart';
import 'package:erp/features/hr/all%20department/presentation/screens/department_all_emplyee_screen.dart';
import 'package:erp/features/hr/all%20department/presentation/screens/hr_all_department_screen.dart';
import 'package:erp/features/hr/all%20empolyee/cubit/all_emplyee_cubit.dart';
import 'package:erp/features/hr/all%20empolyee/presentation/hr_all_empolyee_screen.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_cubit.dart';
import 'package:erp/features/hr/asking_permission/data/model/permission_request.dart';
import 'package:erp/features/hr/asking_permission/presentation/screens/hr_asking_permissions_screen.dart';
import 'package:erp/features/hr/asking_permission/presentation/screens/hr_asking_permsion_details.dart';
import 'package:erp/features/hr/asking_permission/presentation/screens/reason_asking_permisson.dart';
import 'package:erp/features/hr/hr%20home/hr_home.dart';
import 'package:erp/features/hr/hr_more/presentation/screens/hr_more_screen.dart';
import 'package:erp/features/hr/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:erp/features/hr/main%20layout/presentation/hr_main_layout.dart';
import 'package:erp/features/norole/no_role.dart';
import 'package:erp/features/notification/presentation/screen/notifcation_screen.dart';
import 'package:erp/features/onBoarding/Bloc/on_boarding_cubit.dart';
import 'package:erp/features/onBoarding/screens/on_boarding_screen.dart';
import 'package:erp/features/profile/bloc/cubit/profile_cubit.dart';
import 'package:erp/features/profile/presentaiton/screen/change_password_screen.dart';
import 'package:erp/features/profile/presentaiton/screen/profile_screen.dart';
import 'package:erp/features/sales%20flow/add%20lead/bloc/cubit/add_lead_cubit.dart';
import 'package:erp/features/sales%20flow/add%20lead/presentation/screens/add_lead_screen.dart';
import 'package:erp/features/sales%20flow/all%20invoice/bloc/cubit/all_invoice_cubit.dart';
import 'package:erp/features/sales%20flow/all%20invoice/presentation/screen/all_invoice_screen.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_cubit.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/screens/add_projects_client_screen.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/screens/client_deatils_screen.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/screens/edit_client_screen.dart';
import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_cubit.dart';
import 'package:erp/features/sales%20flow/clients/presention/screen/client_screen.dart';
import 'package:erp/features/sales%20flow/home/<USER>/cubit/sales_home_cubit.dart';
import 'package:erp/features/sales%20flow/home/<USER>/screens/sales_home_screen.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/screens/add_project_screen.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/screens/edit_lead_screen.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/screens/lead_deatils_screen.dart';
import 'package:erp/features/sales%20flow/leads/bloc/cubit/lead_cubit.dart';
import 'package:erp/features/sales%20flow/leads/presention/screen/lead_screen.dart';
import 'package:erp/features/sales%20flow/main%20layout/business_logic/sales_main_layout_cubit.dart';
import 'package:erp/features/sales%20flow/main%20layout/presentation/sales_main_layout.dart';
import 'package:erp/features/sales%20flow/more/presentation/sales_more_screen.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/screen/add_invoice_screen.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/screen/add_quote_screen.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/screen/sales_project_details.dart';
import 'package:erp/features/splash/business_logic/splash_cubit.dart';
import 'package:erp/features/splash/presentation/screens/splash_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:page_transition/page_transition.dart';

class AppRouter {
  Route? generateRoute(RouteSettings settings) {
    PageTransition transition<T extends Cubit<Object>>({
      required Widget screen,
      T? cubit,
      Object? arguments,
      PageTransitionType type = PageTransitionType.fade,
      Duration duration = const Duration(milliseconds: 200),
      Alignment alignment = Alignment.center,
    }) {
      final child = cubit != null
          ? BlocProvider<T>(
              create: (context) => cubit,
              child: screen,
            )
          : screen;

      return PageTransition(
        child: child,
        type: type,
        duration: duration,
        alignment: alignment,
        settings: settings,
      );
    }

    switch (settings.name) {
      case Routes.splashScreen:
        return transition(
            screen: const SplashScreen(), cubit: SplashCubit()..startAppFlow());
      case Routes.onBoardingScreen:
        return transition(
            screen: const OnBoardingScreen(), cubit: OnBoardingCubit());
      case Routes.changePasswordScreen:
        return transition(
            screen: ChangePasswordScreen(), cubit: ProfileCubit(getIt()));
      case Routes.loginScreen:
        return transition(
            screen: const LoginScreen(), cubit: AuthCubit(getIt()));
      case Routes.forgotpasswordScreen:
        return transition(
          screen: const ForgotPasswordScreen(),
        );
      case Routes.createNewpasswordScreen:
        return transition(
          screen: const CreateNewPasswordScreen(),
          cubit: AuthCubit(getIt()),
        );
      case Routes.otpForgetPasswordScreen:
        return transition(
          screen: const OtpForgotPasswordScreen(),
          cubit: AuthCubit(getIt()),
        );

      case Routes.emplyessMainlayoutScreen:
        return transition(
          screen: MainLayoutScreen(),
          cubit: MainLayoutCubit(),
        );
      case Routes.addProjectScreen:
        final argument = settings.arguments as LeadDetailsCubit;
        return transition(
          screen: BlocProvider.value(
            value: argument,
            child: AddProjectLeadScreen(),
          ),
        );

      case Routes.salesMainlayoutScreen:
        return transition(
          screen: SalesMainLayoutScreen(),
          cubit: SalesMainLayoutCubit(),
        );
      case Routes.adminMainlayoutScreen:
        return transition(
          screen: MainLayoutAdminScreen(),
          cubit: MainLayoutAdminCubit(),
        );
      case Routes.chatScreen:
        final ticketId = settings.arguments as int;
        return transition(
          screen: BlocProvider(
            create: (context) => ChatCubit(getIt())
              ..scrollToBottom()
              ..getSpecificTicket(ticketId),
            child: const ChatScreen(),
          ),
        );
      case Routes.ticketsScreen:
        return transition(
          screen: BlocProvider(
            create: (context) => SupportCubit(getIt())..getAllTickets(),
            child: const SupportScreen(),
          ),
        );

      case Routes.projectDetailsScreen:
        final id = settings.arguments as int;

        return transition(
            screen: ProjectDetailsScreen(),
            cubit: ProjectsDetailsCubit(getIt())..getProjects(id: id));

      case Routes.taskDetailsScreen:
        final id = settings.arguments as int;
        return transition(
            cubit: TaskDetailsCubit(getIt())..getTaskDetails(id: id),
            screen: TaskDetailsScreen());
      case Routes.allTasksScreen:
        final List<Task> tasks = settings.arguments as List<Task>;

        return transition(screen: AllTasksScreen(tasks: tasks));
      case Routes.notificationScreen:
        return transition(screen: NotificationScreen());

      case Routes.allmettingsScreen:
        return transition(
            screen: AllMettingsScreen(),
            cubit: AllMeetingsCubit(getIt())..getAllMeetings());

      case Routes.addMeetingScreen:
        final argument = settings.arguments as AllMeetingsCubit;
        return transition(
          screen: BlocProvider.value(
            value: argument,
            // create: (context) => SubjectBloc(),
            child: AddMeetingsScreen(),
          ),
        );
      case Routes.absenceScreen:
        return transition(
            screen: AbsenceScreen(), cubit: LeaveCubit(getIt())..getLeaves());

      case Routes.absenceRejectionOrAcceptingScreen:
        final model = settings.arguments as LeaveRequest;

        return transition(
            screen: AbsenceRejectionOrAcceptingScreen(
              isReject: model.status != "approved" ? false : true,
              model: model,
            ),
            cubit: LeaveCubit(getIt()));

      case Routes.askingPermissionScreen:
        return transition(
            screen: AskingPermissonScreen(),
            cubit: AskingPermissonCubit(getIt())..getAskingPermsions());
      case Routes.requestPermissionScreen:
        return transition(screen: RequestPermissionScreen());
      case Routes.mettingDetails:
        final meeting = settings.arguments as Meeting;
        return transition(screen: MeetingDetailsScreen(meeting: meeting));
      case Routes.noRoleScreen:
        return transition(screen: NoRoleScreen());
      case Routes.companyProfile:
        return transition(
            screen: CompanyProfiles(),
            cubit: CompanyProfileCubit(getIt())..getCompanyProfiles());
      case Routes.permissionRejectionOrAcceptingScreen:
        final model = settings.arguments as Request;
        return transition(
            cubit: AskingPermissonCubit(getIt()),
            screen: PermissionRejectionOrAcceptScreen(
              isReject: model.status != "approved" ? false : true,
              model: model,
            ));
      case Routes.profileScreen:
        final arguments = settings.arguments as ProfileCubit;
        return transition(
          screen: BlocProvider.value(
            value: arguments,
            child: ProfileScreen(),
          ),
        );

      case Routes.salesProjectDetails:
        final id = settings.arguments as int;
        return transition(
            screen: SalesProjectDetails(),
            cubit: SalesProjectDetailsCubit(getIt())..getShowProject(id));
      case Routes.hrReasonsendAskingPermission:
        final model = settings.arguments as HrAskingPermissionArguments;
        return transition(
          screen: BlocProvider.value(
            value: model.cubit,
            child: ReasonAskingPermissonHrScreen(model: model.model),
          ),
        );

      case Routes.sendAbsencePermission:
        final model = settings.arguments as HrAbsenceArguments;
        return transition(
          screen: BlocProvider.value(
            value: model.cubit,
            child: SendAbsencePermissionScreen(model: model.model),
          ),
        );

      case Routes.addLeadScreen:
        return transition(
            screen: AddLeadScreen(), cubit: AddLeadCubit(getIt()));
      case Routes.leadDetails:
        final leadId = settings.arguments as int;
        return transition(
            screen: LeadDeatilsScreen(),
            cubit: LeadDetailsCubit(getIt())..getLeadDetails(leadId: leadId));
      case Routes.hrAskingPermsionDetails:
        final model = settings.arguments as HrAskingPermissionArguments;
        return transition(
            screen: BlocProvider.value(
          value: model.cubit,
          child: HrAskingPermsionDetails(model: model.model),
        ));

      case Routes.addQuoteScreen:
        final model = settings.arguments as SalesProjectDetailsCubit;
        return transition(
            screen: BlocProvider.value(
          value: model,
          child: AddQuoteScreen(),
        ));

      case Routes.addInvoiceScreen:
        final model = settings.arguments as SalesProjectDetailsCubit;
        return transition(
            screen: BlocProvider.value(
          value: model,
          child: AddInvoiceScreen(),
        ));
      case Routes.hrAbsenceDetails:
        final model = settings.arguments as HrAbsenceArguments;
        return transition(
            screen: BlocProvider.value(
          value: model.cubit,
          child: HrAbsenceDetails(model: model.model),
        ));

      case Routes.hrMainlayoutScreen:
        return transition(
            screen: MainLayoutHrScreen(), cubit: MainLayoutHrCubit());

      case Routes.hrAllEmployeeScreen:
        return transition(
            screen: HrAllEmpolyeeScreen(),
            cubit: AllEmplyeeCubit(getIt())
              ..getAllEmployees()
              ..setupAllEmployeeScrollController());

      case Routes.hrAllDepartmentScreen:
        return transition(
            screen: HrAllDepartmentScreen(),
            cubit: DepartmentCubit(getIt())
              ..setupDepartmentScrollController()
              ..getAllDepartments());
      case Routes.departmentEmplyeesScreen:
        final data = settings.arguments as DepartMentAllEmplyee;
        return transition(
            screen: AllDepartmentEmployeesScreen(
              departmentName: data.name,
            ),
            cubit: DepartmentCubit(getIt())
              ..setupEmployeeScrollController()
              ..getEmployeesForDepartment(data.id));

      case Routes.editLeadScreen:
        final cubit = settings.arguments as LeadDetailsCubit;
        return transition(
            screen: BlocProvider.value(
          value: cubit..fillControllers(),
          child: EditLeadScreen(),
        ));

      case Routes.editClientScreen:
        final cubit = settings.arguments as ClientDetailsCubit;
        return transition(
            screen: BlocProvider.value(
          value: cubit..fillControllers(),
          child: EditClientScreen(),
        ));
      case Routes.clientDetails:
        final leadId = settings.arguments as int;
        return transition(
            screen: ClientDeatilsScreen(),
            cubit: ClientDetailsCubit(getIt())..getLeadDetails(leadId: leadId));
      case Routes.hrAskingPermissionScreen:
        return transition(
          screen: HrAskingPermissionsScreen(),
          cubit: HRAskingPermissonCubit(getIt())
            ..getPermissionRequests()
            ..setupScrollController(),
        );

      case Routes.hrAbsenceScreen:
        return transition(
          screen: HrAbsencesScreen(),
          cubit: HRAbsneceCubit(getIt())
            ..hrGetLeaves()
            ..setupScrollController(),
        );
      case Routes.contactSupportScreen:
        return transition(
            screen: ContactSupportScreen(), cubit: SupportCubit(getIt()));
      case Routes.addClientProjectScreen:
        final cubit = settings.arguments as ClientDetailsCubit;
        return transition(
            screen: BlocProvider.value(
          value: cubit,
          child: AddClientProjectScreen(),
        ));
      case Routes.allInvoiceScreen:
        return transition(
            screen: AllInvoiceScreen(),
            cubit: AllInvoiceCubit(getIt())..getAllInvoice());
      default:
        return null;
    }
  }

  List<Widget> employeeScreen = [
    BlocProvider(
      create: (context) => HomeCubit(getIt())..getHome(),
      child: HomeScreen(),
    ),
    BlocProvider(
      create: (context) => ProjectsCubit(getIt())..getProjects(),
      child: ProjectsScreen(),
    ),
    BlocProvider(
      create: (context) => AllMeetingsCubit(getIt())..getAllMeetings(),
      child: AllMettingsScreen(),
    ),
    MoreScreen(),
  ];
  List<Widget> adminScreen = [
    BlocProvider(
      create: (context) => HomeCubit(getIt())..getHome(),
      child: HomeScreen(),
    ),
    BlocProvider(
      create: (context) => ProjectsCubit(getIt())..getProjects(),
      child: ProjectsScreen(),
    ),
    BlocProvider(
      create: (context) => CalendarCubit(),
      child: CalendarScreen(),
    ),
    MoreScreen(),
  ];
  List<Widget> salesScreens = [
    BlocProvider(
      create: (context) => SalesHomeCubit(getIt())..getSealesCount(),
      child: SalesHomeScreen(),
    ),
    BlocProvider(
      create: (context) => LeadsCubit(getIt())..getLeads(),
      child: LeadsScreen(),
    ),
    BlocProvider(
      create: (context) => ClientCubit(getIt())..getLeads(),
      child: ClientScreen(),
    ),
    SalesMoreScreen(),
  ];

  List<Widget> hrScreens = [
    BlocProvider(
      create: (context) => HomeCubit(getIt())..getHome(),
      child: HrHomeScreen(),
    ),
    BlocProvider(
      create: (context) => HRAbsneceCubit(getIt())
        ..hrGetLeaves()
        ..setupScrollController(),
      child: HrAbsencesScreen(),
    ),
    BlocProvider(
      create: (context) => HRAskingPermissonCubit(getIt())
        ..getPermissionRequests()
        ..setupScrollController(),
      child: HrAskingPermissionsScreen(),
    ),
    HrMoreScreen(),
  ];
}

class HrAskingPermissionArguments {
  final HRAskingPermissonCubit cubit;
  final PermissionRequest model;

  HrAskingPermissionArguments({required this.cubit, required this.model});
}

class HrAbsenceArguments {
  final HRAbsneceCubit cubit;
  final PermissionRequestItem model;

  HrAbsenceArguments({required this.cubit, required this.model});
}

class DepartMentAllEmplyee {
  final int id;
  final String name;

  DepartMentAllEmplyee({required this.id, required this.name});
}
