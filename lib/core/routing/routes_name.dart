class Routes {
  static const String splashScreen = 'splashScreen';
  static const String onBoardingScreen = 'on_boarding_screen';
  static const String loginScreen = 'login-screen';
  static const String forgotpasswordScreen = 'forgot-password-screen';

  static const String createNewpasswordScreen = 'create-new-password-screen';
  static const String otpForgetPasswordScreen = 'otp-forget-password-screen';
  static const String registerScreen = 'register-screen';
  static const String verifyOtpScreen = 'verify-otp_screen';
  static const String localizationScreen = 'localization_screen';
  static const String emplyessMainlayoutScreen = 'mainlayout-screen';
  static const String clientsMainlayoutScreen = 'clients-mainlayout-screen';
  static const String changePasswordScreen = 'change-password-screen';
  static const String projectDetailsScreen = 'project_details_screen';
  static const String allTasksScreen = 'tasks_screen';
  static const String taskDetailsScreen = 'task_details_screen';
  static const String notificationScreen = 'notifcation_screen';
  static const String allmettingsScreen = 'mettings_screen';
  static const String addMeetingScreen = 'add_meetings_screen';

    static const String mettingDetails = 'meeting_details';

  static const String absenceScreen = 'absence_screen';

  static const String addProjectScreen = 'add_project_screen';

  static const String absenceRejectionOrAcceptingScreen =
      'absence_rejected_or_accept_screen';
  static const String askingPermissionScreen = 'asking_permission_screen';
  static const String requestPermissionScreen = 'request_permission_screen';

  static const String permissionRejectionOrAcceptingScreen =
      'permission_rejected_or_accept_screen';
  static const String chatScreen = 'chat_screen';
  static const String ticketsScreen = 'tickets_screen';
  static const String companyProfile = 'company_profile';
  static const String salesMainlayoutScreen = 'sales_main_layout';
  static const String adminMainlayoutScreen = 'admin_main_layout';
  static const String profileScreen = 'profile_screen';

  static const String hrMainlayoutScreen = 'hr_main_layout';

  static const String addLeadScreen = 'add_lead_screen';
  static const String leadDetails = 'lead_deatils_screen';
  static const String hrAskingPermsionDetails = 'hr_asking_permsion_details';
  static const String hrReasonsendAskingPermission = 'send_asking_permission';
  static const String sendAbsencePermission = 'send_absence_permission';

  static const String hrAbsenceDetails = 'hr_absence_details';
  static const String hrAllEmployeeScreen = 'hr_all_empolyee_screen';
  static const String hrAllDepartmentScreen = 'hr_all_department_screen';
  static const String editLeadScreen = 'edit_lead_screen';
  static const String editClientScreen = 'edit_client_screen';
  static const String clientDetails = 'client_deatils_screen';
  static const String noRoleScreen = 'no_role';
  static const String departmentEmplyeesScreen =
      'department_all_emplyee_screen';

  static const String hrAskingPermissionScreen = 'hr_asking_permission_screen';
  static const String hrAbsenceScreen = 'hr_absence_screen';
  static const String contactSupportScreen = 'contact_support_screen';
  static const String clientProjectDetails = 'client_project_details_screen';
  static const String salesProjectDetails = 'sales_project_details_screen';
  static const String addQuoteScreen = 'add_quote_screen';
  static const String addInvoiceScreen = 'add_invoice_screen';
  static const String addClientProjectScreen = 'add_client_project_screen';
  static const String allInvoiceScreen = 'all_invoice_screen';
}
