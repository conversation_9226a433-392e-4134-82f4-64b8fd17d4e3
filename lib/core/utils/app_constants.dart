import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppConstants {
  static String? userToken;

  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  static int mainLayoutInitialScreenIndex = 0;

  static screenWidth(context) => MediaQuery.sizeOf(context).width;

  static screenHeight(context) => MediaQuery.sizeOf(context).height;

  static double borderRadius = 8.r;

  /// Build Error Text
  static String buildErrorText(String message) {
    return Text(
      message,
      style: Styles.contentEmphasis.copyWith(
        color: AppColors.redColor100,
      ),
    ).data!;
  }

  /// Product Images List in Search

  /// Product Images List in Search
}
