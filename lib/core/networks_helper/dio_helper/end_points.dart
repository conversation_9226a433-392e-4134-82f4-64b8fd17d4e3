class EndPoints {
  static const String baseUrl = 'https://backenderp.tanfeethi.com.sa/';

  static const String login = 'api/employee/auth/login';
  static const String getLeaveRequest = 'api/frontend/leaveRequest/list';
  static const String createLeaveRequest = 'api/frontend/leaveRequest/create';
  static const String updateLeaveRequest = "api/frontend/leaveRequest/update";

  static const String updatePassword = "api/employee/profile/updatePassword";

  static const String getAskingPermsionRequest =
      'api/frontend/permissionRequest/list';
  static const String createAskingPermsionRequest =
      'api/frontend/permissionRequest/create';
  static const String updateAskingPermsionRequest =
      "api/frontend/permissionRequest/update";
  static const String getHomeDays =
      "api/frontend/leaveRequest/getLeaveDaysSetting";
  static const String getProjects = "api/frontend/projects";
  static const String getTaskDetails = "api/frontend/tasks";
  static const String createTicket = 'api/frontend/clientTicket/create';

  static const String getAllDepartments = 'api/dashboard/departments/list';

  static const String getAllTickets = 'api/dashboard/clientTicket/list';
  static const String sendMessage = 'api/dashboard/clientTicket/createMessage';
  static String getSpecificTicket(int ticketId) =>
      'api/dashboard/clientTicket/show/$ticketId';
  static const String getLeads = 'api/dashboard/clients';
  //  ! hr
  static const String hrGetPermissonRequests =
      'api/dashboard/permissionRequest/list';

  static String hrUpdateStateus(int id) =>
      'api/dashboard/permissionRequest/updateStatus/$id';

  static const String hrGetLeaves = 'api/dashboard/leaveRequest/list';

  static String hrUpdateLeave(int id) =>
      'api/dashboard/leaveRequest/updateStatus/$id';

  static String getDepartmentEmplyee(int id) =>
      'api/dashboard/departments/$id/employees';

  static const String getAllEmployees = 'api/dashboard/employees/list';
  // static const String getProfile = 'api/dashboard/employees/list';

  static String getProfile = "api/employee/auth/profile";
  static String getCompanyProfiles = "api/frontend/company-profaile/list";
  static String salesHomeCount = "api/dashboard/clients/count/for_employee";
  static String getBannersHome = "api/frontend/events";

  /// projects///
  /// get list  projects
  static String getListProjects = "api/dashboard/projects";

  /// get show projects
  static String getShowProject(int id) => 'api/dashboard/projects/$id';

  /// post add quote
  static String addQuote = "api/dashboard/project/addQuote";

  /// get delete quote
  static String deleteQuote(int id) => 'api/dashboard/project/deleteQuote/$id';

  ///post add invoice
  static String addInvoice = "api/dashboard/project/addInvoice";

  /// post update invoice
  static String updateInvoice(int id) =>
      'api/dashboard/project/updateInvoice/$id';

  /// get delete invoice
  static String deleteInvoice(int id) =>
      'api/dashboard/project/deleteInvoice/$id';

  /// post create project
  static String createProject = "api/dashboard/projects";

  /// post update project
  static String updateProject(int id) => 'api/dashboard/projects/$id';

  /// post destoy project
  static String deleteProject(int id) => 'api/dashboard/clients/$id';

  static String allInvoiceList = 'api/dashboard/project/listInvoice';

  static const String checkInEP = 'api/frontend/attendances/check-in';
  static const String checkOutEP = 'api/frontend/attendances/check-out';
  static const String getAllMeetings = 'api/frontend/meeting/list';
  static const String createMeeting = 'api/frontend/meeting/create';
}
