import 'dart:async';

import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/log_util.dart';
import 'package:erp/core/fcm.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/utils/bloc_observer.dart';
import 'package:erp/erp.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await setupDependencyInjection();
  await DioHelper.init();
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  await EasyLocalization.ensureInitialized();
  await CacheHelper.init();

  await Firebase.initializeApp();
  await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
  await PushNotificationService().initialize();
  FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };

  await ScreenUtil.ensureScreenSize();

  Bloc.observer = MyBlocObserver();
  // CacheHelper.clearAllData();
  // CacheHelper.clearAllSecuredData();
  AppConstants.userToken =
      await CacheHelper.getSecuredString(key: CacheKeys.userToken);
  logSuccess("User Token: ${AppConstants.userToken}");

  logSuccess("fcmToken: ${CacheHelper.getData(key: CacheKeys.deviceToken)}");

  runApp(
    EasyLocalization(
      saveLocale: true,
      useFallbackTranslations: true,
      fallbackLocale: const Locale('ar', 'EG'),
      supportedLocales: const [
        Locale('ar', 'EG'),
        Locale('en', 'UK'),
      ],
      path: 'assets/languages',
      child: Phoenix(
        child: Erp(
          appRouter: AppRouter(),
        ),
      ),
    ),
  );
}


// ? 1//03h8-IY49pc1TCgYIARAAGAMSNwF-L9IrHhDF9T1ppSeFRgA4kdJvRiooE5iAYfUS6HMzaER7Zyo37-6exE6reIPnZ06f7F31IxE