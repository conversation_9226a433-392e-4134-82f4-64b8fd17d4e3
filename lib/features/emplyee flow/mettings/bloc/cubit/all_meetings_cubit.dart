import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/model/all_meetings_model.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/repo/all_meetings_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'all_meetings_state.dart';

class AllMeetingsCubit extends Cubit<AllMeetingsState> {
  AllMeetingsCubit(this._repo) : super(AllMeetingsInitial());

  final AllMeetingRepo _repo;

  AllMeetingsModel? model;

  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController startTimeController = TextEditingController();
  final TextEditingController endTimeController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final TextEditingController emailController = TextEditingController();
  DateTime? selectedDate;
  TimeOfDay? selectedTime;
  List<String> attendees = [];

  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreProjects();
      }
    });
  }

  Future<void> getAllMeetings() async {
    emit(AllMeetingsLoading());
    final result = await _repo.getAllMeetings(page: currentPage);

    result.when(
      success: (data) {
        model = data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(AllMeetingsSuccess());
      },
      failure: (error) {
        emit(AllMeetingsError());
      },
    );
  }

  Future<void> createMeeting() async {
    showLoading();
    emit(CreateMeetingLoading());

    final result = await _repo.createMeeting(
      title: titleController.text.trim(),
      description: descriptionController.text.trim(),
      startTime: DateTime.parse(startTimeController.text.replaceAllMapped(
              RegExp(r'[٠-٩]'),
              (match) => (match.group(0)!.codeUnitAt(0) - 0x0660).toString()))
          .toUtc()
          .toIso8601String(),
      endTime: DateTime.parse(endTimeController.text.replaceAllMapped(
              RegExp(r'[٠-٩]'),
              (match) => (match.group(0)!.codeUnitAt(0) - 0x0660).toString()))
          .toUtc()
          .toIso8601String(),
      attendees: attendees,
    );

    result.when(
      success: (message) async {
        emit(CreateMeetingSuccess());
        hideLoading();
        clearForm();
        emit(AllMeetingsSuccess());

        model!.data.data.add(message);
      },
      failure: (error) {
        hideLoading();
        emit(CreateMeetingError());
      },
    );
  }

  void addAttendee(String email) {
    if (!attendees.contains(email)) {
      attendees.add(email);
      emit(AttendeesUpdated());
    }
  }

  void removeAttendee(String email) {
    attendees.remove(email);
    emit(AttendeesUpdated());
  }

  void clearForm() {
    titleController.clear();
    descriptionController.clear();
    startTimeController.clear();
    endTimeController.clear();
    attendees.clear();
  }

  Future<void> loadMoreProjects() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(AllMeetingsLoadingMoreState());

    final nextPage = currentPage + 1;
    final result = await _repo.getAllMeetings(page: nextPage);

    result.when(
      success: (data) {
        model!.data.data.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(AllMeetingsSuccess());
      },
      failure: (errorHandler) {
        emit(AllMeetingsError());
      },
    );

    isLoadingMore = false;
  }
}
