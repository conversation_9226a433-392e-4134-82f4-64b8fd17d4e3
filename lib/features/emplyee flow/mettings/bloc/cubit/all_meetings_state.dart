part of 'all_meetings_cubit.dart';

abstract class AllMeetingsState {}

class AllMeetingsInitial extends AllMeetingsState {}

class AllMeetingsLoading extends AllMeetingsState {}

class AllMeetingsSuc<PERSON> extends AllMeetingsState {}

class AllMeetingsError extends AllMeetingsState {}

class AllMeetingsLoadingMoreState extends AllMeetingsState {}

class CreateMeetingLoading extends AllMeetingsState {}

class CreateMeetingSuc<PERSON> extends AllMeetingsState {}

class CreateMeetingError extends AllMeetingsState {}

class AttendeesUpdated extends AllMeetingsState {}
