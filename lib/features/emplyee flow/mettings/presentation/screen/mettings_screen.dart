import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/mettings/bloc/cubit/all_meetings_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AllMettingsScreen extends StatelessWidget {
  const AllMettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AllMeetingsCubit>();
    return BlocBuilder<AllMeetingsCubit, AllMeetingsState>(
      buildWhen: (previous, current) =>
          current is AllMeetingsError ||
          current is AllMeetingsLoading ||
          current is AllMeetingsSuccess,
      builder: (context, state) {
        if (state is AllMeetingsLoading) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                leading: Icon(Icons.notifications_none_rounded),
                title: Text("homeEmployee.allMettings".tr()),
              ),
              body: ListView.separated(
                itemCount: 15,
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                separatorBuilder: (BuildContext context, int index) {
                  return 16.sp.verticalSpace;
                },
                itemBuilder: (BuildContext context, int index) {
                  return ProjectsItemWidget(
                    onTap: () {},
                    title: "Meeting  Name",
                    description: "14:30",
                  );
                },
              ),
              bottomNavigationBar: SafeArea(
                minimum: EdgeInsets.all(12.sp),
                child: CustomButtonWidget(
                  text: 'homeEmployee.addMeeting'.tr(),
                  onPressed: () {
                    context.pushNamed(Routes.addMeetingScreen);
                  },
                ),
              ),
            ),
          );
        }
        return Scaffold(
          appBar: AppBar(
            leading: Icon(Icons.notifications_none_rounded),
            title: Text("homeEmployee.allMettings".tr()),
          ),
          body: Column(
            children: [
              Expanded(
                child:
                 cubit.model!.data.data.isEmpty
                    ? Center(
                        child: Text(
                          "homeEmployee.noMeetings".tr(),
                          style: TextStyle(fontSize: 16.sp, color: Colors.grey),
                        ),
                      )
                    : ListView.separated(
                        controller: cubit.scrollController,
                        itemCount: cubit.model!.data.data.length,
                        padding: EdgeInsets.symmetric(
                            vertical: 18.sp, horizontal: 16.sp),
                        separatorBuilder: (BuildContext context, int index) =>
                            16.sp.verticalSpace,
                        itemBuilder: (BuildContext context, int index) {
                          return ProjectsItemWidget(
                            onTap: () async {
                              context.pushNamed(
                                Routes.mettingDetails,
                                arguments: cubit.model!.data.data[index],
                              );
                            },
                            title: cubit.model!.data.data[index].title,
                            description:
                                cubit.model!.data.data[index].description,
                          );
                        },
                      ),
              ),
              BlocBuilder<AllMeetingsCubit, AllMeetingsState>(
                buildWhen: (previous, current) =>
                    current is AllMeetingsLoadingMoreState ||
                    current is AllMeetingsSuccess,
                builder: (context, state) {
                  if (state is AllMeetingsLoadingMoreState) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  return SizedBox.shrink();
                },
              )
            ],
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.all(12.sp),
            child: CustomButtonWidget(
              text: 'homeEmployee.addMeeting'.tr(),
              onPressed: () {
                context.pushNamed(Routes.addMeetingScreen, arguments: cubit);
              },
            ),
          ),
        );
      },
    );
  }
}
