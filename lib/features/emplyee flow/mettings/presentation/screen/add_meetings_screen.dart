import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/emplyee%20flow/mettings/bloc/cubit/all_meetings_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddMeetingsScreen extends StatelessWidget {
  const AddMeetingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AllMeetingsCubit>();

    return Scaffold(
      backgroundColor: const Color(0xffFCFCFC),
      appBar: AppBar(
        title: Text('homeEmployee.addMeeting'.tr()),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 18.sp),
        child: Form(
          key: cubit.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('homeEmployee.meetingName'.tr(),
                  style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.titleController,
                hintText: 'homeEmployee.enteryourMeetingName'.tr(),
                validator: (val) => val!.isEmpty ? 'Required'.tr() : null,
              ),
              SizedBox(height: 12.h),
              Text('homeEmployee.meetingStartDateTime'.tr(),
                  style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.startTimeController,
                hintText: 'homeEmployee.selectStartDateTime'.tr(),
                readOnly: true,
                suffixIcon: Icon(Icons.calendar_today_rounded),
                onTap: () async {
                  DateTime? date = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    TimeOfDay? time = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                    );
                    if (time != null) {
                      DateTime dateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                      cubit.selectedDate = dateTime;
                      cubit.startTimeController.text =
                          DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
                    }
                  }
                },
                validator: (val) => val!.isEmpty ? 'Required'.tr() : null,
              ),
              SizedBox(height: 12.h),
              Text('homeEmployee.meetingEndDateTime'.tr(),
                  style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.endTimeController,
                hintText: 'homeEmployee.selectEndDateTime'.tr(),
                readOnly: true,
                suffixIcon: Icon(Icons.access_time_rounded),
                onTap: () async {
                  DateTime? date = await showDatePicker(
                    context: context,
                    initialDate: cubit.selectedDate ?? DateTime.now(),
                    firstDate: cubit.selectedDate ?? DateTime.now(),
                    lastDate: DateTime(2100),
                  );
                  if (date != null) {
                    TimeOfDay? time = await showTimePicker(
                      context: context,
                      initialTime: TimeOfDay.now(),
                    );
                    if (time != null) {
                      DateTime dateTime = DateTime(
                        date.year,
                        date.month,
                        date.day,
                        time.hour,
                        time.minute,
                      );
                      cubit.selectedTime = TimeOfDay.fromDateTime(dateTime);
                      cubit.endTimeController.text =
                          DateFormat('yyyy-MM-dd HH:mm').format(dateTime);
                    }
                  }
                },
                validator: (val) => val!.isEmpty ? 'Required'.tr() : null,
              ),
              SizedBox(height: 12.h),
              Text('homeEmployee.description'.tr(),
                  style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.descriptionController,
                height: 80.sp,
                maxLines: 10,
                isChat: true,
                hintText: 'homeEmployee.enterTheDescription'.tr(),
                textAlignVertical: TextAlignVertical.top,
                contentPadding: EdgeInsets.only(
                  top: 15.h,
                  left: 10.w,
                  right: 10.w,
                  bottom: 80.sp,
                ),
                validator: (val) => val!.isEmpty ? 'Required'.tr() : null,
              ),
              SizedBox(height: 12.h),
              Text('homeEmployee.attendees'.tr(),
                  style: Styles.contentEmphasis),
              Row(
                children: [
                  Expanded(
                    child: CustomTextFormFieldWidget(
                      keyboardType: TextInputType.emailAddress,
                      controller: cubit.emailController,
                      hintText: 'homeEmployee.enterAttendeeEmail'.tr(),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.add_circle),
                    onPressed: () {
                      final email = cubit.emailController.text.trim();
                      if (email.isNotEmpty) {
                        cubit.addAttendee(email);
                        cubit.emailController.clear();
                      }
                    },
                  ),
                ],
              ),
              BlocBuilder<AllMeetingsCubit, AllMeetingsState>(
                buildWhen: (previous, current) => current is AttendeesUpdated,
                builder: (context, state) {
                  return Wrap(
                    children: cubit.attendees
                        .map((email) => Padding(
                              padding: EdgeInsets.all(4.0.sp),
                              child: Chip(
                                label: Text(email),
                                onDeleted: () => cubit.removeAttendee(email),
                              ),
                            ))
                        .toList(),
                  );
                },
              ),
              SizedBox(height: 20.h),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(12.sp),
        child: BlocListener<AllMeetingsCubit, AllMeetingsState>(
            listenWhen: (previous, current) =>
                current is CreateMeetingSuccess ||
                current is CreateMeetingError,
            listener: (context, state) {
              if (state is CreateMeetingSuccess) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      backgroundColor: AppColors.greenColor200,
                      content: Text('Meeting created successfully!'.tr())),
                );
                Navigator.pop(context);
              } else if (state is CreateMeetingError) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                      backgroundColor: AppColors.redColor200,
                      content: Text('Failed to create meeting'.tr())),
                );
              }
            },
            child: CustomButtonWidget(
              text: 'homeEmployee.add'.tr(),
              onPressed: () {
                if (cubit.formKey.currentState!.validate()) {
                  if (cubit.selectedDate != null &&
                      cubit.selectedTime != null) {
                    cubit.createMeeting();
                  }
                }
              },
            )),
      ),
    );
  }
}
