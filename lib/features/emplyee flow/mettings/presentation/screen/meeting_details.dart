import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/model/all_meetings_model.dart';
import 'package:erp/features/emplyee%20flow/mettings/presentation/widget/metting%20details%20widgets/details_row_widget.dart';
import 'package:erp/features/emplyee%20flow/mettings/presentation/widget/metting%20details%20widgets/details_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';

class MeetingDetailsScreen extends StatelessWidget {
  final Meeting meeting;

  const MeetingDetailsScreen({super.key, required this.meeting});

  String formatDateTime(String utcTime) {
    final dateTime = DateTime.parse(utcTime).toLocal();
    return DateFormat('EEE, MMM d • h:mm a').format(dateTime);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          meeting.title,
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back_ios_new_rounded, size: 20.sp),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 16.h),
            Text(
              formatDateTime(meeting.startTime).split('•').first.trim(),
              style: TextStyle(
                fontSize: 14.sp,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 24.h),
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16.r),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha:  0.05),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  )
                ],
                border: Border.all(
                  color: Colors.grey.shade200,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DetailSection(
                    icon: Icons.access_time_rounded,
                    title: 'time'.tr(),
                    children: [
                      DetailRow(
                        label: 'from'.tr(),
                        value: formatDateTime(meeting.startTime)
                            .split('•')
                            .last
                            .trim(),
                      ),
                      SizedBox(height: 8.h),
                      DetailRow(
                        label: 'to'.tr(),
                        value: formatDateTime(meeting.endTime)
                            .split('•')
                            .last
                            .trim(),
                      ),
                    ],
                  ),
                  Divider(
                      height: 32.h, thickness: 1, color: Colors.grey.shade100),
                  DetailSection(
                    icon: Icons.description_outlined,
                    title: 'description'.tr(),
                    children: [
                      Text(
                        meeting.description,
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey.shade800,
                          height: 1.5,
                        ),
                      ),
                    ],
                  ),
                  Divider(
                      height: 32.h, thickness: 1, color: Colors.grey.shade100),
                  DetailSection(
                    icon: Icons.person_outline,
                    title: 'organizer'.tr(),
                    children: [
                      Text(
                        '${meeting.organizer.name} ',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.grey.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  if (meeting.googleMeetLink.isNotEmpty) ...[
                    Divider(
                        height: 32.h,
                        thickness: 1,
                        color: Colors.grey.shade100),
                    DetailSection(
                      icon: Icons.videocam_rounded,
                      title: 'meeting_link'.tr(),
                      children: [
                        GestureDetector(
                          onTap: () async {
                            final Uri uri = Uri.parse(meeting.googleMeetLink);
                            await launchUrl(uri,
                                mode: LaunchMode.externalApplication);
                          },
                          child: Text(
                            meeting.googleMeetLink,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.primaryColor800,
                              fontWeight: FontWeight.w500,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                  Divider(
                      height: 32.h, thickness: 1, color: Colors.grey.shade100),
                  DetailSection(
                    icon: Icons.people_outline_rounded,
                    title:
                        'attendees'.tr(args: ['${meeting.attendees.length}']),
                    children: [
                      SizedBox(height: 8.h),
                      Wrap(
                        spacing: 8.w,
                        runSpacing: 8.h,
                        children: meeting.attendees.map((email) {
                          return Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 12.w,
                              vertical: 6.h,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade50,
                              borderRadius: BorderRadius.circular(12.r),
                              border: Border.all(
                                color: Colors.grey.shade200,
                                width: 1,
                              ),
                            ),
                            child: Text(
                              email,
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: Colors.grey.shade800,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(height: 24.h),
            if (meeting.googleMeetLink.isNotEmpty)
              CustomButtonWidget(
                onPressed: () async {
                  final Uri uri = Uri.parse(meeting.googleMeetLink);
                  await launchUrl(uri, mode: LaunchMode.externalApplication);
                },
                text: 'join_meeting'.tr(),
              ),
          ],
        ),
      ),
    );
  }
}
