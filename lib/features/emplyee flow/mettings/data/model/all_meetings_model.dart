import 'package:erp/features/emplyee%20flow/asking%20permission/data/model/request_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'all_meetings_model.g.dart';

@JsonSerializable(explicitToJson: true)
class AllMeetingsModel {
  final MeetingData data;
  final String status;
  final String error;
  final int code;

  AllMeetingsModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory AllMeetingsModel.fromJson(Map<String, dynamic> json) =>
      _$AllMeetingsModelFromJson(json);

  Map<String, dynamic> toJson() => _$AllMeetingsModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class MeetingData {
  final List<Meeting> data;
  final PaginationLinks links;
  final Meta meta;

  MeetingData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory MeetingData.fromJson(Map<String, dynamic> json) =>
      _$MeetingDataFromJson(json);

  Map<String, dynamic> toJson() => _$MeetingDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Meeting {
  final int id;
  final String title;
  final String description;
  @JsonKey(name: 'start_time')
  final String startTime;
  @JsonKey(name: 'end_time')
  final String endTime;
  @JsonKey(name: 'google_meet_link')
  final String googleMeetLink;
  final List<String> attendees;
  final Organizer organizer;

  Meeting({
    required this.id,
    required this.title,
    required this.description,
    required this.startTime,
    required this.endTime,
    required this.googleMeetLink,
    required this.attendees,
    required this.organizer,
  });

  factory Meeting.fromJson(Map<String, dynamic> json) =>
      _$MeetingFromJson(json);

  Map<String, dynamic> toJson() => _$MeetingToJson(this);
}

@JsonSerializable()
class Organizer {
  final int id;
  final String name;
  final String type;

  Organizer({
    required this.id,
    required this.name,
    required this.type,
  });

  factory Organizer.fromJson(Map<String, dynamic> json) =>
      _$OrganizerFromJson(json);

  Map<String, dynamic> toJson() => _$OrganizerToJson(this);
}

@JsonSerializable()
class PaginationLinks {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  PaginationLinks({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory PaginationLinks.fromJson(Map<String, dynamic> json) =>
      _$PaginationLinksFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationLinksToJson(this);
}
