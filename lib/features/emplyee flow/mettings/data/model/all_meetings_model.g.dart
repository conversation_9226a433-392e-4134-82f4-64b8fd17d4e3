// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'all_meetings_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AllMeetingsModel _$AllMeetingsModelFromJson(Map<String, dynamic> json) =>
    AllMeetingsModel(
      data: MeetingData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$AllMeetingsModelToJson(AllMeetingsModel instance) =>
    <String, dynamic>{
      'data': instance.data.toJson(),
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

MeetingData _$MeetingDataFromJson(Map<String, dynamic> json) => MeetingData(
      data: (json['data'] as List<dynamic>)
          .map((e) => Meeting.fromJson(e as Map<String, dynamic>))
          .toList(),
      links: PaginationLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MeetingDataToJson(MeetingData instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'links': instance.links.toJson(),
      'meta': instance.meta.toJson(),
    };

Meeting _$MeetingFromJson(Map<String, dynamic> json) => Meeting(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      description: json['description'] as String,
      startTime: json['start_time'] as String,
      endTime: json['end_time'] as String,
      googleMeetLink: json['google_meet_link'] as String,
      attendees:
          (json['attendees'] as List<dynamic>).map((e) => e as String).toList(),
      organizer: Organizer.fromJson(json['organizer'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$MeetingToJson(Meeting instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'description': instance.description,
      'start_time': instance.startTime,
      'end_time': instance.endTime,
      'google_meet_link': instance.googleMeetLink,
      'attendees': instance.attendees,
      'organizer': instance.organizer.toJson(),
    };

Organizer _$OrganizerFromJson(Map<String, dynamic> json) => Organizer(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String,
    );

Map<String, dynamic> _$OrganizerToJson(Organizer instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
    };

PaginationLinks _$PaginationLinksFromJson(Map<String, dynamic> json) =>
    PaginationLinks(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$PaginationLinksToJson(PaginationLinks instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };
