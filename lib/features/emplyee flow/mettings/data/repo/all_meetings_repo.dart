import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/api_services/meetings_api_services.dart';
import 'package:erp/features/emplyee%20flow/mettings/data/model/all_meetings_model.dart';

class AllMeetingRepo {
  final AllMeatingsApiServices _leaveApiServices;
  AllMeetingRepo(this._leaveApiServices);

  Future<ApiResult<AllMeetingsModel>> getAllMeetings(
      {required int page}) async {
    final response = await _leaveApiServices.getAllMeetings(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        AllMeetingsModel model = AllMeetingsModel.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<Meeting>> createMeeting({
    required String title,
    required String description,
    required String startTime,
    required String endTime,
    required List<String> attendees,
  }) async {
    final response = await _leaveApiServices.createMeeting(
      title: title,
      description: description,
      startTime: startTime,
      endTime: endTime,
      attendees: attendees,
    );

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        // Meeting meeting =
        return ApiResult.success(Meeting.fromJson(response.data["data"]));
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
