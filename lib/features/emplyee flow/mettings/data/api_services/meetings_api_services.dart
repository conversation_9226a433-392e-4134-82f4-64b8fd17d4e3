import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class AllMeatingsApiServices {
  AllMeatingsApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> getAllMeetings({required int page}) async {
    return _dioFactory
        .get(endPoint: EndPoints.getAllMeetings, data: {"page": page});
  }

  Future<Response?> createMeeting({
    required String title,
    required String description,
    required String startTime,
    required String endTime,
    required List<String> attendees,
  }) async {
    return _dioFactory.post(
      endPoint: EndPoints.createMeeting,
      data: {
        "title": title,
        "description": description,
        "start_time": startTime,
        "end_time": endTime,
        "attendees": attendees,
      },
    );
  }
}
