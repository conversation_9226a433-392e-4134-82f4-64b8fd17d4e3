import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/support/business_logic/support_cubit.dart';
import 'package:erp/features/emplyee%20flow/support/presentation/widgets/all_ticket_number_skeletonizer_widget.dart';
import 'package:erp/features/emplyee%20flow/support/presentation/widgets/all_ticket_number_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SupportScreen extends StatelessWidget {
  const SupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SupportCubit>();

    return Scaffold(
      body: Column(
        children: [
          // 30.verticalSpace,
          AppBar(title: Text('support.supportText'.tr())),
          BlocBuilder<SupportCubit, SupportState>(
            builder: (context, state) {
              if (state is GetAllTicketsLoading ||
                  cubit.ticketDataModel == null) {
                return Expanded(
                  child: ListView.separated(
                    shrinkWrap: true,
                    itemCount: 10,
                    padding:
                        EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
                    separatorBuilder: (_, __) => 16.verticalSpace,
                    itemBuilder: (context, index) {
                      return AllTicketNumberSkeletonizerWidget();
                    },
                  ),
                );
              }
              if (cubit.ticketDataModel!.data.data.isEmpty) {
                return Expanded(
                  child: Center(
                    child: Text(
                      "support.noTickets".tr(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                );
              }
              return Expanded(
                child: ListView.separated(
                  controller: cubit.scrollController,
                  shrinkWrap: true,
                  itemCount: cubit.ticketDataModel!.data.data.length,
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
                  separatorBuilder: (_, __) => 16.verticalSpace,
                  itemBuilder: (context, index) {
                    return AllTicketNumberWidget(
                      id: cubit.ticketDataModel!.data.data[index].id,
                    );
                  },
                ),
              );
            },
          ),
          // BlocBuilder<SupportCubit, SupportState>(
          //   builder: (context, state) {
          //     return Expanded(
          //       child: state is GetAllTicketsLoading ||
          //               cubit.ticketDataModel == null
          //           ? ListView.separated(
          //               shrinkWrap: true,
          //               itemCount: 10,
          //               padding:
          //                   EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
          //               separatorBuilder: (_, __) => 16.verticalSpace,
          //               itemBuilder: (context, index) {
          //                 return AllTicketNumberSkeletonizerWidget();
          //               },
          //             )
          //           : ListView.separated(
          //               controller: cubit.scrollController,
          //               shrinkWrap: true,
          //               itemCount: cubit.ticketDataModel!.data.data.length,
          //               padding:
          //                   EdgeInsets.only(left: 16.w, right: 16.w, top: 30.h),
          //               separatorBuilder: (_, __) => 16.verticalSpace,
          //               itemBuilder: (context, index) {
          //                 return AllTicketNumberWidget(
          //                   id: cubit.ticketDataModel!.data.data[index].id,
          //                 );
          //               },
          //             ),
          //     );
          //   },
          // ),
        ],
      ),
      bottomNavigationBar:
          (CacheHelper.getData(key: CacheKeys.role) == "client")
              ? SafeArea(
                  minimum: EdgeInsets.all(20.sp),
                  child: BlocProvider.value(
                    value: SupportCubit(getIt()),
                    child: CustomButtonWidget(
                      text: 'support.contactSupport'.tr(),
                      onPressed: () {
                        context
                            .pushNamed(Routes.contactSupportScreen,
                                arguments: cubit)
                            .then(
                          (value) {
                            context.read<SupportCubit>().getAllTickets();
                          },
                        );
                      },
                    ),
                  ),
                )
              : SizedBox.shrink(),
    );
  }
}
