import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_drop_down_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/emplyee%20flow/support/business_logic/support_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ContactSupportScreen extends StatelessWidget {
  const ContactSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SupportCubit>();

    return Scaffold(
      body: SingleChildScrollView(
        physics: NeverScrollableScrollPhysics(),
        child: Column(
          children: [
            AppBar(title: Text('support.contactSupport'.tr())),
            30.verticalSpace,
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                children: [
                  CustomDropdownButton<String>(
                    hint: 'support.messageStatus'.tr(),
                    items: [
                      'support.urgent'.tr(),
                      'support.medium'.tr(),
                      'support.normal'.tr()
                    ],
                    value: cubit.selectedPriority,
                    onChanged: (selectedValue) {
                      cubit.selectPriority(selectedValue!);
                    },
                  ),
                  16.verticalSpace,
                  CustomTextFormFieldWidget(
                    controller: context.read<SupportCubit>().titleController,
                    labelText: 'support.messageTitle'.tr(),
                    labelStyle: Styles.contentEmphasis.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.next,
                    prefixIcon: Icon(
                      Icons.message,
                      size: 23.sp,
                    ),
                    backgroundColor: Colors.white,
                    borderRadius: AppConstants.borderRadius,
                  ),
                  16.verticalSpace,
                  CustomTextFormFieldWidget(
                    controller: context.read<SupportCubit>().messageController,
                    borderRadius: AppConstants.borderRadius,
                    backgroundColor: Colors.white,
                    borderColor: AppColors.neutralColor200,
                    labelText: 'support.messageDetails'.tr(),
                    maxLines: 7,
                    labelStyle: Styles.contentEmphasis.copyWith(
                      color: AppColors.neutralColor1200,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: BlocConsumer<SupportCubit, SupportState>(
        listener: (context, state) {
          if (state is CreateTicketSuccess) {
            context.pop();
            context.read<SupportCubit>().getAllTickets();
          }
        },
        builder: (context, state) {
          final supportCubit = context.read<SupportCubit>();

          return SafeArea(
            minimum: EdgeInsets.all(20.sp),
            child: CustomButtonWidget(
              text: 'support.send'.tr(),
              onPressed: () {
                supportCubit.createTicket();
              },
            ),
          );
        },
      ),
    );
  }
}
