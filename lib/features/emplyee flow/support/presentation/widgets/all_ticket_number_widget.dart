import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AllTicketNumberWidget extends StatelessWidget {
  const AllTicketNumberWidget({super.key, this.id});

  final int? id;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        context.pushNamed(Routes.chatScreen, arguments: id);
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(color: AppColors.primaryColor900),
        ),
        child: Row(
          children: [
            Column(
              spacing: 4.sp,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'support.ticketNumber'.tr(),
                  style: Styles.heading5.copyWith(
                      fontWeight: FontWeight.w700,
                      fontSize: 16.sp,
                      color: AppColors.neutralColor1200),
                ),
                Text(
                  "#TICKET-$id",
                  style: Styles.contentRegular.copyWith(
                      fontWeight: FontWeight.w400,
                      fontSize: 16.sp,
                      color: AppColors.neutralColor600),
                ),
              ],
            ),
            Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              size: 14.sp,
              color: AppColors.primaryColor900,
            ),
          ],
        ),
      ),
    );
  }
}
