import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_rich_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class PopUpSendDialogWidget extends StatelessWidget {
  const PopUpSendDialogWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius + 4),
      ),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(20.sp),
        decoration: BoxDecoration(
          color: AppColors.scaffoldBackground,
          borderRadius: BorderRadius.circular((AppConstants.borderRadius + 4)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () {
                context.pop();
              },
              child: Container(
                width: 50.w,
                height: 50.h,
                padding: EdgeInsets.all(12.sp),
                decoration: BoxDecoration(
                  color: AppColors.neutralColor100,
                  borderRadius: BorderRadius.circular(
                    AppConstants.borderRadius,
                  ),
                ),
                child: SvgPicture.asset(width: 24, height: 24, ""),
              ),
            ),
            20.verticalSpace,
            Text(
              'support.SentSuccessfully'.tr(),
              textAlign: TextAlign.center,
              style: Styles.heading3.copyWith(color: Colors.black),
            ),
            8.verticalSpace,
            CustomRichText(
              text1: 'support.ticketNumber'.tr(),
              text2: '#TICKET-12987',
              textAlign: TextAlign.center,
              textStyle1: Styles.contentRegular.copyWith(
                color: AppColors.neutralColor1000,
              ),
              textStyle2: Styles.contentRegular.copyWith(
                color: AppColors.neutralColor1000,
              ),
            ),
            20.verticalSpace,
            CustomButtonWidget(
              margin: EdgeInsets.symmetric(horizontal: 70.w),
              height: 55.h,
              text: 'support.next'.tr(),
            )
          ],
        ),
      ),
    );
  }
}
