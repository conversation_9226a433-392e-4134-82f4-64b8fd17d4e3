import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class SupportApiServices {
  SupportApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> createTicket({
    required String title,
    required String message,
  }) async {
    return _dioFactory.post(endPoint: EndPoints.createTicket, data: {
      'title': title,
      'message': message,
    });
  }

  Future<Response?> getAllTickets({required int page}) async {
    return _dioFactory
        .get(endPoint: EndPoints.getAllTickets, data: {"page": page});
  }
}
