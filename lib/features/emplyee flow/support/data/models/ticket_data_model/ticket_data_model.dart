import 'package:json_annotation/json_annotation.dart';

part 'ticket_data_model.g.dart';

@JsonSerializable()
class TicketDataModel {
  final String status;
  final String error;
  final int code;
  final TicketData data;

  TicketDataModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory TicketDataModel.fromJson(Map<String, dynamic> json) =>
      _$TicketDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$TicketDataModelToJson(this);
}

@JsonSerializable()
class TicketData {
  final List<Ticket> data;
  final TicketLinks links;
  final TicketMeta meta;

  TicketData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory TicketData.fromJson(Map<String, dynamic> json) =>
      _$TicketDataFromJson(json);
  Map<String, dynamic> toJson() => _$TicketDataToJson(this);
}

@JsonSerializable()
class Ticket {
  final int id;
  final String title;
  final String client;
  final String email;
  final String image;
  final String status;
  final List<Message> messages;

  Ticket({
    required this.id,
    required this.title,
    required this.client,
    required this.email,
    required this.image,
    required this.status,
    required this.messages,
  });

  factory Ticket.fromJson(Map<String, dynamic> json) => _$TicketFromJson(json);
  Map<String, dynamic> toJson() => _$TicketToJson(this);
}

@JsonSerializable()
class Message {
  final String message;
  @JsonKey(name: 'is_admin')
  final int isAdmin;
  final dynamic employee;

  Message({
    required this.message,
    required this.isAdmin,
    this.employee,
  });

  factory Message.fromJson(Map<String, dynamic> json) =>
      _$MessageFromJson(json);
  Map<String, dynamic> toJson() => _$MessageToJson(this);
}

@JsonSerializable()
class TicketLinks {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  TicketLinks({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory TicketLinks.fromJson(Map<String, dynamic> json) =>
      _$TicketLinksFromJson(json);
  Map<String, dynamic> toJson() => _$TicketLinksToJson(this);
}

@JsonSerializable()
class TicketMeta {
  @JsonKey(name: 'current_page')
  final int? currentPage;
  final int? from;
  @JsonKey(name: 'last_page')
  final int? lastPage;
  final List<TicketPageLink> links;
  final String path;
  @JsonKey(name: 'per_page')
  final int? perPage;
  final int? to;
  final int? total;

  TicketMeta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory TicketMeta.fromJson(Map<String, dynamic> json) =>
      _$TicketMetaFromJson(json);
  Map<String, dynamic> toJson() => _$TicketMetaToJson(this);
}

@JsonSerializable()
class TicketPageLink {
  final String? url;
  final String label;
  final bool active;

  TicketPageLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory TicketPageLink.fromJson(Map<String, dynamic> json) =>
      _$TicketPageLinkFromJson(json);
  Map<String, dynamic> toJson() => _$TicketPageLinkToJson(this);
}
