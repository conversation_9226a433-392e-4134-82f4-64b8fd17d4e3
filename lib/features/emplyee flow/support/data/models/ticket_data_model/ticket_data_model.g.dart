// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ticket_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TicketDataModel _$TicketDataModelFromJson(Map<String, dynamic> json) =>
    TicketDataModel(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: TicketData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TicketDataModelToJson(TicketDataModel instance) =>
    <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

TicketData _$TicketDataFromJson(Map<String, dynamic> json) => TicketData(
      data: (json['data'] as List<dynamic>)
          .map((e) => Ticket.fromJson(e as Map<String, dynamic>))
          .toList(),
      links: TicketLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: TicketMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TicketDataToJson(TicketData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'links': instance.links,
      'meta': instance.meta,
    };

Ticket _$TicketFromJson(Map<String, dynamic> json) => Ticket(
      id: (json['id'] as num).toInt(),
      title: json['title'] as String,
      client: json['client'] as String,
      email: json['email'] as String,
      image: json['image'] as String,
      status: json['status'] as String,
      messages: (json['messages'] as List<dynamic>)
          .map((e) => Message.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$TicketToJson(Ticket instance) => <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'client': instance.client,
      'email': instance.email,
      'image': instance.image,
      'status': instance.status,
      'messages': instance.messages,
    };

Message _$MessageFromJson(Map<String, dynamic> json) => Message(
      message: json['message'] as String,
      isAdmin: (json['is_admin'] as num).toInt(),
      employee: json['employee'],
    );

Map<String, dynamic> _$MessageToJson(Message instance) => <String, dynamic>{
      'message': instance.message,
      'is_admin': instance.isAdmin,
      'employee': instance.employee,
    };

TicketLinks _$TicketLinksFromJson(Map<String, dynamic> json) => TicketLinks(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$TicketLinksToJson(TicketLinks instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };

TicketMeta _$TicketMetaFromJson(Map<String, dynamic> json) => TicketMeta(
      currentPage: (json['current_page'] as num?)?.toInt(),
      from: (json['from'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
      links: (json['links'] as List<dynamic>)
          .map((e) => TicketPageLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      path: json['path'] as String,
      perPage: (json['per_page'] as num?)?.toInt(),
      to: (json['to'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$TicketMetaToJson(TicketMeta instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'links': instance.links,
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
    };

TicketPageLink _$TicketPageLinkFromJson(Map<String, dynamic> json) =>
    TicketPageLink(
      url: json['url'] as String?,
      label: json['label'] as String,
      active: json['active'] as bool,
    );

Map<String, dynamic> _$TicketPageLinkToJson(TicketPageLink instance) =>
    <String, dynamic>{
      'url': instance.url,
      'label': instance.label,
      'active': instance.active,
    };
