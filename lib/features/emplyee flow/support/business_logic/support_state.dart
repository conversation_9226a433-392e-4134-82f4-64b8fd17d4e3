part of 'support_cubit.dart';

@immutable
sealed class SupportState {}

final class SupportInitial extends SupportState {}

/// Toggle Priority
class PrioritySelectedState extends SupportState {
  final String priority;
  PrioritySelectedState(this.priority);
}

/// Create ticket
final class CreateTicketLoading extends SupportState {}

final class CreateTicketSuccess extends SupportState {}

final class CreateTicketError extends SupportState {}

/// Get All Tickets
final class GetAllTicketsLoading extends SupportState {}

final class GetAllTicketsSuccess extends SupportState {}

final class GetAllTicketsError extends SupportState {}

final class GetAllTicketsLoadingMore extends SupportState {}
