import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/support/data/models/ticket_data_model/ticket_data_model.dart';
import 'package:erp/features/emplyee%20flow/support/data/repos/support_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'support_state.dart';

class SupportCubit extends Cubit<SupportState> {
  SupportCubit(this.supportRepository) : super(SupportInitial());

  final SupportRepository supportRepository;

  final TextEditingController titleController = TextEditingController();
  final TextEditingController messageController = TextEditingController();

  final ScrollController scrollController = ScrollController();

  TicketDataModel? ticketDataModel;
  List<Ticket> tickets = [];

  String? selectedPriority;

  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreTickets();
      }
    });
  }

  void selectPriority(String priority) {
    selectedPriority = priority;
    emit(PrioritySelectedState(priority));
  }

  Future<void> getAllTickets() async {
    emit(GetAllTicketsLoading());
    currentPage = 1;

    final result = await supportRepository.getAllTickets(page: currentPage);
    result.when(
      success: (data) {
        ticketDataModel = data;
        tickets = data.data.data;
        currentPage = data.data.meta.currentPage!;
        lastPage = data.data.meta.lastPage!;
        emit(GetAllTicketsSuccess());
      },
      failure: (error) {
        emit(GetAllTicketsError());
      },
    );
  }

  Future<void> loadMoreTickets() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(GetAllTicketsLoadingMore());

    final nextPage = currentPage + 1;

    final result = await supportRepository.getAllTickets(page: nextPage);
    result.when(
      success: (data) {
        tickets.addAll(data.data.data);
        currentPage = data.data.meta.currentPage!;
        emit(GetAllTicketsSuccess());
      },
      failure: (error) {
        emit(GetAllTicketsError());
      },
    );

    isLoadingMore = false;
  }

  Future<void> createTicket() async {
    showLoading();
    emit(CreateTicketLoading());

    final result = await supportRepository.createTicket(
      title: titleController.text,
      message: messageController.text,
    );

    result.when(
      success: (_) {
        hideLoading();
        emit(CreateTicketSuccess());
      },
      failure: (_) {
        hideLoading();
        emit(CreateTicketError());
      },
    );
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    titleController.dispose();
    messageController.dispose();
    return super.close();
  }
}
