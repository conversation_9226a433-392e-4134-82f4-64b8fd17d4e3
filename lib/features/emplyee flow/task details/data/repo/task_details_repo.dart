import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/api%20services/task_details_api_services.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/models/task_model.dart';

class TaskDetailsRepository {
  final TaskDetailsApiServices authApiServices;

  TaskDetailsRepository(this.authApiServices);

  Future<ApiResult<TaskModel>> getTaskDetails({required int id}) async {
    final response = await authApiServices.getTaskDetails(id: id);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        TaskModel model = TaskModel.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<bool>> updateStatus(
      {required int id, required String status}) async {
    final response = await authApiServices.updateStatus(id: id, status: status);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success(true);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }


}
