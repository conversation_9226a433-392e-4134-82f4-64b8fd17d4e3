import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class TaskDetailsApiServices {
  TaskDetailsApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> getTaskDetails({required int id}) async {
    return _dioFactory.get(endPoint: "${EndPoints.getTaskDetails}/$id");
  }

  Future<Response?> updateStatus(
      {required int id, required String status}) async {
    return _dioFactory.post(
        endPoint: "${EndPoints.getTaskDetails}/$id",
        data: {'_method': 'put', 'status': status});
  }
}
