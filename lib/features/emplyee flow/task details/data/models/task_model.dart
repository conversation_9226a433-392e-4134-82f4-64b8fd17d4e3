import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'task_model.g.dart';

@JsonSerializable(explicitToJson: true)
class TaskModel {
  final String status;
  final String error;
  final int code;
  final TaskData data;

  TaskModel({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory TaskModel.fromJson(Map<String, dynamic> json) => _$TaskModelFromJson(json);
  Map<String, dynamic> toJson() => _$TaskModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class TaskData {
  final int id;
  final String name;
  final Project project;
    final List<Employee> employees;

  final String? details;
  final String? notes;
  @JsonKey(name: 'start_date')
  final String startDate;
  final String deadline;
  final String status;
  final List<String> files;

  TaskData({
    required this.id,
    required this.name,
    required this.project,
    required this.employees,
    this.details,
    this.notes,
    required this.startDate,
    required this.deadline,
    required this.status,
    required this.files,
  });

  factory TaskData.fromJson(Map<String, dynamic> json) => _$TaskDataFromJson(json);
  Map<String, dynamic> toJson() => _$TaskDataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Project {
  final int id;
  final String name;
  final Client client;
    final List<Employee> employees;

  @JsonKey(name: 'start_date')
  final String startDate;
  final String deadline;
  final String status;
  final List<ProjectTask> tasks;

  Project({
    required this.id,
    required this.name,
    required this.client,
    required this.employees,
    required this.startDate,
    required this.deadline,
    required this.status,
    required this.tasks,
  });

  factory Project.fromJson(Map<String, dynamic> json) => _$ProjectFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectToJson(this);
}

@JsonSerializable()
class Client {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String gender;
  final String country;
  final String? employee;
  final String type;
  final String image;

  Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.gender,
    required this.country,
    this.employee,
    required this.type,
    required this.image,
  });

  factory Client.fromJson(Map<String, dynamic> json) => _$ClientFromJson(json);
  Map<String, dynamic> toJson() => _$ClientToJson(this);
}

@JsonSerializable()
class ProjectTask {
  final int id;
  final String name;
  final String project;
    final List<Employee> employees;

  @JsonKey(name: 'start_date')
  final String startDate;
  final String deadline;
  final String status;

  ProjectTask({
    required this.id,
    required this.name,
    required this.project,
    required this.employees,
    required this.startDate,
    required this.deadline,
    required this.status,
  });

  factory ProjectTask.fromJson(Map<String, dynamic> json) => _$ProjectTaskFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectTaskToJson(this);
}
