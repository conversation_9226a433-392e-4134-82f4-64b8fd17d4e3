// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskModel _$TaskModelFromJson(Map<String, dynamic> json) => TaskModel(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: TaskData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$TaskModelToJson(TaskModel instance) => <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data.toJson(),
    };

TaskData _$TaskDataFromJson(Map<String, dynamic> json) => TaskData(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      project: Project.fromJson(json['project'] as Map<String, dynamic>),
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      details: json['details'] as String?,
      notes: json['notes'] as String?,
      startDate: json['start_date'] as String,
      deadline: json['deadline'] as String,
      status: json['status'] as String,
      files: (json['files'] as List<dynamic>).map((e) => e as String).toList(),
    );

Map<String, dynamic> _$TaskDataToJson(TaskData instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'project': instance.project.toJson(),
      'employees': instance.employees.map((e) => e.toJson()).toList(),
      'details': instance.details,
      'notes': instance.notes,
      'start_date': instance.startDate,
      'deadline': instance.deadline,
      'status': instance.status,
      'files': instance.files,
    };

Project _$ProjectFromJson(Map<String, dynamic> json) => Project(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      client: Client.fromJson(json['client'] as Map<String, dynamic>),
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: json['start_date'] as String,
      deadline: json['deadline'] as String,
      status: json['status'] as String,
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => ProjectTask.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProjectToJson(Project instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'client': instance.client.toJson(),
      'employees': instance.employees.map((e) => e.toJson()).toList(),
      'start_date': instance.startDate,
      'deadline': instance.deadline,
      'status': instance.status,
      'tasks': instance.tasks.map((e) => e.toJson()).toList(),
    };

Client _$ClientFromJson(Map<String, dynamic> json) => Client(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      gender: json['gender'] as String,
      country: json['country'] as String,
      employee: json['employee'] as String?,
      type: json['type'] as String,
      image: json['image'] as String,
    );

Map<String, dynamic> _$ClientToJson(Client instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'gender': instance.gender,
      'country': instance.country,
      'employee': instance.employee,
      'type': instance.type,
      'image': instance.image,
    };

ProjectTask _$ProjectTaskFromJson(Map<String, dynamic> json) => ProjectTask(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      project: json['project'] as String,
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: json['start_date'] as String,
      deadline: json['deadline'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$ProjectTaskToJson(ProjectTask instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'project': instance.project,
      'employees': instance.employees,
      'start_date': instance.startDate,
      'deadline': instance.deadline,
      'status': instance.status,
    };
