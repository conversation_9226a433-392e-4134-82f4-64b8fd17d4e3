import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_wiget.dart';
import 'package:erp/features/emplyee%20flow/task%20details/bloc/cubit/task_details_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class TaskDetailsScreen extends StatelessWidget {
  const TaskDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final taskDetailsCubit = context.read<TaskDetailsCubit>();
    return BlocBuilder<TaskDetailsCubit, TaskDetailsState>(
      buildWhen: (previous, current) =>
          current is GetProjectsLoadingState ||
          current is GetProjectsSuccessState ||
          current is GetProjectsErrorState,
      builder: (context, state) {
        if (state is GetProjectsLoadingState) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                centerTitle: false,
                title: Text("Task "),
              ),
              body: SingleChildScrollView(
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProjectCard(
                      projectInfo: [
                        {"title": "Project Name", "value": "ERP"},
                        {
                          "title": "Task name",
                          "value": "splash & Onboarding Screen "
                        },
                        {"title": "Date received", "value": "12/02/2025"},
                        {"title": "Closing date ", "value": "12/02/2025"},
                      ],
                      details:
                          "Undergoing surgery that requires recovery time.",
                      isProject: false,
                      comment:
                          "Undergoing surgery that requires recovery time.",
                      isMyStausCompleted: true,
                      status: "Not completed",
                      isCompleted: false,
                      myStatus: "To Do",
                      files: ["File1.pdf", "File2.docx"],
                    ),
                    18.verticalSpace,
                  ],
                ),
              ),
              bottomNavigationBar: SafeArea(
                minimum: EdgeInsets.all(20.sp),
                child: CustomButtonWidget(
                  color: AppColors.primaryColor900,
                  text: "Completed",
                  onPressed: () {
                    if (true) {
                      showSuccessBottomSheet(context);
                    }
                  },
                ),
              ),
            ),
          );
        }
        return Scaffold(
            appBar: AppBar(
              centerTitle: false,
              title: Text("Task "),
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ProjectCard(
                    projectInfo: [
                      {
                        "title": "Project Name",
                        "value": taskDetailsCubit.model!.data.project.name
                      },
                      {
                        "title": "Task name",
                        "value": taskDetailsCubit.model!.data.name
                      },
                      {
                        "title": "Date received",
                        "value": taskDetailsCubit.model!.data.startDate
                      },
                      {
                        "title": "Closing date ",
                        "value": taskDetailsCubit.model!.data.deadline
                      },
                    ],
                    details: taskDetailsCubit.model!.data.details ?? "",
                    isMyStausCompleted:
                        taskDetailsCubit.model!.data.status == "completed",
                    status: taskDetailsCubit.model!.data.status,
                    isCompleted:
                        taskDetailsCubit.model!.data.status == "completed",
                    files: taskDetailsCubit.model!.data.files,
                  ),
                  18.verticalSpace,
                ],
              ),
            ),
            bottomNavigationBar: taskDetailsCubit.model!.data.status !=
                    "completed"
                ? BlocListener<TaskDetailsCubit, TaskDetailsState>(
                    listener: (context, state) {
                      if (state is UpdateStatusSuccessState) {
                        if (taskDetailsCubit.model!.data.status != "pending") {
                          showSuccessBottomSheet(context);
                        } else {
                          context.pop();
                        }
                      }
                    },
                    child: SafeArea(
                      minimum: EdgeInsets.all(20.sp),
                      child: CustomButtonWidget(
                        color: taskDetailsCubit.model!.data.status != "pending"
                            ? AppColors.primaryColor900
                            : AppColors.greenColor200,
                        text: taskDetailsCubit.model!.data.status != "pending"
                            ? "Completed"
                            : "Started",
                        onPressed: () {
                          taskDetailsCubit.updateStatus();
                        },
                      ),
                    ),
                  )
                : SizedBox.shrink());
      },
    );
  }
}

void showSuccessBottomSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    isDismissible: false,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return PopScope(
        canPop: false,
        child: Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            padding: EdgeInsets.all(20.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                ImagesWidget(image: Assets.assetsImagesSvgsTaskFinshIcon),
                SizedBox(height: 10.h),
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                          text: "Congratulation! ",
                          style: Styles.featureEmphasis.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor800,
                          )),
                      TextSpan(
                        text: "Your Request Completed",
                        style: Styles.featureEmphasis.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 10.h),
                Text(
                  "Your Request Has Been Successfully Completed. Thank You For Your Patience",
                  textAlign: TextAlign.center,
                  style: Styles.featureEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: AppColors.neutralColor600,
                  ),
                ),
                SizedBox(height: 32.h),
                CustomButtonWidget(
                  text: "Tasks",
                  onPressed: () {
                    context.pop();
                    context.pop();
                  },
                ),
                SizedBox(height: 10.h),
              ],
            ),
          ),
        ),
      );
    },
  );
}
