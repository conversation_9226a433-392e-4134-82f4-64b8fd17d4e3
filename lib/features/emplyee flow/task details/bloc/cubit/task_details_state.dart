part of 'task_details_cubit.dart';

sealed class TaskDetailsState {}

final class ProjectsInitial extends TaskDetailsState {}

final class GetProjectsLoadingState extends TaskDetailsState {}

final class GetProjectsErrorState extends TaskDetailsState {}

final class GetProjectsSuccessState extends TaskDetailsState {}



final class UpdateStatusLoadingState extends TaskDetailsState {}

final class UpdateStatusErrorState extends TaskDetailsState {}

final class UpdateStatusSuccessState extends TaskDetailsState {}
