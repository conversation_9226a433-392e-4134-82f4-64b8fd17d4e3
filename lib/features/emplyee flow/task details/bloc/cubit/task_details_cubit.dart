import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/models/task_model.dart';
import 'package:erp/features/emplyee%20flow/task%20details/data/repo/task_details_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'task_details_state.dart';

class TaskDetailsCubit extends Cubit<TaskDetailsState> {
  TaskDetailsCubit(this._homeRepository) : super(ProjectsInitial());
  final TaskDetailsRepository _homeRepository;
  TaskModel? model;
  Future<void> getTaskDetails({required int id}) async {
    emit(GetProjectsLoadingState());

    final result = await _homeRepository.getTaskDetails(id: id);
    result.when(success: (data) {
      model = data;
      emit(GetProjectsSuccessState());
    }, failure: (errorHandler) {
      emit(GetProjectsErrorState());
    });
  }

  Future<void> updateStatus() async {
    emit(UpdateStatusLoadingState());
    showLoading();
    final result = await _homeRepository.updateStatus(
        id: model!.data.id,
        status: model!.data.status == "pending" ? "in_progress" : "completed");
    result.when(success: (data) {
      hideLoading();
      emit(UpdateStatusSuccessState());
    }, failure: (errorHandler) {
      hideLoading();
      emit(UpdateStatusErrorState());
    });
  }
}
