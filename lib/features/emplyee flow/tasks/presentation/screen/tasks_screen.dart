import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AllTasksScreen extends StatelessWidget {
  const AllTasksScreen({super.key, required this.tasks});
  final List<Task> tasks;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // leading: Icon(Icons.notifications_none_rounded),
        title: Text('homeEmployee.projectTasks'.tr()),
      ),
      body: ListView.separated(
        itemCount: tasks.length,
        padding: EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
        separatorBuilder: (BuildContext context, int index) {
          return 16.sp.verticalSpace;
        },
        itemBuilder: (BuildContext context, int index) {
          return ProjectsItemWidget(
              onTap: () {
                // context.pushNamed(Routes.taskDetailsScreen,
                //     );
                context.pushNamed(Routes.taskDetailsScreen,
                    arguments: tasks[index].id);
              },
              title: tasks[index].name,
              description: tasks[index].startDate);
        },
      ),
    );
  }
}
