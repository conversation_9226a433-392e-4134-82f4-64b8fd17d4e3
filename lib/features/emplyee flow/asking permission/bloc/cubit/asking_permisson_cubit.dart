import 'dart:io';

import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_state.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/model/request_model.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/repo/permison_repo.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AskingPermissonCubit extends Cubit<AskingPermsionState> {
  AskingPermissonCubit(this._askingPermsionRepository)
      : super(AskingPermsionInitial()) {
    setupScrollController();
  }
  final AskingPermsionRepository _askingPermsionRepository;
  List<Request> pendingList = [];
  List<Request> approvedList = [];
  List<Request> rejectedList = [];
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;
  final ScrollController scrollController = ScrollController();
  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 100 &&
          !isLoadingMore) {
        loadMoreDepartments();
      }
    });
  }

  Future<void> loadMoreDepartments() async {
    if (isLoadingMore || currentPage >= lastPage) {
      return;
    }
    isLoadingMore = true;
    currentPage++;
    emit(GetAskingPermsionsLoadingMoreState());
    final result = await _askingPermsionRepository.getAskingPermsionsRequest(
        page: currentPage);

    result.when(
      success: (data) {
        for (var e in data.data.data) {
          if (e.status == 'pending') {
            pendingList.add(e);
          } else if (e.status == 'approved') {
            approvedList.add(e);
          } else {
            rejectedList.add(e);
          }
        }
        currentPage = data.data.meta.currentPage;
        emit(GetAskingPermsionsSuccessState());
      },
      failure: (error) {
        emit(GetAskingPermsionsErrorState());
      },
    );

    isLoadingMore = false;
  }

  Future<void> getAskingPermsions() async {
    pendingList = [];
    approvedList = [];
    rejectedList = [];
    currentPage = 1;
    lastPage = 1;
    emit(GetAskingPermsionsLoadingState());
    final result = await _askingPermsionRepository.getAskingPermsionsRequest(
        page: currentPage);
    result.when(success: (model) {
      for (var e in model.data.data) {
        if (e.status == 'pending') {
          pendingList.add(e);
        } else if (e.status == 'approved') {
          approvedList.add(e);
        } else {
          rejectedList.add(e);
        }
      }
      currentPage = model.data.meta.currentPage;
      lastPage = model.data.meta.lastPage;
      emit(GetAskingPermsionsSuccessState());
    }, failure: (_) {
      emit(GetAskingPermsionsErrorState());
    });
  }

  String removeSeconds(String timeString) {
    List<String> parts = timeString.split(':');
    return '${parts[0]}:${parts[1]}';
  }

  final TextEditingController dateController = TextEditingController();
  final TextEditingController timeFromController = TextEditingController();
  final TextEditingController timeToController = TextEditingController();

  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();

  File? selectedFile;

  Future<void> pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      selectedFile = File(result.files.single.path!);

      emit(PickImageSuccessState());
    } else {
      emit(PickImageErrorState());
    }
  }

  Future<void> postAskingPermsionRequest() async {
    emit(SubmitAskingPermsionLoadingState());
    showLoading();
    final result = await _askingPermsionRepository.postAskingPermsionRequest(
        reason: reasonController.text,
        file: selectedFile,
        date: dateController.text,
        timeFrom: timeFromController.text,
        timeTo: timeToController.text);

    result.when(
      success: (_) async {
        hideLoading();
        await getAskingPermsions();
        emit(SubmitAskingPermsionSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(SubmitAskingPermsionErrorState());
      },
    );
  }

  Future<void> updateAskingPermsionRequest({required int requestId}) async {
    emit(UpdateAskingPermsionLoadingState());
    showLoading();
    final result =
        await _askingPermsionRepository.updateAskingPermsionRequesteRequest(
            requestId: requestId,
            reason: reasonController.text,
            file: selectedFile,
            date: dateController.text,
            timeFrom: timeFromController.text,
            timeTo: timeToController.text);

    result.when(success: (model) async {
      pendingList[
          pendingList.indexWhere((element) => element.id == requestId)] = model;
      hideLoading();
      emit(UpdateAskingPermsionSuccessState());
    }, failure: (error) {
      hideLoading();

      emit(UpdateAskingPermsionErrorState());
    });
  }

  void clearData() {
    dateController.clear();
    reasonController.clear();
    selectedFile = null;
    timeFromController.clear();
    timeToController.clear();
  }

  bool isImageFile(String path) {
    final List<String> imageExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'webp'
    ];
    String extension = path.split('.').last.toLowerCase();
    return imageExtensions.contains(extension);
  }
}
