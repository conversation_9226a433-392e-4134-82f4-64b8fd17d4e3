abstract class AskingPermsionState {}

final class AskingPermsionInitial extends AskingPermsionState {}

final class GetAskingPermsionsSuccessState extends AskingPermsionState {}

final class GetAskingPermsionsLoadingState extends AskingPermsionState {}

final class GetAskingPermsionsErrorState extends AskingPermsionState {}

final class PickImageSuccessState extends AskingPermsionState {}

final class PickImageErrorState extends AskingPermsionState {}

final class SubmitAskingPermsionLoadingState extends AskingPermsionState {}

final class SubmitAskingPermsionSuccessState extends AskingPermsionState {}

final class SubmitAskingPermsionErrorState extends AskingPermsionState {}

class UpdateAskingPermsionLoadingState extends AskingPermsionState {}

class UpdateAskingPermsionSuccessState extends AskingPermsionState {}

class UpdateAskingPermsionErrorState extends AskingPermsionState {}

class GetAskingPermsionsLoadingMoreState extends AskingPermsionState {}
