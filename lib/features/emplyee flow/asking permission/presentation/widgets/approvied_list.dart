import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/no_permisonn_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ApproviedList extends StatelessWidget {
  const ApproviedList({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AskingPermissonCubit>();

    return cubit.approvedList.isNotEmpty
        ? ListView.separated(
            controller: cubit.scrollController,
            padding: EdgeInsets.symmetric(
              horizontal: 16.sp,
            ),
            separatorBuilder: (context, index) {
              return 16.verticalSpace;
            },
            itemCount: cubit.approvedList.length,
            itemBuilder: (context, index) {
              final request = cubit.approvedList[index];

              return ProjectsItemWidget(
                  onTap: () {
                    context.pushNamed(
                        Routes.permissionRejectionOrAcceptingScreen,
                        arguments: request);
                  },
                  title: request.reason,
                  description: request.date);
            },
          )
        : NoPermisonnWidget();
  }
}
