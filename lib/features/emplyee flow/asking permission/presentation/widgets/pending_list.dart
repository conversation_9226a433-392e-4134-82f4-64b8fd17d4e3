import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_state.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/screens/asking_pemission_pending.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/no_permisonn_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';

class PendingList extends StatelessWidget {
  const PendingList({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AskingPermissonCubit>();
    return cubit.pendingList.isNotEmpty
        ? BlocBuilder<AskingPermissonCubit, AskingPermsionState>(
            buildWhen: (previous, current) =>
                current is UpdateAskingPermsionSuccessState,
            builder: (context, state) {
              return ListView.separated(
                controller: cubit.scrollController,
                padding: EdgeInsets.symmetric(
                  horizontal: 16.sp,                  
                ),
                separatorBuilder: (context, index) {
                  return 16.verticalSpace;
                },
                itemCount: cubit.pendingList.length,
                itemBuilder: (context, index) {
                  final request = cubit.pendingList[index];

                  return ProjectsItemWidget(
                      onTap: () {
                        Navigator.of(context).push(PageTransition(
                          type: PageTransitionType.fade,
                          child: BlocProvider.value(
                            value: cubit,
                            child: PermissionPendingScreen(
                              model: request,
                            ),
                          ),
                        ));
                      },
                      title: request.reason,
                      description: request.date);
                },
              );
            },
          )
        : NoPermisonnWidget();
  }
}
