import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void showPermisonnBottonSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return PopScope(
        canPop: false,
        child: Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            padding: EdgeInsets.all(20.sp),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                
                ImagesWidget(image: Assets.assetsImagesSvgsTaskFinshIcon),
                SizedBox(height: 10.h),

                
                RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    children: [
                      TextSpan(
                          text: "successfully!",
                          style: Styles.featureEmphasis.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.primaryColor800,
                          )),
                      TextSpan(
                        text: "Your AskingPermisson request has been sent",
                        style: Styles.featureEmphasis.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 10.h),

                
                Text(
                  "You can track your AskingPermisson request, and you will receive a response soon",
                  textAlign: TextAlign.center,
                  style: Styles.featureEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                    fontSize: 14.sp,
                    color: AppColors.neutralColor600,
                  ),
                ),
                SizedBox(height: 32.h),

                CustomButtonWidget(
                    text: "tracking",
                    onPressed: () {
                      context.pop();
                      context.pop();
                    }),

                SizedBox(height: 10.h),
              ],
            ),
          ),
        ),
      );
    },
  );
}
