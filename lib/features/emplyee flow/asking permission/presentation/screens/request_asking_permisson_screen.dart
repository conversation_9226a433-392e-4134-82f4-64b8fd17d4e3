import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/date_formate.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_state.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/permsion_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RequestPermissionScreen extends StatelessWidget {
  const RequestPermissionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AskingPermissonCubit>();

    return Scaffold(
      appBar: AppBar(
        title: Text('absence.askingPermisson'.tr()),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp,
          vertical: 18.sp,
        ),
        child: Form(
          key: cubit.formKey,
          child: Column(
            spacing: 12.sp,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('absence.timefrom'.tr(), style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.timeFromController,
                hintText: 'absence.timefrom'.tr(),
                readOnly: true,
                onTap: () {
                  showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.now(),
                  ).then((v) {
                    if (v != null) {
                      cubit.timeFromController.text = formatTime(v);
                    }
                  });
                },
              ),
              Text('absence.timeto'.tr(), style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.timeToController,
                hintText: 'absence.timeto'.tr(),
                readOnly: true,
                onTap: () {
                  showTimePicker(
                    context: context,
                    initialTime: TimeOfDay.now(),
                  ).then((v) {
                    if (v != null) {
                      cubit.timeToController.text = formatTime(v);
                    }
                  });
                },
              ),
              Text('absence.date'.tr(), style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.dateController,
                hintText: 'absence.date'.tr(),
                readOnly: true,
                suffixIcon: Padding(
                  padding: EdgeInsets.all(8.0.sp),
                  child: ImagesWidget(
                      height: 30.sp,
                      image: Assets.assetsImagesSvgsCalenderIconTextFormField),
                ),
                onTap: () {
                  showDatePicker(
                    context: context,
                    firstDate: DateTime(DateTime.now().year,
                        DateTime.now().month - 2, DateTime.now().day),
                    initialDate: DateTime.now(),
                    lastDate: DateTime(2100),
                  ).then((v) {
                    if (v != null) {
                      cubit.dateController.text = formatDate(v.toString());
                    }
                  });
                },
              ),
              Text('absence.image'.tr(),
                  style:
                      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600)),
              InkWell(
                onTap: () {
                  cubit.pickFile();
                },
                child: Container(
                  height: 102.sp,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.neutralColor600),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: BlocBuilder<AskingPermissonCubit, AskingPermsionState>(
                    buildWhen: (previous, current) =>
                        current is PickImageErrorState ||
                        current is PickImageSuccessState,
                    builder: (context, state) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (cubit.selectedFile == null)
                            ImagesWidget(image: Assets.assetsImagesSvgsGallery)
                          else if (cubit.isImageFile(cubit.selectedFile!.path))
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Image.file(
                                  cubit.selectedFile!,
                                ),
                              ),
                            )
                          else
                            Text(
                              cubit.selectedFile!.path.split('/').last,
                              style: Styles.captionEmphasis
                                  .copyWith(color: AppColors.primaryColor800),
                            ),
                          SizedBox(height: 8.sp),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'absence.askingPermisson'.tr(),
                                style: Styles.captionEmphasis
                                    .copyWith(color: AppColors.neutralColor600),
                              ),
                              Icon(Icons.file_upload_outlined,
                                  color: AppColors.neutralColor600),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Text('absence.askingPermisson'.tr(),
                  style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.reasonController,
                height: 80.sp,
                maxLines: 10,
                isChat: true,
                hintText: 'absence.askingPermisson'.tr(),
                textAlignVertical: TextAlignVertical.top,
                contentPadding: EdgeInsets.only(
                  top: 15.h,
                  left: 10.w,
                  right: 10.w,
                  bottom: 80.sp,
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(12.sp),
        child: BlocListener<AskingPermissonCubit, AskingPermsionState>(
          listenWhen: (previous, current) =>
              current is SubmitAskingPermsionLoadingState ||
              current is SubmitAskingPermsionSuccessState ||
              current is SubmitAskingPermsionErrorState,
          listener: (context, state) {
            if (state is SubmitAskingPermsionSuccessState) {
              showPermisonnBottonSheet(context);
            }
          },
          child: CustomButtonWidget(
            text: 'absence.askingPermisson'.tr(),
            onPressed: () {
              if (cubit.formKey.currentState!.validate()) {
                cubit.postAskingPermsionRequest();
              }
            },
          ),
        ),
      ),
    );
  }
}
