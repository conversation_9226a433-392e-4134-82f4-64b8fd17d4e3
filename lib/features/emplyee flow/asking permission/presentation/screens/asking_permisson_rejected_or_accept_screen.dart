import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_state.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/model/request_model.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/permsion_bottom_sheet.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/rejected_and_accetp_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class PermissionRejectionOrAcceptScreen extends StatelessWidget {
  const PermissionRejectionOrAcceptScreen(
      {super.key, required this.isReject, required this.model});
  final bool isReject;
  final Request model;
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AskingPermissonCubit>();
    cubit.dateController.text = model.date;
    cubit.reasonController.text = model.reason;
    cubit.timeFromController.text = cubit.removeSeconds(model.timeFrom);
    cubit.timeToController.text = cubit.removeSeconds(model.timeTo);
    return Scaffold(
      appBar: AppBar(
        title: Text(
          !isReject
              ? 'absence.absenceRejection'.tr()
              : 'absence.absenceAccept'.tr(),
        ),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp,
          vertical: 18.sp,
        ),
        child: Column(
          spacing: 12.sp,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AskingPermissonWidget(
              projectInfo: [
                {
                  "title": 'absence.from'.tr(),
                  "value": cubit.removeSeconds(model.timeFrom)
                },
                {
                  "title": 'absence.to'.tr(),
                  "value": cubit.removeSeconds(model.timeTo)
                },
                {"title": 'absence.requestedOn'.tr(), "value": model.date},
              ],
              isCompleted: isReject,
              reason: model.reason,
              status: model.status,
              rejectonReason: model.rejectReason,
            ),
          ],
        ),
      ),
      bottomNavigationBar: isReject
          ? SizedBox.shrink()
          : SafeArea(
              minimum: EdgeInsets.all(12.sp),
              child: BlocListener<AskingPermissonCubit, AskingPermsionState>(
                listenWhen: (previous, current) =>
                    current is SubmitAskingPermsionErrorState ||
                    current is SubmitAskingPermsionSuccessState ||
                    current is SubmitAskingPermsionLoadingState,
                listener: (context, state) {
                  if (state is SubmitAskingPermsionSuccessState) {
                    showPermisonnBottonSheet(context);
                  }
                },
                child: CustomButtonWidget(
                  text: 'absence.resend'.tr(),
                  onPressed: () {
                    cubit.postAskingPermsionRequest();
                  },
                ),
              ),
            ),
    );
  }
}
