import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/absence_skelation_widget.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_cubit.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/bloc/cubit/asking_permisson_state.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/screens/request_asking_permisson_screen.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/approvied_list.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/pending_list.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/rejected_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';

class AskingPermissonScreen extends StatelessWidget {
  const AskingPermissonScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AskingPermissonCubit>();

    return BlocBuilder<AskingPermissonCubit, AskingPermsionState>(
      buildWhen: (previous, current) =>
          current is GetAskingPermsionsErrorState ||
          current is GetAskingPermsionsSuccessState ||
          current is GetAskingPermsionsLoadingState,
      builder: (context, state) {
        if (state is GetAskingPermsionsLoadingState) {
          return AbsenceSkeletonizer();
        }
        return Scaffold(
          appBar: AppBar(
            title: Text('moreSales.askingpermisson'.tr()),
            centerTitle: false,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 15.sp,
            ),
            child: DefaultTabController(
              length: 3,
              child: Column(
                spacing: 20.sp,
                children: [
                  TabBar(
                    dividerHeight: 0.h,
                    unselectedLabelColor: AppColors.neutralColor600,
                    labelStyle: Styles.highlightEmphasis.copyWith(
                      color: AppColors.primaryColor800,
                    ),
                    unselectedLabelStyle: Styles.highlightEmphasis.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                    indicatorColor: AppColors.primaryColor800,
                    dividerColor: AppColors.neutralColor300,
                    automaticIndicatorColorAdjustment: true,
                    indicatorSize: TabBarIndicatorSize.tab,
                    tabs: [
                      Tab(text: 'moreSales.reject'.tr()),
                      Tab(text: 'moreSales.accept'.tr()),
                      Tab(text: 'moreSales.Pending'.tr()),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        RejectedList(),
                        ApproviedList(),
                        PendingList(),
                      ],
                    ),
                  ),
                  BlocBuilder<AskingPermissonCubit, AskingPermsionState>(
                    buildWhen: (previous, current) =>
                        current is GetAskingPermsionsLoadingMoreState ||
                        current is GetAskingPermsionsSuccessState,
                    builder: (context, state) {
                      if (state is GetAskingPermsionsLoadingMoreState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  )
                ],
              ),
            ),
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 16.sp),
            child: CustomButtonWidget(
              text: 'moreSales.requestAskingpermission'.tr(),
              onPressed: () {
                Navigator.of(context).push(
                  PageTransition(
                    type: PageTransitionType.fade,
                    child: BlocProvider.value(
                      value: cubit..clearData(),
                      child: RequestPermissionScreen(),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
