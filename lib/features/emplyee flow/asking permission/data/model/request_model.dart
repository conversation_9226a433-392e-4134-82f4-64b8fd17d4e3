import 'package:json_annotation/json_annotation.dart';

part 'request_model.g.dart';

@JsonSerializable()
class Request {
  final int id;
  @Json<PERSON>ey(name: 'time_from')
  final String timeFrom;
  @Json<PERSON>ey(name: 'time_to')
  final String timeTo;
  final String date;
  final String reason;
  final dynamic attachments;
  final String status;
  @J<PERSON><PERSON>ey(name: 'reject_reason')
  final dynamic rejectReason;
  final dynamic manager;
  final String employee;
  @<PERSON>son<PERSON>ey(name: 'employee_id')
  final int employeeId;
  final String email;

  Request({
    required this.id,
    required this.timeFrom,
    required this.timeTo,
    required this.date,
    required this.reason,
    this.attachments,
    required this.status,
    this.rejectReason,
    this.manager,
    required this.employee,
    required this.employeeId,
    required this.email,
  });

  factory Request.fromJson(Map<String, dynamic> json) =>
      _$RequestFromJson(json);
  Map<String, dynamic> toJson() => _$RequestToJson(this);
}

@JsonSerializable()
class Meta {
  @<PERSON><PERSON><PERSON>ey(name: 'current_page')
  final int currentPage;
  @J<PERSON><PERSON><PERSON>(name: 'last_page')
  final int lastPage;

  Meta({
    required this.currentPage,
    required this.lastPage,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);
  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class DataWrapper {
  final List<Request> data;
  final Meta meta;

  DataWrapper({
    required this.data,
    required this.meta,
  });

  factory DataWrapper.fromJson(Map<String, dynamic> json) =>
      _$DataWrapperFromJson(json);
  Map<String, dynamic> toJson() => _$DataWrapperToJson(this);
}

@JsonSerializable()
class AskingPermsionModel {
  final DataWrapper data;
  final String status;
  final String error;
  final int code;

  AskingPermsionModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory AskingPermsionModel.fromJson(Map<String, dynamic> json) =>
      _$AskingPermsionModelFromJson(json);
  Map<String, dynamic> toJson() => _$AskingPermsionModelToJson(this);
}
