// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'request_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Request _$RequestFromJson(Map<String, dynamic> json) => Request(
      id: (json['id'] as num).toInt(),
      timeFrom: json['time_from'] as String,
      timeTo: json['time_to'] as String,
      date: json['date'] as String,
      reason: json['reason'] as String,
      attachments: json['attachments'],
      status: json['status'] as String,
      rejectReason: json['reject_reason'],
      manager: json['manager'],
      employee: json['employee'] as String,
      employeeId: (json['employee_id'] as num).toInt(),
      email: json['email'] as String,
    );

Map<String, dynamic> _$RequestToJson(Request instance) => <String, dynamic>{
      'id': instance.id,
      'time_from': instance.timeFrom,
      'time_to': instance.timeTo,
      'date': instance.date,
      'reason': instance.reason,
      'attachments': instance.attachments,
      'status': instance.status,
      'reject_reason': instance.rejectReason,
      'manager': instance.manager,
      'employee': instance.employee,
      'employee_id': instance.employeeId,
      'email': instance.email,
    };

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
      currentPage: (json['current_page'] as num).toInt(),
      lastPage: (json['last_page'] as num).toInt(),
    );

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'last_page': instance.lastPage,
    };

DataWrapper _$DataWrapperFromJson(Map<String, dynamic> json) => DataWrapper(
      data: (json['data'] as List<dynamic>)
          .map((e) => Request.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DataWrapperToJson(DataWrapper instance) =>
    <String, dynamic>{
      'data': instance.data,
      'meta': instance.meta,
    };

AskingPermsionModel _$AskingPermsionModelFromJson(Map<String, dynamic> json) =>
    AskingPermsionModel(
      data: DataWrapper.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$AskingPermsionModelToJson(
        AskingPermsionModel instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };
