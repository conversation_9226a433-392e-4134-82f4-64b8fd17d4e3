import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/api_services/api_services.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/data/model/request_model.dart';

class AskingPermsionRepository {
  final AskingPermsionApiServices _leaveApiServices;
  AskingPermsionRepository(this._leaveApiServices);

  Future<ApiResult<AskingPermsionModel>> getAskingPermsionsRequest(
      {required int page}) async {
    final response =
        await _leaveApiServices.getAskingPermsionsRequest(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        AskingPermsionModel model = AskingPermsionModel.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<void>> postAskingPermsionRequest({
    required String date,
    required String reason,
    required String timeFrom,
    required String timeTo,
    File? file,
  }) async {
    try {
      final response = await _leaveApiServices.postAskingPermsionRequest(
        date: date,
        reason: reason,
        file: file,
        timeFrom: timeFrom,
        timeTo: timeTo,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        return const ApiResult.success(null);
      } else {
        return ApiResult.failure(
            ServerException.fromResponse(response?.statusCode, response));
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    }
  }

  Future<ApiResult<Request>> updateAskingPermsionRequesteRequest({
    required int requestId,
    required String date,
    required String reason,
    required String timeFrom,
    required String timeTo,
    File? file,
  }) async {
    try {
      final response = await _leaveApiServices.updateAskingPermsionRequest(
        requestId: requestId,
        reason: reason,
        file: file,
        date: date,
        timeFrom: timeFrom,
        timeTo: timeTo,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        Request model = Request.fromJson(response.data["data"]);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    } catch (e) {
      return ApiResult.failure(
          FailureException(errMessage: 'Unexpected error occurred'));
    }
  }
}
