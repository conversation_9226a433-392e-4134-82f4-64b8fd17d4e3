import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class AskingPermsionApiServices {
  AskingPermsionApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> getAskingPermsionsRequest({required int page}) async {
    return _dioFactory.get(endPoint: EndPoints.getAskingPermsionRequest, data: {
      'page': page,
    });
  }

  Future<Response?> postAskingPermsionRequest({
    required String date,
    required String reason,
    required String timeFrom,
    required String timeTo,
    File? file,
  }) async {
    final formData = FormData.fromMap({
      'time_from': timeFrom,
      'time_to': timeTo,
      'date': date,
      'reason': reason,
      if (file != null)
        'attachments': [
          await MultipartFile.fromFile(file.path,
              filename: file.path.split('/').last),
        ],
    });

    return _dioFactory.post(
      endPoint: EndPoints.createAskingPermsionRequest,
      data: formData,
    );
  }

  Future<Response?> updateAskingPermsionRequest({
    required int requestId,
    required String date,
    required String reason,
    required String timeFrom,
    required String timeTo,
    File? file,
  }) async {
    final formData = FormData.fromMap({
      'time_from': timeFrom,
      'time_to': timeTo,
      'date': date,
      'reason': reason,
      if (file != null)
        'attachments': [
          await MultipartFile.fromFile(file.path,
              filename: file.path.split('/').last),
        ],
    });

    return _dioFactory.post(
      endPoint: '${EndPoints.updateAskingPermsionRequest}/$requestId',
      data: formData,
    );
  }
}
