import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProjectsItemWidget extends StatelessWidget {
  const ProjectsItemWidget(
      {super.key, this.onTap, required this.title, required this.description});
  final VoidCallback? onTap;
  final String title;
  final String description;
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: AppConstants.screenWidth(context) / 2,
        padding: EdgeInsets.symmetric(
          horizontal: 8.sp,
          vertical: 12.sp,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            width: 1.sp,
            color: AppColors.primaryColor800,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    // "Project Name",
                    title,
                    style: Styles.highlightStandard,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    // maxLines: 1,
                  ),
                  SizedBox(height: 8.sp),
                  Text(
                    description,
                    style: Styles.highlightEmphasis.copyWith(
                      color: AppColors.neutralColor600,
                      fontSize: 12.sp,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ],
              ),
            ),
            SizedBox(width: 12.sp),
            Icon(
              size: 20.sp,
              Icons.arrow_forward_ios_outlined,
              color: AppColors.primaryColor800,
            ),
          ],
        ),
      ),
    );
  }
}
