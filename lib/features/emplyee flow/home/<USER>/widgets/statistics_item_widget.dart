import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class StatisticsItemWidget extends StatelessWidget {
  const StatisticsItemWidget(
      {super.key, required this.title, required this.value, this.total});

  final String title;
  final String value;
  final String? total;

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.all(8.sp),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            width: 0.5.sp,
            color: AppColors.primaryColor800,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          spacing: 8.sp,
          children: [
            Text(
              title,
              style: Styles.contentRegular,
            ),
            if (total != null)
              Row(
                children: [
                  Text(
                    value,
                    style: Styles.heading3.copyWith(
                      color: AppColors.primaryColor800,
                    ),
                  ),
                  Text(
                    "/${total!}",
                    style: Styles.featureEmphasis.copyWith(
                      color: AppColors.neutralColor600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            else
              Text(
                value,
                style:
                    Styles.heading2.copyWith(color: AppColors.primaryColor800),
              ),
          ],
        ),
      ),
    );
  }
}
