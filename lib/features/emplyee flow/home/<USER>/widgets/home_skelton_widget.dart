import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/custom_make_attendance_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/home_top_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/policy_item_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/statistics_item_widget.dart';
import 'package:erp/features/sales%20flow/home/<USER>/widgets/sales_widget_skelton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HomeSkeltonWidget extends StatelessWidget {
  const HomeSkeltonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            HomeTopWidget(),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(18.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SalesBannersWidgetSkelton(),
                      16.verticalSpace,
                      CustomMakeAttendanceWidget(),
                      16.verticalSpace,
                      Text(
                        "Statistics of the years",
                        style: Styles.contentBold.copyWith(
                            fontSize: 18.sp, fontWeight: FontWeight.w500),
                      ),
                      12.verticalSpace,
                      Row(
                        spacing: 16.sp,
                        children: [
                          StatisticsItemWidget(
                            title: 'Absence',
                            value: '16',
                            total: "60",
                          ),
                        ],
                      ),
                      18.verticalSpace,
                      Row(
                        children: [
                          Text(
                            "Projects",
                            style: Styles.contentBold.copyWith(
                                fontSize: 18.sp, fontWeight: FontWeight.w500),
                          ),
                          Spacer(),
                          Text(
                            "see More",
                            style: Styles.captionEmphasis.copyWith(
                              color: AppColors.primaryColor800,
                              decoration: TextDecoration.underline,
                              decorationColor: AppColors.primaryColor800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      12.verticalSpace,
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          spacing: 16.sp,
                          children: List.generate(
                            5,
                            (index) {
                              return ProjectsItemWidget(
                                title: "Project Name",
                                description: "Dessssssssssssssssssssssssaaaaaa",
                              );
                            },
                          ),
                        ),
                      ),
                      12.verticalSpace,
                      Text(
                        "Employee Policies & Requests",
                        style: Styles.contentBold.copyWith(
                            fontSize: 18.sp, fontWeight: FontWeight.w600),
                      ),
                      Skeletonizer(
                        enabled: true,
                        child: GridView.builder(
                          gridDelegate:
                              SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 12.w,
                            mainAxisSpacing: 12.h,
                            mainAxisExtent: 130.sp,
                          ),
                          itemCount: 3,
                          shrinkWrap: true,
                          physics: NeverScrollableScrollPhysics(),
                          itemBuilder: (context, index) {
                            return PolicyItemWidget(
                              onTap: () {
                                if (index == 0) {
                                  context.pushNamed(Routes.absenceScreen);
                                }
                                if (index == 1) {
                                  context
                                      .pushNamed(Routes.askingPermissionScreen);
                                }
                              },
                              image: "assets/images/pngs/erp.png",
                              text: "text",
                            );
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
