import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/main%20layout/business_logic/main_layout_cubit.dart';
import 'package:erp/features/emplyee%20flow/projects/bloc/cubit/projects_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProjectWidget extends StatelessWidget {
  const ProjectWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProjectsCubit(getIt())..getProjects(),
      child: <PERSON><PERSON><PERSON>er<ProjectsCubit, ProjectsState>(
        builder: (context, state) {
          if (state is GetProjectsLoadingState) {
            return Skeletonizer(
              enabled: true,
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        'homeEmployee',
                        style: Styles.contentBold.copyWith(
                            fontSize: 18.sp, fontWeight: FontWeight.w500),
                      ),
                      Spacer(),
                      Text(
                        "see More",
                        style: Styles.captionEmphasis.copyWith(
                          color: AppColors.primaryColor800,
                          decoration: TextDecoration.underline,
                          decorationColor: AppColors.primaryColor800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  12.verticalSpace,
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      spacing: 16.sp,
                      children: List.generate(
                        5,
                        (index) {
                          return ProjectsItemWidget(
                            title: "Project Name",
                            description: "Dessssssssssssssssssssssssaaaaaa",
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }
          return Column(
            children: [
              Row(
                children: [
                  Text(
                    'homeEmployee.projects'.tr(),
                    style: Styles.contentBold
                        .copyWith(fontSize: 18.sp, fontWeight: FontWeight.w500),
                  ),
                  Spacer(),
                  TextButton(
                    onPressed: () {
                      MainLayoutCubit.get(context).changeBottomNavBar(1);
                    },
                    child: Text(
                      'homeEmployee.seeMore'.tr(),
                      style: Styles.captionEmphasis.copyWith(
                        color: AppColors.primaryColor800,
                        decoration: TextDecoration.underline,
                        decorationColor: AppColors.primaryColor800,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              12.verticalSpace,
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  spacing: 16.sp,
                  children: List.generate(
                    context.read<ProjectsCubit>().model!.data.data.length < 4
                        ? context.read<ProjectsCubit>().model!.data.data.length
                        : 4,
                    (index) {
                      return ProjectsItemWidget(
                          title: context
                              .read<ProjectsCubit>()
                              .model!
                              .data
                              .data[index]
                              .name,
                          description: context
                              .read<ProjectsCubit>()
                              .model!
                              .data
                              .data[index]
                              .deadline!);
                    },
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
