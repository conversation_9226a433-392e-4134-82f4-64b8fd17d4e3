import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class HomeTopWidget extends StatelessWidget {
  const HomeTopWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 141.sp,
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 24.sp),
      decoration: BoxDecoration(
        color: AppColors.primaryColor800,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(AppConstants.borderRadius),
          bottomRight: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Image.asset(Assets.assetsImagesPngsUserImage),
          8.horizontalSpace,
          Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                spacing: 8.sp,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    'home.goodMorning'.tr(),
                    style: Styles.captionEmphasis
                        .copyWith(fontSize: 12.sp, color: Color(0xff77aad4)),
                  ),
                  Icon(
                    Icons.sunny,
                    color: Colors.amber,
                  ),
                ],
              ),
              Text(
                CacheHelper.getData(key: CacheKeys.userName) ?? "",
                style: Styles.captionEmphasis.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
              )
            ],
          ),
          Spacer(),
          InkWell(
            onTap: () => context.pushNamed(Routes.notificationScreen),
            child: Container(
              padding: EdgeInsets.all(6.sp),
              width: 45.sp,
              height: 45.sp,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                color: AppColors.scaffoldBackground,
              ),
              child: Center(
                child: Badge.count(
                  count: 2,
                  child: SvgPicture.asset(
                    Assets.assetsImagesSvgsNotifcationIcon,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
