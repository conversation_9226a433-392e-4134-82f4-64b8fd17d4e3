import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/check%20in%20&%20checkOut/fucnation/check_in_and_check_out_fucnations.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomMakeAttendanceWidget extends StatefulWidget {
  const CustomMakeAttendanceWidget({super.key});

  @override
  State<CustomMakeAttendanceWidget> createState() =>
      _CustomMakeAttendanceWidgetState();
}

class _CustomMakeAttendanceWidgetState
    extends State<CustomMakeAttendanceWidget> {
  final ValueNotifier<DateTime> _currentTime = ValueNotifier(DateTime.now());

  bool _isCheckedIn =
      CacheHelper.getData(key: CacheKeys.isUserCheckIn) ?? false;
  bool _showGoodMorning = false;
  bool _showBye = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _startClock();
    _loadCheckInStatus();
  }

  void _startClock() {
    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (!mounted) return false;

      final now = DateTime.now();
      _currentTime.value = now;

      // Optional auto-check-out logic at midnight

      if (_isCheckedIn && now.hour == 0 && now.minute == 0 && now.second == 0) {
        if (mounted) {
          setState(() {
            _isCheckedIn = false;
            _showBye = true;
          });
          Future.delayed(const Duration(seconds: 3), () {
            if (mounted) setState(() => _showBye = false);
          });
        }
      }

      return true;
    });
  }

  Future<void> _loadCheckInStatus() async {
    final cachedStatus =
        CacheHelper.getData(key: CacheKeys.isUserCheckIn) as bool? ?? false;
    final lastCheckInDateStr =
        CacheHelper.getData(key: CacheKeys.lastCheckInDate) as String?;

    if (cachedStatus && lastCheckInDateStr != null) {
      final lastDate = DateTime.tryParse(lastCheckInDateStr);
      final now = DateTime.now();

      // Check if the last check-in was today
      if (lastDate != null &&
          lastDate.year == now.year &&
          lastDate.month == now.month &&
          lastDate.day == now.day) {
        // User is properly checked in for today
        setState(() => _isCheckedIn = true);
        return;
      } else {
        // Last check-in was not today, reset status
        await CacheHelper.saveData(key: CacheKeys.isUserCheckIn, value: false);
        await CacheHelper.removeData(key: CacheKeys.lastCheckInDate);
      }
    }

    // Default to not checked in
    setState(() => _isCheckedIn = false);
  }

  String _formatDate(DateTime now) {
    return DateFormat('EEE dd MMM').format(now);
  }

  String _formatTime(DateTime now) {
    final hour = now.hour % 12 == 0 ? 12 : now.hour % 12;
    final minute = now.minute.toString().padLeft(2, '0');
    final second = now.second.toString().padLeft(2, '0');
    final period = now.hour < 12 ? 'am'.tr() : 'pm'.tr();
    return '$hour:$minute:$second $period';
  }

  String _formatTimeForApi(DateTime now) {
    return DateFormat("HH:mm:ss").format(now);
  }

  void _handleCheckIn() async {
    if (_isLoading || _isCheckedIn) return;

    setState(() => _isLoading = true);

    final now = _currentTime.value;

    await checkIn(
      checkInTime: _formatTimeForApi(now),
      onSuccess: () async {
        await CacheHelper.saveData(key: CacheKeys.isUserCheckIn, value: true);
        await CacheHelper.saveData(
            key: CacheKeys.lastCheckInDate, value: now.toIso8601String());

        setState(() {
          _isCheckedIn = true;
          _showGoodMorning = true;
          _showBye = false;
        });

        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) setState(() => _showGoodMorning = false);
        });
      },
      onFailure: () {},
    );

    if (mounted) setState(() => _isLoading = false);
  }

  void _handleCheckOut() async {
    if (_isLoading || !_isCheckedIn) return;

    setState(() => _isLoading = true);

    final now = _currentTime.value;

    await checkOut(
      checkOutTime: _formatTimeForApi(now),
      onSuccess: () async {
        await CacheHelper.saveData(key: CacheKeys.isUserCheckIn, value: false);
        await CacheHelper.removeData(key: CacheKeys.lastCheckInDate);

        setState(() {
          _isCheckedIn = false;
          _showBye = true;
        });

        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) setState(() => _showBye = false);
        });
      },
      onFailure: () {},
    );

    if (mounted) setState(() => _isLoading = false);
  }

  Widget _buildStatusText(DateTime now) {
    final greeting =
        '${'good'.tr()} ${now.hour < 12 ? 'morning'.tr() : 'evening'.tr()}';

    if (_isCheckedIn) {
      return Text('$greeting ${CacheHelper.getData(key: CacheKeys.userName)}',
          style: Styles.contentRegular);
    }

    if (_showBye) {
      return Text('home.bye'.tr(), style: Styles.contentRegular);
    }

    return Text(
        '$greeting ${CacheHelper.getData(key: CacheKeys.userName)}, ${'no_check_in'.tr()}',
        style: Styles.contentRegular);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: AppColors.neutralColor600, width: 1.w),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('attendance'.tr(), style: Styles.highlightEmphasis),
              ValueListenableBuilder<DateTime>(
                valueListenable: _currentTime,
                builder: (context, now, _) {
                  return Text(_formatDate(now), style: Styles.contentRegular);
                },
              ),
            ],
          ),
          8.verticalSpace,
          ValueListenableBuilder<DateTime>(
            valueListenable: _currentTime,
            builder: (context, now, _) {
              return Text(_formatTime(now), style: Styles.featureEmphasis);
            },
          ),
          16.verticalSpace,
          ValueListenableBuilder<DateTime>(
            valueListenable: _currentTime,
            builder: (context, now, _) {
              return Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(
                    now.hour < 12 ? Icons.wb_sunny : Icons.nightlight_round,
                    color: now.hour < 12 ? Colors.amber : Colors.grey,
                    size: 24.sp,
                  ),
                  16.horizontalSpace,
                  Expanded(child: _buildStatusText(now)),
                ],
              );
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 300),
                  opacity: !_isCheckedIn && !_isLoading ? 1.0 : 0.6,
                  child: CustomButtonWidget(
                    borderRadius: 30.r,
                    height: 48.h,
                    text: 'home.clockingIN'.tr(),
                    onPressed:
                        !_isCheckedIn && !_isLoading ? _handleCheckIn : null,
                    color: !_isCheckedIn
                        ? AppColors.primaryColor900
                        : AppColors.primaryColor900.withValues(alpha: .4),
                    textStyle: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),
              12.horizontalSpace,
              Expanded(
                child: AnimatedOpacity(
                  duration: const Duration(milliseconds: 300),
                  opacity: _isCheckedIn && !_isLoading ? 1.0 : 0.6,
                  child: CustomButtonWidget(
                    borderRadius: 30.r,
                    height: 48.h,
                    text: 'home.clockingOUT'.tr(),
                    onPressed:
                        _isCheckedIn && !_isLoading ? _handleCheckOut : null,
                    color: _isCheckedIn
                        ? AppColors.neutralColor800
                        : AppColors.neutralColor800.withValues(alpha: 0.4),
                    textStyle: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14.sp,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _currentTime.dispose();
    super.dispose();
  }
}
