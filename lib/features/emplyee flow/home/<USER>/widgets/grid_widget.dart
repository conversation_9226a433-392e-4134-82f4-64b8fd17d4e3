import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/cubit/home_cubit.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/policy_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class GirdWidget extends StatelessWidget {
  const GirdWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 12.h,
        mainAxisExtent: 130.sp,
      ),
      itemCount: context.read<HomeCubit>().policyItems.length,
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final item = context.read<HomeCubit>().policyItems[index];
        return PolicyItemWidget(
          onTap: () {
            if (index == 0) {
              context.pushNamed(Routes.absenceScreen);
            }
            if (index == 1) {
              context.pushNamed(Routes.askingPermissionScreen);
            }
          },
          image: item["image"]!,
          text: item["text"]!,
        );
      },
    );
  }
}
