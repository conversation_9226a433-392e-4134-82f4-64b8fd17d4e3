import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/model/banners_model.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/model/home_leaves_days.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/repo/home_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'home_state.dart';

class HomeCubit extends Cubit<HomeState> {
  HomeCubit(this._homeRepository) : super(HomeInitial());
  final HomeRepository _homeRepository;

  HomeLeaveModel? model;

  Future<void> getHome() async {
    emit(GetHomeLoadingState());

    final result = await _homeRepository.getHomeDays();
    result.when(success: (data) async {
      model = data;
      await getBanners();
      emit(GetHomeSuccessState());
    }, failure: (errorHandler) {
      emit(GetHomeErrorState());
    });
  }

  List policyItems = [
    {
      "image": Assets.assetsImagesSvgsAbsencePolicyIconContainer,
      "text": 'homeEmployee.absence'.tr(),
    },
    {
      "image": Assets.assetsImagesSvgsPermissionPolicyIconContainer,
      "text": 'homeEmployee.askingpermission'.tr(),
    },
    {
      "image": Assets.assetsImagesSvgsRewardsPolicyIcon,
      "text": 'homeEmployee.rewards'.tr(),
    },
  ];
  int currentIndex = 0;
  void changePage(int index) {
    currentIndex = index;
    emit(ChangePageState());
  }

  BannersResponse? bannersResponse;
  Future<void> getBanners() async {
    final result = await _homeRepository.getBanners();
    result.when(
        success: (data) {
          bannersResponse = data;
        },
        failure: (errorHandler) {});
  }
}
