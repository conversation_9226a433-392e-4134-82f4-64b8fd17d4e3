part of 'home_cubit.dart';

sealed class HomeState {}

final class HomeInitial extends HomeState {}

final class GetHomeLoadingState extends HomeState {}

final class GetHomeErrorState extends HomeState {}

final class GetHomeSuccessState extends HomeState {}

final class ChangePageState extends HomeState {}

/// Check In States
final class CheckInLoading extends HomeState {}

final class CheckInSuc<PERSON> extends HomeState {}

final class CheckInError extends HomeState {}

/// Check Out States
final class CheckOutLoading extends HomeState {}

final class CheckOutSuccess extends HomeState {}

final class CheckOutError extends HomeState {}
