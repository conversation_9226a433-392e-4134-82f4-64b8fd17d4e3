import 'package:json_annotation/json_annotation.dart';

part 'home_leaves_days.g.dart';

@JsonSerializable()
class HomeLeaveModel {
  @Json<PERSON>ey(name: 'data')
  final LeaveData data;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  final String status;
  
  @Json<PERSON><PERSON>(name: 'error')
  final String error;
  
  @JsonKey(name: 'code')
  final int code;

  HomeLeaveModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory HomeLeaveModel.fromJson(Map<String, dynamic> json) => _$HomeLeaveModelFromJson(json);
  Map<String, dynamic> toJson() => _$HomeLeaveModelToJson(this);
}

@JsonSerializable()
class LeaveData {
  @Json<PERSON>ey(name: 'annual_leave_days')
  final int annualLeaveDays;
  
  @Json<PERSON>ey(name: 'normal_days')
  final int normalDays;
  
  @JsonKey(name: 'sick_days')
  final int sickDays;
  
  @<PERSON>son<PERSON>ey(name: 'available_annual_leave_days')
  final int availableAnnualLeaveDays;
  
  @Json<PERSON><PERSON>(name: 'available_normal_days')
  final int availableNormalDays;
  
  @Json<PERSON>ey(name: 'available_sick_days')
  final int availableSickDays;

  LeaveData({
    required this.annualLeaveDays,
    required this.normalDays,
    required this.sickDays,
    required this.availableAnnualLeaveDays,
    required this.availableNormalDays,
    required this.availableSickDays,
  });

  factory LeaveData.fromJson(Map<String, dynamic> json) => _$LeaveDataFromJson(json);
  Map<String, dynamic> toJson() => _$LeaveDataToJson(this);
}