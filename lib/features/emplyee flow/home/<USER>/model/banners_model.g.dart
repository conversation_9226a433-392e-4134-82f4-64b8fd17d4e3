// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'banners_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BannersResponse _$BannersResponseFromJson(Map<String, dynamic> json) =>
    BannersResponse(
      data: (json['data'] as List<dynamic>)
          .map((e) => BannerItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$Banners<PERSON>esponseToJson(BannersResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

BannerItem _$BannerItemFromJson(Map<String, dynamic> json) => BannerItem(
      id: (json['id'] as num).toInt(),
      background: json['background'] as String,
      title: json['title'] as String,
      text: json['text'] as String,
      btnTitle: json['btnTitle'] as String,
      btnUrl: json['btnUrl'] as String,
      btnActive: json['btnActive'] as String,
    );

Map<String, dynamic> _$BannerItemToJson(BannerItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'background': instance.background,
      'title': instance.title,
      'text': instance.text,
      'btnTitle': instance.btnTitle,
      'btnUrl': instance.btnUrl,
      'btnActive': instance.btnActive,
    };
