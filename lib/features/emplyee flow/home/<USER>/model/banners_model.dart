import 'package:json_annotation/json_annotation.dart';

part 'banners_model.g.dart';

@JsonSerializable()
class BannersResponse {
  final List<BannerItem> data;
  final String status;
  final String error;
  final int code;

  BannersResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory BannersResponse.fromJson(Map<String, dynamic> json) => _$BannersResponseFromJson(json);
  Map<String, dynamic> toJson() => _$BannersResponseToJson(this);
}

@JsonSerializable()
class BannerItem {
  final int id;
  final String background;
  final String title;
  final String text;
  final String btnTitle;
  final String btnUrl;
  final String btnActive;

  BannerItem({
    required this.id,
    required this.background,
    required this.title,
    required this.text,
    required this.btnTitle,
    required this.btnUrl,
    required this.btnActive,
  });

  factory BannerItem.fromJson(Map<String, dynamic> json) => _$BannerItemFromJson(json);
  Map<String, dynamic> toJson() => _$BannerItem<PERSON>o<PERSON>(this);
}
