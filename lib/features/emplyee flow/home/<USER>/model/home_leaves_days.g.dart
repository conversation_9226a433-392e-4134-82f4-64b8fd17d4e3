// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_leaves_days.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeLeaveModel _$HomeLeaveModelFromJson(Map<String, dynamic> json) =>
    HomeLeaveModel(
      data: LeaveData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$HomeLeaveModelToJson(HomeLeaveModel instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

LeaveData _$LeaveDataFromJson(Map<String, dynamic> json) => LeaveData(
      annualLeaveDays: (json['annual_leave_days'] as num).toInt(),
      normalDays: (json['normal_days'] as num).toInt(),
      sickDays: (json['sick_days'] as num).toInt(),
      availableAnnualLeaveDays:
          (json['available_annual_leave_days'] as num).toInt(),
      availableNormalDays: (json['available_normal_days'] as num).toInt(),
      availableSickDays: (json['available_sick_days'] as num).toInt(),
    );

Map<String, dynamic> _$LeaveDataToJson(LeaveData instance) => <String, dynamic>{
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'available_annual_leave_days': instance.availableAnnualLeaveDays,
      'available_normal_days': instance.availableNormalDays,
      'available_sick_days': instance.availableSickDays,
    };
