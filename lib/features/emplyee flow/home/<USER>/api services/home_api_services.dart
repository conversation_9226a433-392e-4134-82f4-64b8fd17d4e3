import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class HomeApiServices {
  HomeApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> getHomeDays() async {
    return _dioFactory.get(
      endPoint: EndPoints.getHomeDays,
    );
  }

  Future<Response?> getBanners() async {
    return _dioFactory.get(
      endPoint: EndPoints.getBannersHome,
    );
  }
}
