import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/api%20services/home_api_services.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/model/banners_model.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/model/home_leaves_days.dart';

class HomeRepository {
  final HomeApiServices authApiServices;

  HomeRepository(this.authApiServices);

  Future<ApiResult<HomeLeaveModel>> getHomeDays() async {
    final response = await authApiServices.getHomeDays();
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        HomeLeaveModel model = HomeLeaveModel.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<BannersResponse>> getBanners() async {
    final response = await authApiServices.getBanners();
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        BannersResponse model = BannersResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
