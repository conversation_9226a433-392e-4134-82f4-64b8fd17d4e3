import 'package:json_annotation/json_annotation.dart';

part 'compauny_profile_model.g.dart';

@JsonSerializable()
class CompanyProfileResponse {
  final CompanyProfileData data;

  CompanyProfileResponse({required this.data});

  factory CompanyProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$CompanyProfileResponseFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyProfileResponseToJson(this);
}

@JsonSerializable()
class CompanyProfileData {
  final List<CompanyProfile> data;
  final Meta meta;

  CompanyProfileData({required this.data, required this.meta});

  factory CompanyProfileData.fromJson(Map<String, dynamic> json) =>
      _$CompanyProfileDataFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyProfileDataToJson(this);
}

@JsonSerializable()
class CompanyProfile {
  final int id;
  final LocalizedText title;
  final LocalizedText text;
  final String file;

  CompanyProfile({
    required this.id,
    required this.title,
    required this.text,
    required this.file,
  });

  factory CompanyProfile.fromJson(Map<String, dynamic> json) =>
      _$CompanyProfileFromJson(json);

  Map<String, dynamic> toJson() => _$CompanyProfileToJson(this);
}

@JsonSerializable()
class LocalizedText {
  final String en;
  final String ar;

  LocalizedText({required this.en, required this.ar});

  factory LocalizedText.fromJson(Map<String, dynamic> json) =>
      _$LocalizedTextFromJson(json);

  Map<String, dynamic> toJson() => _$LocalizedTextToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page')
  final int currentPage;

  @JsonKey(name: 'last_page')
  final int lastPage;

  Meta({required this.currentPage, required this.lastPage});

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}
