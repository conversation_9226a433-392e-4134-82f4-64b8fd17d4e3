// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'compauny_profile_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CompanyProfileResponse _$CompanyProfileResponseFromJson(
        Map<String, dynamic> json) =>
    CompanyProfileResponse(
      data: CompanyProfileData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CompanyProfileResponseToJson(
        CompanyProfileResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
    };

CompanyProfileData _$CompanyProfileDataFromJson(Map<String, dynamic> json) =>
    CompanyProfileData(
      data: (json['data'] as List<dynamic>)
          .map((e) => CompanyProfile.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$CompanyProfileDataToJson(CompanyProfileData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'meta': instance.meta,
    };

CompanyProfile _$CompanyProfileFromJson(Map<String, dynamic> json) =>
    CompanyProfile(
      id: (json['id'] as num).toInt(),
      title: LocalizedText.fromJson(json['title'] as Map<String, dynamic>),
      text: LocalizedText.fromJson(json['text'] as Map<String, dynamic>),
      file: json['file'] as String,
    );

Map<String, dynamic> _$CompanyProfileToJson(CompanyProfile instance) =>
    <String, dynamic>{
      'id': instance.id,
      'title': instance.title,
      'text': instance.text,
      'file': instance.file,
    };

LocalizedText _$LocalizedTextFromJson(Map<String, dynamic> json) =>
    LocalizedText(
      en: json['en'] as String,
      ar: json['ar'] as String,
    );

Map<String, dynamic> _$LocalizedTextToJson(LocalizedText instance) =>
    <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
      currentPage: (json['current_page'] as num).toInt(),
      lastPage: (json['last_page'] as num).toInt(),
    );

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'last_page': instance.lastPage,
    };
