import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/data/api%20servies/company_profile_api_seriveces.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/data/model/compauny_profile_model.dart';

class CompanyProfileRepository {
  final CompanyProfileApiService _apiService;

  CompanyProfileRepository(this._apiService);

  Future<ApiResult<CompanyProfileResponse>> getCompanyProfiles(
      {required int page}) async {
    try {
      final response = await _apiService.getCompanyProfiles(page: page);
      if (response?.statusCode == 200 || response?.statusCode == 201) {
        final model = CompanyProfileResponse.fromJson(response!.data);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response?.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    }
  }
}
