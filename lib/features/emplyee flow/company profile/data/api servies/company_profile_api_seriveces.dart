import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class CompanyProfileApiService {
  final DioHelper _dioHelper;

  CompanyProfileApiService(this._dioHelper);

  Future<Response?> getCompanyProfiles({required int page}) async {
    return await _dioHelper.get(
      endPoint: EndPoints.getCompanyProfiles,
      data: {'page': page},
    );
  }
}