import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/bloc/cubit/company_profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:erp/core/helper_functions/download_function.dart';


class CompanyProfiles extends StatelessWidget {
  const CompanyProfiles({super.key});

  @override
  Widget build(BuildContext context) {
    final companyProfileCubit = context.read<CompanyProfileCubit>();
    return BlocBuilder<CompanyProfileCubit, CompanyProfileState>(
      builder: (context, state) {
        if (state is CompanyProfileLoading) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                title: const Text('Company Profile'),
                centerTitle: true,
              ),
              body: ListView.separated(
                controller: companyProfileCubit.scrollController,
                padding:
                    EdgeInsets.symmetric(horizontal: 18.sp, vertical: 32.sp),
                itemCount: 10,
                separatorBuilder: (BuildContext context, int index) {
                  return 20.verticalSpace;
                },
                itemBuilder: (BuildContext context, int index) {
                  return Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.sp, vertical: 8.sp),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.sp),
                      border: Border.all(
                        width: 1.sp,
                        color: AppColors.neutralColor600,
                      ),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12.sp),
                          child: SvgPicture.asset(
                            Assets.assetsImagesSvgsPdfIcon,
                            height: 36.sp,
                            width: 36.sp,
                            fit: BoxFit.cover,
                          ),
                        ),
                        SizedBox(width: 10.sp),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Text(
                                "data.text.en",
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w700,
                                ),
                              ),
                              Text(
                                "  data.title.en",
                                style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.neutralColor600,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Row(
                          children: [
                            Icon(
                              Icons.download_outlined,
                              size: 20.sp,
                              color: AppColors.primaryColor800,
                            ),
                            Text(
                              "download",
                              style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.primaryColor800,
                                  fontSize: 12.sp),
                            ),
                          ],
                        )
                      ],
                    ),
                  );
                },
              ),
            ),
          );
        }
        return Scaffold(
          appBar: AppBar(
            title: Text('moreSales.companyProfile'.tr()),
            centerTitle: true,
          ),
          body: ListView.separated(
            controller: companyProfileCubit.scrollController,
            padding: EdgeInsets.symmetric(horizontal: 18.sp, vertical: 32.sp),
            itemCount: companyProfileCubit.model!.data.data.length,
            separatorBuilder: (BuildContext context, int index) {
              return 20.verticalSpace;
            },
            itemBuilder: (BuildContext context, int index) {
              final data = companyProfileCubit.model!.data.data[index];
              return Container(
                padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 8.sp),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.sp),
                  border: Border.all(
                    width: 1.sp,
                    color: AppColors.neutralColor600,
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(12.sp),
                      child: SvgPicture.asset(
                        Assets.assetsImagesSvgsPdfIcon,
                        height: 36.sp,
                        width: 36.sp,
                        fit: BoxFit.cover,
                      ),
                    ),
                    SizedBox(width: 10.sp),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            data.text.en,
                            style: Styles.contentEmphasis.copyWith(
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          Text(
                            data.title.en,
                            style: Styles.contentEmphasis.copyWith(
                              fontWeight: FontWeight.w400,
                              color: AppColors.neutralColor600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (data.file.isNotEmpty || data.file != "")
                      InkWell(
                        onTap: () async {
                          await downloadPdfFile(data.file,
                              Uri.parse(data.file).pathSegments.last);
                        },
                        child: Row(
                          children: [
                            Icon(
                              Icons.download_outlined,
                              size: 20.sp,
                              color: AppColors.primaryColor800,
                            ),
                            Text(
                              'clientsDetails.download'.tr(),
                              style: Styles.contentEmphasis.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: AppColors.primaryColor800,
                                  fontSize: 12.sp),
                            ),
                          ],
                        ),
                      )
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}
