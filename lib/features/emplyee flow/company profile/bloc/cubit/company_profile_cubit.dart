import 'package:erp/features/emplyee%20flow/company%20profile/data/model/compauny_profile_model.dart';
import 'package:erp/features/emplyee%20flow/company%20profile/data/repo/company_profile_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'company_profile_state.dart';

class CompanyProfileCubit extends Cubit<CompanyProfileState> {
  CompanyProfileCubit(this._repo) : super(CompanyProfileInitial()) {
    setupScrollListener();
  }

  final CompanyProfileRepository _repo;

  CompanyProfileResponse? model;
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final ScrollController scrollController = ScrollController();

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 100 &&
          !isLoadingMore) {
        loadMoreProfiles();
      }
    });
  }

  Future<void> getCompanyProfiles() async {
    emit(CompanyProfileLoading());
    currentPage = 1;
    lastPage = 1;

    final result = await _repo.getCompanyProfiles(page: currentPage);

    result.when(success: (data) {
      model = data;
      currentPage = data.data.meta.currentPage;
      lastPage = data.data.meta.lastPage;
      emit(CompanyProfileSuccess());
    }, failure: (error) {
      emit(CompanyProfileError());
    });
  }

  Future<void> loadMoreProfiles() async {
    if (currentPage >= lastPage) return;

    isLoadingMore = true;
    currentPage++;

    final result = await _repo.getCompanyProfiles(page: currentPage);
    result.when(success: (data) {
      model!.data.data.addAll(data.data.data);
      currentPage = data.data.meta.currentPage;
      lastPage = data.data.meta.lastPage;
      emit(CompanyProfileSuccess());
    }, failure: (error) {
      emit(CompanyProfileError());
    });

    isLoadingMore = false;
  }
}
