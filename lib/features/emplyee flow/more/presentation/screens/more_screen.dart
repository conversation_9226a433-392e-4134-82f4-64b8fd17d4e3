import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/helper_functions/log_out_method.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/features/emplyee%20flow/more/presentation/widgets/more_item_widget.dart';
import 'package:erp/features/localization/presentation/localization_screen.dart';
import 'package:erp/features/profile/presentaiton/widgets/profile_top_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class MoreScreen extends StatelessWidget {
  const MoreScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 18.sp,
                  vertical: 12.sp,
                ),
                child: Column(
                  children: [
                    ProfileTopWidget(),
                    20.verticalSpace,
                    if (CacheHelper.getData(key: CacheKeys.isTeamLead) ==
                        true) ...[
                      MoreItemWIdget(
                        onPressed: () {
                          context.pushNamed(Routes.hrAbsenceScreen);
                        },
                        label: 'moreEmployee.teamAbsence'.tr(),
                        image: Assets.assetsImagesSvgsTeamAbsence,
                      ),
                      MoreItemWIdget(
                        onPressed: () {
                          context.pushNamed(Routes.hrAskingPermissionScreen);
                        },
                        label: 'moreEmployee.teamAskingpermisson'.tr(),
                        image: Assets.assetsImagesSvgsTeamAbsence,
                      ),
                    ],
                    MoreItemWIdget(
                      onPressed: () {
                        showLocalizationBottomSheet(context);
                      },
                      label: 'moreSales.language'.tr(),
                      subTitle: context.locale.toString() == 'ar_EG'
                          ? "العربية"
                          : "English (uk)",
                      image: Assets.assetsImagesSvgsLanguageIcon,
                    ),
                    MoreItemWIdget(
                      onPressed: () {
                        context.pushNamed(Routes.companyProfile);
                      },
                      label: 'moreSales.Companypolicies'.tr(),
                      image: Assets.assetsImagesSvgsCompanyProfile,
                    ),
                    // MoreItemWIdget(
                    //   onPressed: () {
                    //     context.pushNamed(Routes.ticketsScreen);
                    //   },
                    //   label: 'Tickets',
                    //   image: Assets.assetsImagesSvgsTicketsIcon,
                    // ),
                    MoreItemWIdget(
                      onPressed: () {
                        context.pushNamed(Routes.changePasswordScreen);
                      },
                      label: 'moreSales.changePassword'.tr(),
                      image: Assets.assetsImagesSvgsChangePasswordIcon,
                    ),
                    // MoreItemWIdget(
                    //   onPressed: () {},
                    //   label: 'moreSales.darkmode'.tr(),
                    //   isDarkMode: true,
                    //   value: false,
                    //   onChange: (v) {},
                    //   subTitle: 'moreSales.notEffective'.tr(),
                    //   image: Assets.assetsImagesSvgsTheme,
                    // ),
                    MoreItemWIdget(
                      onPressed: logoutMethod,
                      isRedColor: true,
                      isLastItem: true,
                      label: 'moreSales.logout'.tr(),
                      image: Assets.assetsImagesSvgsLogoutIcon,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
