import 'package:flutter_bloc/flutter_bloc.dart';

part 'calendar_state.dart';

class CalendarCubit extends Cubit<CalendarState> {
  CalendarCubit() : super(CalendarInitial());
  DateTime? selectedDate;
  DateTime endDate = DateTime.now().add(const Duration(days: 120));

  void chooseBookingDate(DateTime dateTime) async {
    selectedDate = dateTime;

    emit(BookChangeDateState());
  }

  DateTime focusedDay = DateTime.now();

  void updateFocusedDay(DateTime day) {
    focusedDay = day;
    emit(BookChangeDateState());
  }
}
