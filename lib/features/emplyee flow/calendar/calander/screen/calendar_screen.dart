import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/calendar/bloc/cubit/calendar_cubit.dart';
import 'package:erp/features/emplyee%20flow/calendar/calander/widget/calendar_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CalendarScreen extends StatelessWidget {
  const CalendarScreen({super.key});

  @override
  Widget build(BuildContext context) {
    CalendarCubit calenderCubit = BlocProvider.of<CalendarCubit>(context);

    return Scaffold(
      appBar: AppBar(
        title: Text('mainLayoutEmployee.calender'.tr()),
        leading: Icon(Icons.notifications_none_rounded),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.sp),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CalendarWidget(),
            BlocBuilder<CalendarCubit, CalendarState>(
              buildWhen: (previous, current) => current is BookChangeDateState,
              builder: (context, state) {
                if (calenderCubit.selectedDate != null) {
                  return Column(
                    children: [
                      12.verticalSpace,
                      Row(
                        children: [
                          Text(
                            "Meetings ( 13 )",
                            style: Styles.contentBold.copyWith(
                                fontSize: 18.sp, fontWeight: FontWeight.w500),
                          ),
                          Spacer(),
                          TextButton(
                            onPressed: () {
                              context.pushNamed(Routes.allmettingsScreen,
                                  arguments:
                                      "${calenderCubit.selectedDate!.day}/${calenderCubit.selectedDate!.month}/${calenderCubit.selectedDate!.year}");
                            },
                            child: Text(
                              'homeEmployee.seeMore'.tr(),
                              style: Styles.captionEmphasis.copyWith(
                                color: AppColors.primaryColor800,
                                decoration: TextDecoration.underline,
                                decorationColor: AppColors.primaryColor800,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      12.verticalSpace,
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          spacing: 16.sp,
                          children: List.generate(
                            13,
                            (index) {
                              return ProjectsItemWidget(
                                title: "Meeting Name",
                                description: "14:30",
                              );
                            },
                          ),
                        ),
                      ),
                      12.verticalSpace,
                      CustomButtonWidget(
                        text: 'homeEmployee.addMeeting'.tr(),
                        onPressed: () {
                          context.pushNamed(Routes.addMeetingScreen);
                        },
                      ),
                    ],
                  );
                }
                return SizedBox.shrink();
              },
            )
          ],
        ),
      ),
    );
  }
}
