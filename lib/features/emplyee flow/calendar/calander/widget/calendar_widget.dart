import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/calendar/bloc/cubit/calendar_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';

class CalendarWidget extends StatelessWidget {
  const CalendarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    CalendarCubit calenderCubit = BlocProvider.of<CalendarCubit>(context);
    return BlocBuilder<CalendarCubit, CalendarState>(
      buildWhen: (previous, current) => current is BookChangeDateState,
      builder: (context, state) {
        return Column(
          children: [
            TableCalendar(
              rowHeight: 40.sp,
              daysOfWeekVisible: true,
              onDaySelected: (selectedDay, focusedDay) {
                calenderCubit.chooseBookingDate(selectedDay);
                calenderCubit.updateFocusedDay(selectedDay);
              },
              focusedDay: calenderCubit.focusedDay,
              firstDay: DateTime.now(),
              lastDay: calenderCubit.endDate,
              currentDay: DateTime.now(),
              calendarStyle: CalendarStyle(
                disabledTextStyle: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutralColor600),
                outsideTextStyle: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.neutralColor600),
              ),
              locale: context.locale.toString(),
              daysOfWeekHeight: 30.sp,
              calendarFormat: CalendarFormat.month,
              onPageChanged: (focusedDay) {
                calenderCubit.updateFocusedDay(focusedDay);
              },
              calendarBuilders: CalendarBuilders(
                todayBuilder: (context, day, focusedDay) {
                  if (day.day == calenderCubit.selectedDate?.day) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.sp,
                        vertical: 10.sp,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.primaryColor800, width: 1.sp),
                        color: AppColors.primaryColor800.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(day.day.toString(),
                          style: Styles.contentEmphasis.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                    );
                  } else {
                    return Center(
                      child: Text(
                        day.day.toString(),
                        style: Styles.contentEmphasis.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }
                },
                defaultBuilder: (context, day, focusedDay) {
                  if (day == calenderCubit.selectedDate) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12.sp,
                        vertical: 10.sp,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                            color: AppColors.primaryColor800, width: 1.sp),
                        color: AppColors.primaryColor800.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(day.day.toString(),
                          style: Styles.contentEmphasis.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                    );
                  } else {
                    return Center(
                      child: Text(day.day.toString(),
                          style: Styles.contentEmphasis.copyWith(
                            fontWeight: FontWeight.w500,
                          )),
                    );
                  }
                },
              ),
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.primaryColor800,
                      width: 1.0.sp,
                    ),
                  ),
                ),
                leftChevronIcon: Container(
                  padding: EdgeInsets.all(6.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: AppColors.primaryColor800,
                    ),
                  ),
                  child: Icon(Icons.keyboard_arrow_left_rounded,
                      color: Colors.grey.shade600, size: 18.sp),
                ),
                rightChevronIcon: Container(
                  padding: EdgeInsets.all(6.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                      color: AppColors.primaryColor800,
                    ),
                  ),
                  child: Icon(Icons.keyboard_arrow_right_sharp,
                      color: Colors.grey.shade600, size: 18.sp),
                ),
                titleCentered: true,
                titleTextStyle: Styles.highlightEmphasis.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                titleTextFormatter: (date, locale) {
                  return DateFormat.yMMM(locale).format(date);
                },
              ),
              daysOfWeekStyle: DaysOfWeekStyle(
                weekdayStyle: Styles.highlightEmphasis.copyWith(
                  fontWeight: FontWeight.w500,
                ),
                weekendStyle: Styles.highlightEmphasis.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            if (calenderCubit.selectedDate != null) ...[
              12.verticalSpace,
              Row(
                spacing: 12.sp,
                children: [
                  Expanded(
                    child: Container(
                      height: 1.sp,
                      width: double.infinity,
                      color: AppColors.neutralColor600,
                    ),
                  ),
                  Text(
                    "${calenderCubit.selectedDate!.day.toString()}/${calenderCubit.selectedDate!.month.toString()}",
                    style: Styles.captionEmphasis
                        .copyWith(color: AppColors.neutralColor600),
                  ),
                  Expanded(
                    child: Container(
                      height: 1.sp,
                      width: double.infinity,
                      color: AppColors.neutralColor600,
                    ),
                  )
                ],
              )
            ],
          ],
        );
      },
    );
  }
}
