import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/repo/projects_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'projects_details_state.dart';

class ProjectsDetailsCubit extends Cubit<ProjectsDetailsState> {
  ProjectsDetailsCubit(this._homeRepository) : super(ProjectsInitial());
  final ProjectsDetailsRepository _homeRepository;
  ProjectDetailsResponse? model;
  Future<void> getProjects({required int id}) async {
    emit(GetProjectsLoadingState());

    final result = await _homeRepository.getProjects(id: id);
    result.when(success: (data) {
      model = data;
      emit(GetProjectsSuccessState());
    }, failure: (errorHandler) {
      emit(GetProjectsErrorState());
    });
  }
}
