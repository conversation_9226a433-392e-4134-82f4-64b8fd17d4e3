import 'package:erp/core/themes/app_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final BorderRadiusGeometry? borderRadius;
  final Color? borderColor;

  const CustomCard({
    super.key,
    required this.child,
    this.padding,
    this.borderRadius,
    this.borderColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: padding ?? EdgeInsets.all(12.sp),
      decoration: BoxDecoration(
        border: Border.all(color: borderColor ?? AppColors.neutralColor600),
        borderRadius: borderRadius ?? BorderRadius.circular(8.r),
      ),
      child: child,
    );
  }
}
