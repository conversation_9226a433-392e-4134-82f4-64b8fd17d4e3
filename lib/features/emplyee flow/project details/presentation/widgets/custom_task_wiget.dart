import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/download_function.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_continer_widgt.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProjectCard extends StatelessWidget {
  final List<Map<String, String>> projectInfo;
  final String details;
  final String status;
  final String? myStatus;

  final bool? isMyStausCompleted;

  final bool isCompleted;
  final String? comment;
  final bool isProject;

  final List<String> files;

  const ProjectCard({
    super.key,
    required this.projectInfo,
    required this.details,
    this.myStatus,
    this.comment,
    this.isProject = true,
    this.isMyStausCompleted,
    required this.status,
    required this.isCompleted,
    required this.files,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 16.sp,
      children: [
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: projectInfo.map((info) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4.h),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Text(info["title"]!,
                        style: Styles.contentEmphasis.copyWith(
                          fontWeight: FontWeight.w800,
                        )),
                    Spacer(),
                    Expanded(
                      child: Text(info["value"]!,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.start,
                          style: Styles.contentEmphasis
                              .copyWith(fontWeight: FontWeight.w400)),
                    ),
                    20.horizontalSpace,
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                  "${isProject ? 'clientsDetails.project'.tr() : 'clientsDetails.task'.tr()} ${'clientsDetails.details'.tr()}",
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w800,
                  )),
              SizedBox(height: 5.h),
              Text(
                details,
                style: Styles.contentEmphasis.copyWith(
                  fontWeight: FontWeight.w300,
                ),
              ),
            ],
          ),
        ),
        if (isProject == false)
          CustomCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('clientsDetails.comment'.tr(),
                    style: Styles.contentEmphasis.copyWith(
                      fontWeight: FontWeight.w800,
                    )),
                SizedBox(height: 5.h),
                Text(
                  comment!,
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
        CustomCard(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                isProject
                    ? 'clientsDetails.status'.tr()
                    : 'clientsDetails.statusTask'.tr(),
                style: Styles.contentEmphasis.copyWith(
                  fontWeight: FontWeight.w800,
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppColors.greenColor200.withValues(alpha: 0.1)
                      : AppColors.yellowColor100.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  status,
                  style: Styles.captionEmphasis.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                    color: isCompleted
                        ? AppColors.greenColor200
                        : AppColors.yellowColor100,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (isProject == false)
          if (isMyStausCompleted == true)
            CustomCard(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('clientsDetails.myStatus'.tr(),
                      style: Styles.contentEmphasis.copyWith(
                        fontWeight: FontWeight.w800,
                      )),
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                    decoration: BoxDecoration(
                      color: AppColors.greenColor200.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      myStatus!,
                      style: Styles.captionEmphasis.copyWith(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.greenColor200),
                    ),
                  ),
                ],
              ),
            ),
        ...files.map(
          (file) => CustomCard(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(file,
                      style: Styles.contentEmphasis.copyWith(
                        fontWeight: FontWeight.w800,
                      )),
                ),
                CustomButtonWidget(
                  text: 'clientsDetails.download'.tr(),
                  onPressed: () async {
                    await downloadPdfFile(
                        file, Uri.parse(file).pathSegments.last);
                  },
                  color: AppColors.scaffoldBackground,
                  borderColor: BorderSide(color: AppColors.neutralColor1600),
                  textColor: AppColors.neutralColor1600,
                  width: 150.sp,
                  height: 40.sp,
                  iconData: Icons.download,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
