import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/project%20details/bloc/cubit/projects_details_cubit.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_wiget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProjectDetailsScreen extends StatelessWidget {
  const ProjectDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final projectDetails = context.read<ProjectsDetailsCubit>();
    return BlocBuilder<ProjectsDetailsCubit, ProjectsDetailsState>(
      builder: (context, state) {
        if (state is GetProjectsLoadingState) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                centerTitle: false,
                title: Text("project Name "),
              ),
              body: SingleChildScrollView(
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ProjectCard(
                      projectInfo: [
                        {"title": "Project Name", "value": "ERP"},
                        {
                          "title": "Date of receipt of the project",
                          "value": "12/02/2025"
                        },
                        {
                          "title": "Project closing date",
                          "value": "12/02/2025"
                        },
                      ],
                      details:
                          "Undergoing surgery that requires recovery time.",
                      status: "Not completed",
                      isCompleted: false,
                      files: ["File1.pdf", "File2.docx"],
                    ),
                    18.verticalSpace,
                    Row(
                      children: [
                        Text(
                          "Projects",
                          style: Styles.contentBold.copyWith(
                              fontSize: 18.sp, fontWeight: FontWeight.w500),
                        ),
                        Spacer(),
                        InkWell(
                          onTap: () {
                            context.pushNamed(Routes.allTasksScreen);
                          },
                          child: Text(
                            "see More",
                            style: Styles.captionEmphasis.copyWith(
                              color: AppColors.primaryColor800,
                              decoration: TextDecoration.underline,
                              decorationColor: AppColors.primaryColor800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                    12.verticalSpace,
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Row(
                        spacing: 16.sp,
                        children: List.generate(
                          5,
                          (index) {
                            return ProjectsItemWidget(
                              onTap: () {},
                              title: "Task Name",
                              description: "Dessssssssssssssssssssssssaaaaaa",
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
        return Scaffold(
          appBar: AppBar(
            centerTitle: false,
            title: Text(projectDetails.model!.data.name),
          ),
          body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ProjectCard(
                    projectInfo: [
                      {"title": projectDetails.model!.data.name, "value": ""},
                      {
                        "title": 'homeEmployee.dateofreceiptoftheproject'.tr(),
                        "value": projectDetails.model!.data.startDate
                      },
                      {
                        "title": 'homeEmployee.projectclosingdate'.tr(),
                        "value": projectDetails.model!.data.deadline
                      },
                    ],
                    details: projectDetails.model!.data.details ?? "",
                    status: projectDetails.model!.data.status,
                    isCompleted: projectDetails.model!.data.status != "pending"
                        ? true
                        : false,
                    files: projectDetails.model!.data.files),
                18.verticalSpace,
                Row(
                  children: [
                    Text(
                      'homeEmployee.tasks'.tr(),
                      style: Styles.contentBold.copyWith(
                          fontSize: 18.sp, fontWeight: FontWeight.w500),
                    ),
                    Spacer(),
                    InkWell(
                      onTap: () {
                        context.pushNamed(Routes.allTasksScreen,
                            arguments: projectDetails.model!.data.tasks);
                      },
                      child: Text(
                        'homeEmployee.seeMore'.tr(),
                        style: Styles.captionEmphasis.copyWith(
                          color: AppColors.primaryColor800,
                          decoration: TextDecoration.underline,
                          decorationColor: AppColors.primaryColor800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                12.verticalSpace,
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    spacing: 16.sp,
                    children: List.generate(
                      projectDetails.model!.data.tasks.length,
                      (index) {
                        return ProjectsItemWidget(
                          onTap: () {
                            context.pushNamed(Routes.taskDetailsScreen,
                                arguments:
                                    projectDetails.model!.data.tasks[index].id);
                          },
                          title: projectDetails.model!.data.tasks[index].name,
                          description:
                              projectDetails.model!.data.tasks[index].status,
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
