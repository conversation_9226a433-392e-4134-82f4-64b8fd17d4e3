// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'project_details_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProjectDetailsResponse _$ProjectDetailsResponseFromJson(
        Map<String, dynamic> json) =>
    ProjectDetailsResponse(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data: ProjectDetails.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$ProjectDetailsResponseToJson(
        ProjectDetailsResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

ProjectDetails _$ProjectDetailsFromJson(Map<String, dynamic> json) =>
    ProjectDetails(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      client: json['client'] == null
          ? null
          : Client.fromJson(json['client'] as Map<String, dynamic>),
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      departments: (json['departments'] as List<dynamic>)
          .map((e) => e as String)
          .toList(),
      details: json['details'] as String?,
      notes: json['notes'] as String?,
      startDate: json['start_date'] as String,
      deadline: json['deadline'] as String,
      status: json['status'] as String,
      files: (json['files'] as List<dynamic>).map((e) => e as String).toList(),
      tasks: (json['tasks'] as List<dynamic>)
          .map((e) => Task.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectQuotes: (json['project_quotes'] as List<dynamic>?)
          ?.map((e) => ProjectQuote.fromJson(e as Map<String, dynamic>))
          .toList(),
      projectInvoices: (json['project_invoices'] as List<dynamic>?)
          ?.map((e) => ProjectInvoice.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$ProjectDetailsToJson(ProjectDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'client': instance.client,
      'employees': instance.employees,
      'departments': instance.departments,
      'details': instance.details,
      'notes': instance.notes,
      'start_date': instance.startDate,
      'deadline': instance.deadline,
      'status': instance.status,
      'files': instance.files,
      'tasks': instance.tasks,
      'project_quotes': instance.projectQuotes,
      'project_invoices': instance.projectInvoices,
    };

Client _$ClientFromJson(Map<String, dynamic> json) => Client(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      gender: json['gender'] as String,
      country: json['country'] as String,
      employee: json['employee'],
      type: json['type'] as String,
      image: json['image'] as String,
    );

Map<String, dynamic> _$ClientToJson(Client instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'gender': instance.gender,
      'country': instance.country,
      'employee': instance.employee,
      'type': instance.type,
      'image': instance.image,
    };

Task _$TaskFromJson(Map<String, dynamic> json) => Task(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      project: json['project'] as String,
      employees: (json['employees'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      startDate: json['start_date'] as String,
      deadline: json['deadline'] as String,
      status: json['status'] as String,
    );

Map<String, dynamic> _$TaskToJson(Task instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'project': instance.project,
      'employees': instance.employees,
      'start_date': instance.startDate,
      'deadline': instance.deadline,
      'status': instance.status,
    };

ProjectQuote _$ProjectQuoteFromJson(Map<String, dynamic> json) => ProjectQuote(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$ProjectQuoteToJson(ProjectQuote instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'url': instance.url,
    };

ProjectInvoice _$ProjectInvoiceFromJson(Map<String, dynamic> json) =>
    ProjectInvoice(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      url: json['url'] as String?,
    );

Map<String, dynamic> _$ProjectInvoiceToJson(ProjectInvoice instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'url': instance.url,
    };
