import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'project_details_response.g.dart';

@JsonSerializable()
class ProjectDetailsResponse {
  final String status;
  final String error;
  final int code;
  final ProjectDetails data;

  ProjectDetailsResponse({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory ProjectDetailsResponse.fromJson(Map<String, dynamic> json) =>
      _$ProjectDetailsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectDetailsResponseToJson(this);
}

@JsonSerializable()
class ProjectDetails {
  final int id;
  final String name;
  final Client? client;
  final List<Employee> employees;

  final List<String> departments;
  final String? details;
  final String? notes;

  @JsonKey(name: 'start_date')
  final String startDate;

  final String deadline;
  final String status;
  final List<String> files;
  final List<Task> tasks;
  @Json<PERSON>ey(name: 'project_quotes')
  final List<ProjectQuote>? projectQuotes;
  @JsonKey(name: 'project_invoices')
  final List<ProjectInvoice>? projectInvoices;
  ProjectDetails({
    required this.id,
    required this.name,
    required this.client,
    required this.employees,
    required this.departments,
    this.details,
    this.notes,
    required this.startDate,
    required this.deadline,
    required this.status,
    required this.files,
    required this.tasks,
    this.projectQuotes,
    this.projectInvoices,
  });

  factory ProjectDetails.fromJson(Map<String, dynamic> json) =>
      _$ProjectDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectDetailsToJson(this);
}

@JsonSerializable()
class Client {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String gender;
  final String country;
  final dynamic employee;
  final String type;
  final String image;

  Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.gender,
    required this.country,
    this.employee,
    required this.type,
    required this.image,
  });

  factory Client.fromJson(Map<String, dynamic> json) => _$ClientFromJson(json);

  Map<String, dynamic> toJson() => _$ClientToJson(this);
}

@JsonSerializable()
class Task {
  final int id;
  final String name;
  final String project;
  final List<Employee> employees;

  @JsonKey(name: 'start_date')
  final String startDate;

  final String deadline;
  final String status;

  Task({
    required this.id,
    required this.name,
    required this.project,
    required this.employees,
    required this.startDate,
    required this.deadline,
    required this.status,
  });

  factory Task.fromJson(Map<String, dynamic> json) => _$TaskFromJson(json);

  Map<String, dynamic> toJson() => _$TaskToJson(this);
}

@JsonSerializable()
class ProjectQuote {
  final int? id;
  final String? name;
  final String? url;

  ProjectQuote({this.id, this.name, this.url});

  factory ProjectQuote.fromJson(Map<String, dynamic> json) =>
      _$ProjectQuoteFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectQuoteToJson(this);
}

@JsonSerializable()
class ProjectInvoice {
  final int? id;
  final String? name;
  final String? url;

  ProjectInvoice({this.id, this.name , this.url});

  factory ProjectInvoice.fromJson(Map<String, dynamic> json) =>
      _$ProjectInvoiceFromJson(json);
  Map<String, dynamic> toJson() => _$ProjectInvoiceToJson(this);
}
