import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';
class ProjectsDetailsApiServices {
  ProjectsDetailsApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> getProjects({required int id }) async {
    return _dioFactory.get(endPoint: "${EndPoints.getProjects}/$id" );
  }
}
