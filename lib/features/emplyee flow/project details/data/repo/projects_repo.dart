import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/api%20services/projects_details_api_services.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';

class ProjectsDetailsRepository {
  final ProjectsDetailsApiServices authApiServices;

  ProjectsDetailsRepository(this.authApiServices);

  Future<ApiResult<ProjectDetailsResponse>> getProjects(
      {required int id}) async {
    final response = await authApiServices.getProjects(id: id);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        ProjectDetailsResponse model =
            ProjectDetailsResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
