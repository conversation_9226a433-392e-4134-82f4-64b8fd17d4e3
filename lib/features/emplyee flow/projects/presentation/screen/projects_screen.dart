import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/emplyee%20flow/projects/bloc/cubit/projects_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProjectsScreen extends StatelessWidget {
  const ProjectsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final projectsCubit = context.read<ProjectsCubit>();
    return BlocBuilder<ProjectsCubit, ProjectsState>(
      buildWhen: (previous, current) =>
          current is GetProjectsLoadingState ||
          current is GetProjectsErrorState ||
          current is GetProjectsSuccessState,
      builder: (context, state) {
        if (state is GetProjectsLoadingState) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                leading: Icon(Icons.notifications_none_rounded),
                title: Text('Projects'),
              ),
              body: ListView.separated(
                itemCount: 15,
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                separatorBuilder: (BuildContext context, int index) {
                  return 16.sp.verticalSpace;
                },
                itemBuilder: (BuildContext context, int index) {
                  return ProjectsItemWidget(
                    onTap: () {
                      context.pushNamed(Routes.projectDetailsScreen);
                    },
                    title: "Project Name",
                    description: "Dessssssssssssssssssssssssaaaaaa",
                  );
                },
              ),
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            leading: Icon(Icons.notifications_none_rounded),
            title: Text('homeEmployee.projects'.tr()),
          ),
          body: projectsCubit.model!.data.data.isEmpty
              ? Center(
                  child: Text(
                    'homeEmployee.noprojectsavailable'.tr(),
                    style: Styles.highlightEmphasis,
                  ),
                )
              : Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        controller: projectsCubit.scrollController,
                        itemCount: projectsCubit.model!.data.data.length,
                        padding: EdgeInsets.symmetric(
                            vertical: 18.sp, horizontal: 16.sp),
                        separatorBuilder: (BuildContext context, int index) {
                          return 16.sp.verticalSpace;
                        },
                        itemBuilder: (BuildContext context, int index) {
                          final project = projectsCubit.model!.data.data[index];
                          return ProjectsItemWidget(
                            onTap: () {
                              context.pushNamed(
                                Routes.projectDetailsScreen,
                                arguments: project.id,
                              );
                            },
                            title: project.name,
                            description: project.deadline!,
                          );
                        },
                      ),
                    ),
                    BlocBuilder<ProjectsCubit, ProjectsState>(
                      buildWhen: (previous, current) =>
                          current is GetProjectsLoadingMoreState ||
                          current is GetProjectsSuccessState,
                      builder: (context, state) {
                        if (state is GetProjectsLoadingMoreState) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        return SizedBox.shrink();
                      },
                    )
                  ],
                ),
        );
      },
    );
  }
}
