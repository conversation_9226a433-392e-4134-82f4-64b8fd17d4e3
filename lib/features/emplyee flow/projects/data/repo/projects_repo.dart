import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/projects/data/api%20services/projects_api_services.dart';
import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';

class ProjectsRepository {
  final ProjectsApiServices authApiServices;

  ProjectsRepository(this.authApiServices);

  Future<ApiResult<ProjectResponse>> getProjects({required int page}) async {
    final response = await authApiServices.getProjects(page:page );
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        ProjectResponse model = ProjectResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
