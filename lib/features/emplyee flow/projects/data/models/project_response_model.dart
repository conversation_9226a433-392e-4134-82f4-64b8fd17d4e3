import 'package:json_annotation/json_annotation.dart';

part 'project_response_model.g.dart';

@JsonSerializable()
class ProjectResponse {
  final String status;
  final String error;
  final int code;
  final ProjectData data;

  ProjectResponse({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory ProjectResponse.fromJson(Map<String, dynamic> json) =>
      _$ProjectResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectResponseToJson(this);
}

@JsonSerializable()
class ProjectData {
  final List<ProjectModel> data;
  final Links links;
  final PaginationMeta meta;

  ProjectData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory ProjectData.fromJson(Map<String, dynamic> json) =>
      _$ProjectDataFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectDataToJson(this);
}

@JsonSerializable()
class ProjectModel {
  final int id;
  final String name;
  final Client? client;
  final List<Employee> employees;

  @JsonKey(name: 'start_date')
  final String? startDate;

  final String? deadline;
  final String? status;
  final List<Task>? tasks;

  ProjectModel({
    required this.id,
    required this.name,
    required this.client,
    required this.employees,
    required this.startDate,
    required this.deadline,
    required this.status,
    required this.tasks,
  });

  factory ProjectModel.fromJson(Map<String, dynamic> json) =>
      _$ProjectModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProjectModelToJson(this);
}

@JsonSerializable()
class Client {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String gender;
  final String country;
  final dynamic employee;
  final String type;
  final String image;

  Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.gender,
    required this.country,
    this.employee,
    required this.type,
    required this.image,
  });

  factory Client.fromJson(Map<String, dynamic> json) => _$ClientFromJson(json);

  Map<String, dynamic> toJson() => _$ClientToJson(this);
}

@JsonSerializable()
class Task {
  final int id;
  final String name;
  final String project;
  final List<Employee> employees;

  @JsonKey(name: 'start_date')
  final String startDate;

  final String deadline;
  final String status;

  Task({
    required this.id,
    required this.name,
    required this.project,
    required this.employees,
    required this.startDate,
    required this.deadline,
    required this.status,
  });

  factory Task.fromJson(Map<String, dynamic> json) => _$TaskFromJson(json);

  Map<String, dynamic> toJson() => _$TaskToJson(this);
}

@JsonSerializable()
class Links {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  Links({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);

  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;
  final int? from;

  @JsonKey(name: 'last_page')
  final int lastPage;

  final List<PageLink> links;
  final String path;

  @JsonKey(name: 'per_page')
  final int perPage;

  final int? to;
  final int total;

  PaginationMeta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}

@JsonSerializable()
class PageLink {
  final String? url;
  final String label;
  final bool active;

  PageLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory PageLink.fromJson(Map<String, dynamic> json) =>
      _$PageLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}

@JsonSerializable()
class Employee {
  final int id;
  final String name;

  Employee({
    required this.id,
    required this.name,
  });

  factory Employee.fromJson(Map<String, dynamic> json) =>
      _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);
}
