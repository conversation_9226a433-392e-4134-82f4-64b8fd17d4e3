import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';
import 'package:erp/features/emplyee%20flow/projects/data/repo/projects_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'projects_state.dart';

class ProjectsCubit extends Cubit<ProjectsState> {
  ProjectsCubit(this._homeRepository) : super(ProjectsInitial()) {
    setupScrollController();
  }

  final ProjectsRepository _homeRepository;

  ProjectResponse? model;

  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreProjects();
      }
    });
  }

  Future<void> getProjects() async {
    emit(GetProjectsLoadingState());

    final result = await _homeRepository.getProjects(page: currentPage);
    result.when(
      success: (data) {
        model = data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetProjectsSuccessState());
      },
      failure: (errorHandler) {
        emit(GetProjectsErrorState());
      },
    );
  }

  Future<void> loadMoreProjects() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(GetProjectsLoadingMoreState());

    final nextPage = currentPage + 1;
    final result = await _homeRepository.getProjects(page: nextPage);

    result.when(
      success: (data) {
        model!.data.data.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetProjectsSuccessState());
      },
      failure: (errorHandler) {
        emit(GetProjectsErrorState());
      },
    );

    isLoadingMore = false;
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    return super.close();
  }
}
