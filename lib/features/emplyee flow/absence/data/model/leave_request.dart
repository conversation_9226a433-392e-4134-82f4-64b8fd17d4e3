import 'package:json_annotation/json_annotation.dart';

part 'leave_request.g.dart';

@JsonSerializable()
class LeaveRequestsResponse {
  @JsonKey(name: 'data')
  final DataWrapper data;

  @Json<PERSON>ey(name: 'status')
  final String status;

  @JsonKey(name: 'error')
  final String error;

  @JsonKey(name: 'code')
  final int code;

  LeaveRequestsResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory LeaveRequestsResponse.fromJson(Map<String, dynamic> json) =>
      _$LeaveRequestsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$LeaveRequestsResponseToJson(this);
}

@JsonSerializable()
class DataWrapper {
  @JsonKey(name: 'data')
  final List<LeaveRequest> data;

  @JsonKey(name: 'meta')
  final PaginationMeta meta;

  DataWrapper({
    required this.data,
    required this.meta,
  });

  factory DataWrapper.fromJson(Map<String, dynamic> json) =>
      _$DataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$DataWrapperToJson(this);
}

@JsonSerializable()
class LeaveRequest {
  @JsonKey(name: 'id')
  final int id;

  @JsonKey(name: 'start_date')
  final String? startDate;

  @JsonKey(name: 'end_date')
  final String endDate;

  @JsonKey(name: 'reason')
  final String reason;

  @JsonKey(name: 'attachments')
  final String? attachments;

  @JsonKey(name: 'type')
  final String type;

  @JsonKey(name: 'status')
  final String status;

  @JsonKey(name: 'reject_reason')
  final String? rejectReason;

  @JsonKey(name: 'manager')
  final String? manager;

  LeaveRequest({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.reason,
    this.attachments,
    required this.type,
    required this.status,
    this.rejectReason,
    this.manager,
  });

  factory LeaveRequest.fromJson(Map<String, dynamic> json) =>
      _$LeaveRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LeaveRequestToJson(this);
}

@JsonSerializable()
class PaginationMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;

  @JsonKey(name: 'last_page')
  final int lastPage;

  @JsonKey(name: 'total')
  final int total;

  PaginationMeta({
    required this.currentPage,
    required this.lastPage,
    required this.total,
  });

  factory PaginationMeta.fromJson(Map<String, dynamic> json) =>
      _$PaginationMetaFromJson(json);

  Map<String, dynamic> toJson() => _$PaginationMetaToJson(this);
}
