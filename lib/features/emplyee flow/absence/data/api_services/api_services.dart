import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class LeaveApiServices {
  LeaveApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> getLeavesRequest({required int page }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getLeaveRequest,
      data: {
        'page': page,
      },
    );
  }

  Future<Response?> postLeaveRequest({
    required String endDate,
    required String reason,
    required String type,
    File? file,
  }) async {
    final formData = FormData.fromMap({
      'start_date': endDate,
      'end_date': endDate,
      'reason': reason,
      'type': type,
      if (file != null)
        'attachments': [
          await MultipartFile.fromFile(file.path,
              filename: file.path.split('/').last),
        ],
    });

    return _dioFactory.post(
      endPoint: EndPoints.createLeaveRequest,
      data: formData,
    );
  }

  Future<Response?> updateLeaveRequest({
    required int requestId,
    required String startDate,
    required String endDate,
    required String reason,
    required String type,
    File? file,
  }) async {
    final formData = FormData.fromMap({
      'start_date': startDate,
      'end_date': endDate,
      'reason': reason,
      'type': type,
      if (file != null)
        'attachments': [
          await MultipartFile.fromFile(file.path,
              filename: file.path.split('/').last),
        ],
    });

    return _dioFactory.post(
      endPoint: '${EndPoints.updateLeaveRequest}/$requestId',
      data: formData,
    );
  }
}
