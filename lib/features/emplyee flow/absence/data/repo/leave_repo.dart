import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/absence/data/api_services/api_services.dart';
import 'package:erp/features/emplyee%20flow/absence/data/model/leave_request.dart';

class LeaveRepository {
  final LeaveApiServices _leaveApiServices;
  LeaveRepository(this._leaveApiServices);

  Future<ApiResult<LeaveRequestsResponse>> getLeavesRequest({required int page }) async {
    final response = await _leaveApiServices.getLeavesRequest(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        LeaveRequestsResponse model =
            LeaveRequestsResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<void>> submitLeaveRequest({
    required String endDate,
    required String reason,
    required String type,
    File? file,
  }) async {
    try {
      final response = await _leaveApiServices.postLeaveRequest(
        endDate: endDate,
        reason: reason,
        type: type,
        file: file,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        return const ApiResult.success(null);
      } else {
        return ApiResult.failure(
            ServerException.fromResponse(response?.statusCode, response));
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    }
  }

  Future<ApiResult<LeaveRequest>> updateLeaveRequest({
    required int requestId,
    required String startDate,
    required String endDate,
    required String reason,
    required String type,
    File? file,
  }) async {
    try {
      final response = await _leaveApiServices.updateLeaveRequest(
        requestId: requestId,
        startDate: startDate,
        endDate: endDate,
        reason: reason,
        file: file,
        type: type,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        LeaveRequest model = LeaveRequest.fromJson(response.data["data"]);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    } catch (e) {
      return ApiResult.failure(
          FailureException(errMessage: 'Unexpected error occurred'));
    }
  }
}
