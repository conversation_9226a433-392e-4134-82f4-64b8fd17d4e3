part of 'leave_cubit.dart';

abstract class LeaveState {}

final class LeaveInitial extends LeaveState {}

final class GetLeavesSuccessState extends LeaveState {}

final class GetLeavesLoadingState extends LeaveState {}

final class GetLeavesErrorState extends LeaveState {}

final class PickImageSuccessState extends LeaveState {}

final class PickImageErrorState extends LeaveState {}

final class SubmitLeaveLoadingState extends LeaveState {}

final class SubmitLeaveSuccessState extends LeaveState {}

final class SubmitLeaveErrorState extends LeaveState {}

class UpdateLeaveLoadingState extends LeaveState {}

class UpdateLeaveSuccessState extends LeaveState {}

class UpdateLeaveErrorState extends LeaveState {}

class GetLeavesLoadingMoreState extends LeaveState {}
