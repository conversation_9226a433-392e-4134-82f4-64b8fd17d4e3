import 'dart:io';

import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/absence/data/model/leave_request.dart';
import 'package:erp/features/emplyee%20flow/absence/data/repo/leave_repo.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'leave_state.dart';

class LeaveCubit extends Cubit<LeaveState> {
  LeaveCubit(this._leaveRepository) : super(LeaveInitial()) {
    setupScrollController();
  }

  final LeaveRepository _leaveRepository;
  List<LeaveRequest> pendingList = [];
  List<LeaveRequest> approvedList = [];
  List<LeaveRequest> rejectedList = [];
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;
  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 100 &&
          !isLoadingMore) {
        loadMoreLeaves();
      }
    });
  }

  Future<void> loadMoreLeaves() async {
    if (isLoadingMore || currentPage >= lastPage) {
      return;
    }
    isLoadingMore = true;
    currentPage++;
    emit(GetLeavesLoadingMoreState());
    final result = await _leaveRepository.getLeavesRequest(page: currentPage);

    result.when(
      success: (data) {
        for (var e in data.data.data) {
          if (e.status == 'pending') {
            pendingList.add(e);
          } else if (e.status == 'approved') {
            approvedList.add(e);
          } else {
            rejectedList.add(e);
          }
        }
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetLeavesSuccessState());
      },
      failure: (_) {
        emit(GetLeavesErrorState());
      },
    );

    isLoadingMore = false;
  }

  Future<void> getLeaves() async {
    pendingList = [];
    approvedList = [];
    rejectedList = [];
    currentPage = 1;
    lastPage = 1;
    emit(GetLeavesLoadingState());
    final result = await _leaveRepository.getLeavesRequest(page: currentPage);
    result.when(success: (model) {
      for (var e in model.data.data) {
        if (e.status == 'pending') {
          pendingList.add(e);
        } else if (e.status == 'approved') {
          approvedList.add(e);
        } else {
          rejectedList.add(e);
        }
      }
      currentPage = model.data.meta.currentPage;
      lastPage = model.data.meta.lastPage;
      emit(GetLeavesSuccessState());
    }, failure: (_) {
      emit(GetLeavesErrorState());
    });
  }

  final TextEditingController dateController = TextEditingController();
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController reasonController = TextEditingController();
  String? requestType;
  File? selectedFile;

  Future<void> pickFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result != null) {
      selectedFile = File(result.files.single.path!);

      emit(PickImageSuccessState());
    } else {
      emit(PickImageErrorState());
    }
  }

  Future<void> submitLeaveRequest() async {
    emit(SubmitLeaveLoadingState());
    showLoading();
    final result = await _leaveRepository.submitLeaveRequest(
      endDate: dateController.text,
      reason: reasonController.text,
      type: requestType!,
      file: selectedFile,
    );

    result.when(
      success: (_) async {
        hideLoading();
        await getLeaves();
        emit(SubmitLeaveSuccessState());
      },
      failure: (error) {
        hideLoading();
        emit(SubmitLeaveErrorState());
      },
    );
  }

  Future<void> updateLeaveRequest({required int requestId}) async {
    emit(UpdateLeaveLoadingState());
    showLoading();
    final result = await _leaveRepository.updateLeaveRequest(
      requestId: requestId,
      startDate: dateController.text,
      endDate: dateController.text,
      reason: reasonController.text,
      type: requestType!,
      file: selectedFile,
    );

    result.when(success: (model) async {
      pendingList[
          pendingList.indexWhere((element) => element.id == requestId)] = model;
      hideLoading();
      emit(UpdateLeaveSuccessState());
    }, failure: (error) {
      hideLoading();
      emit(UpdateLeaveErrorState());
    });
  }

  void clearData() {
    dateController.clear();
    reasonController.clear();
    requestType = null;
    selectedFile = null;
  }

  bool isImageFile(String path) {
    final List<String> imageExtensions = [
      'jpg',
      'jpeg',
      'png',
      'gif',
      'bmp',
      'webp'
    ];
    String extension = path.split('.').last.toLowerCase();
    return imageExtensions.contains(extension);
  }
}
