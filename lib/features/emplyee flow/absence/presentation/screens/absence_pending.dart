import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/date_formate.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_drop_down_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/data/model/leave_request.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/request_button_sheet_success.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AbsencePendingScreen extends StatelessWidget {
  const AbsencePendingScreen({super.key, required this.model});
  final LeaveRequest model;

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeaveCubit>();
    cubit.dateController.text = model.endDate;
    cubit.reasonController.text = model.reason;
    cubit.requestType = model.type;
    cubit.selectedFile = null;
    return Scaffold(
      appBar: AppBar(
        title: Text('absence.absencePending'.tr()),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp,
          vertical: 18.sp,
        ),
        child: Form(
          key: cubit.formKey,
          child: Column(
            spacing: 12.sp,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('absence.absenceType'.tr(), style: Styles.contentEmphasis),
              CustomDropdownButton<String>(
                items: [
                  "normal",
                  "sick",
                ],
                isString: true,
                value: model.type,
                validator: (value) {
                  return AppValidator.validateEmptyText("Absence Type", value);
                },
                onChanged: (v) {
                  cubit.requestType = v;
                },
              ),
              Text('absence.meetingDate'.tr(), style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.dateController,
                hintText: 'absence.enteryourMeetingDate'.tr(),
                validator: (value) {
                  return AppValidator.validateEmptyText("date", value);
                },
                readOnly: true,
                suffixIcon: Padding(
                  padding: EdgeInsets.all(8.0.sp),
                  child: ImagesWidget(
                      height: 30.sp,
                      image: Assets.assetsImagesSvgsCalenderIconTextFormField),
                ),
                onTap: () {
                  showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime(2100),
                  ).then(
                    (v) {
                      if (v != null) {
                        cubit.dateController.text = formatDate(v.toString());
                      }
                    },
                  );
                },
              ),
              Text('absence.image'.tr(),
                  style:
                      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600)),
              InkWell(
                onTap: () {
                  cubit.pickFile();
                },
                child: Container(
                  height: 102.sp,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    border: Border.all(color: AppColors.neutralColor600),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: BlocBuilder<LeaveCubit, LeaveState>(
                    buildWhen: (previous, current) =>
                        current is PickImageErrorState ||
                        current is PickImageSuccessState,
                    builder: (context, state) {
                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (cubit.selectedFile == null) ...[
                            if (model.attachments != null &&
                                (model.attachments!.endsWith('.png') ||
                                    model.attachments!.endsWith('.jpg') ||
                                    model.attachments!.endsWith('.jpeg') ||
                                    model.attachments!.endsWith('.gif')))
                              Expanded(
                                child: CachedNetworkImage(
                                  imageUrl: model.attachments!,
                                  fit: BoxFit.cover,
                                  height: 100.sp,
                                  placeholder: (context, url) => Center(
                                      child: CircularProgressIndicator()),
                                  errorWidget: (context, url, error) => Center(
                                    child: Icon(
                                      Icons.image_not_supported_outlined,
                                      size: 40.sp,
                                      color: AppColors.primaryColor800,
                                    ),
                                  ),
                                ),
                              )
                            else if (model.attachments != null &&
                                model.attachments!.endsWith('.pdf'))
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(Icons.picture_as_pdf,
                                        size: 40.sp,
                                        color: AppColors.primaryColor800),
                                    SizedBox(height: 8.sp),
                                    Text(
                                      model.attachments!.split('/').last,
                                      style: Styles.captionEmphasis.copyWith(
                                          color: AppColors.primaryColor800),
                                    ),
                                  ],
                                ),
                              )
                            else
                              Text(
                                'absence.novalidattachmentfound'.tr(),
                                style: Styles.captionEmphasis
                                    .copyWith(color: AppColors.neutralColor600),
                              )
                          ] else if (cubit
                              .isImageFile(cubit.selectedFile!.path))
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Image.file(cubit.selectedFile!),
                              ),
                            )
                          else if (cubit.selectedFile!.path.endsWith('.pdf'))
                            Expanded(
                              child: Column(
                                children: [
                                  Icon(Icons.picture_as_pdf,
                                      size: 40,
                                      color: AppColors.primaryColor800),
                                  SizedBox(height: 8.sp),
                                  Text(
                                    cubit.selectedFile!.path.split('/').last,
                                    style: Styles.captionEmphasis.copyWith(
                                        color: AppColors.primaryColor800),
                                  ),
                                ],
                              ),
                            )
                          else
                            Text(
                              cubit.selectedFile!.path.split('/').last,
                              style: Styles.captionEmphasis
                                  .copyWith(color: AppColors.primaryColor800),
                            ),
                          SizedBox(height: 8.sp),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                'absence.uploadPicture'.tr(),
                                style: Styles.captionEmphasis
                                    .copyWith(color: AppColors.neutralColor600),
                              ),
                              Icon(Icons.file_upload_outlined,
                                  color: AppColors.neutralColor600),
                            ],
                          ),
                        ],
                      );
                    },
                  ),
                ),
              ),
              Text('absence.reason'.tr(), style: Styles.contentEmphasis),
              CustomTextFormFieldWidget(
                controller: cubit.reasonController,
                height: 80.sp,
                maxLines: 10,
                isChat: true,
                hintText: 'absence.enterTheReason'.tr(),
                validator: (value) {
                  return AppValidator.validateEmptyText("Reason", value);
                },
                textAlignVertical: TextAlignVertical.top,
                contentPadding: EdgeInsets.only(
                  top: 15.h,
                  left: 10.w,
                  right: 10.w,
                  bottom: 80.sp,
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(12.sp),
        child: BlocListener<LeaveCubit, LeaveState>(
          listenWhen: (previous, current) =>
              current is UpdateLeaveSuccessState ||
              current is UpdateLeaveLoadingState ||
              current is UpdateLeaveErrorState,
          listener: (context, state) {
            if (state is UpdateLeaveSuccessState) {
              showAbsenceBottomSheet(context);
            }
          },
          child: CustomButtonWidget(
            text: 'absence.send'.tr(),
            onPressed: () {
              if (cubit.formKey.currentState!.validate()) {
                cubit.updateLeaveRequest(requestId: model.id);
              }
            },
          ),
        ),
      ),
    );
  }
}
