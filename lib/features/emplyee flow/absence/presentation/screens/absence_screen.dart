import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/screens/request_absence_screen.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/absence_skelation_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/approved_list_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/pending_list_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/reject_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';

class AbsenceScreen extends StatelessWidget {
  const AbsenceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeaveCubit>();
    return BlocBuilder<LeaveCubit, LeaveState>(
      buildWhen: (previous, current) =>
          current is GetLeavesErrorState ||
          current is GetLeavesLoadingState ||
          current is GetLeavesSuccessState,
      builder: (context, state) {
        if (state is GetLeavesLoadingState) {
          return AbsenceSkeletonizer();
        }
        return Scaffold(
          appBar: AppBar(
            title: Text('absence.absence'.tr()),
            centerTitle: false,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(
              vertical: 15.sp,
            ),
            child: DefaultTabController(
              length: 3,
              child: Column(
                spacing: 20.sp,
                children: [
                  TabBar(
                    dividerHeight: 0,
                    unselectedLabelColor: AppColors.neutralColor600,
                    labelStyle: Styles.highlightEmphasis.copyWith(
                      color: AppColors.primaryColor800,
                    ),
                    unselectedLabelStyle: Styles.highlightEmphasis.copyWith(
                      color: AppColors.neutralColor600,
                    ),
                    indicatorColor: AppColors.primaryColor800,
                    dividerColor: AppColors.neutralColor300,
                    automaticIndicatorColorAdjustment: true,
                    indicatorSize: TabBarIndicatorSize.tab,
                    tabs: [
                      Tab(text: 'absence.reject'.tr()),
                      Tab(text: 'absence.accept'.tr()),
                      Tab(text: 'absence.Pending'.tr()),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        RejectedLeavesList(),
                        ApprovedLeavesList(),
                        PendingLeavesList(),
                      ],
                    ),
                  ),
                  BlocBuilder<LeaveCubit, LeaveState>(
                    buildWhen: (previous, current) =>
                        current is GetLeavesSuccessState ||
                        current is GetLeavesLoadingMoreState,
                    builder: (context, state) {
                      if (state is GetLeavesLoadingMoreState) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  )
                ],
              ),
            ),
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 16.sp),
            child: CustomButtonWidget(
              text: 'absence.requestAbsence'.tr(),
              onPressed: () {
                Navigator.of(context).push(
                  PageTransition(
                    type: PageTransitionType.fade,
                    child: BlocProvider.value(
                      value: cubit..clearData(),
                      child: RequestAbsenceScreen(),
                    ),
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
