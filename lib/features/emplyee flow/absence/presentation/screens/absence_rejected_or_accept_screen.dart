import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/data/model/leave_request.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/rejected_and_widget.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/request_button_sheet_success.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AbsenceRejectionOrAcceptingScreen extends StatelessWidget {
  const AbsenceRejectionOrAcceptingScreen(
      {super.key, required this.isReject, required this.model});
  final bool isReject;
  final LeaveRequest model;
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeaveCubit>();
    cubit.dateController.text = model.endDate;
    cubit.reasonController.text = model.reason;
    cubit.requestType = model.type;

    return Scaffold(
      appBar: AppBar(
        title: Text(!isReject
            ? 'absence.absenceRejection'.tr()
            : 'absence.absenceAccept'.tr()),
        centerTitle: false,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(
          horizontal: 12.sp,
          vertical: 18.sp,
        ),
        child: Column(
          spacing: 12.sp,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AbsenceWidget(
              projectInfo: [
                {"title": 'absence.absenceType'.tr(), "value": model.type},
                {
                  "title": 'absence.requestedOn'.tr(),
                  "value": model.startDate!
                },
              ],
              isCompleted: isReject,
              reason: model.reason,
              status: model.status,
              attachment: model.attachments,
              rejectonReason: model.rejectReason,
            ),
          ],
        ),
      ),
      bottomNavigationBar: isReject
          ? SizedBox.shrink()
          : SafeArea(
              minimum: EdgeInsets.all(12.sp),
              child: BlocListener<LeaveCubit, LeaveState>(
                listenWhen: (previous, current) =>
                    current is SubmitLeaveErrorState ||
                    current is SubmitLeaveSuccessState ||
                    current is SubmitLeaveLoadingState,
                listener: (context, state) {
                  if (state is SubmitLeaveSuccessState) {
                    showAbsenceBottomSheet(context);
                  }
                },
                child: CustomButtonWidget(
                  text: 'absence.resend'.tr(),
                  onPressed: () {
                    cubit.submitLeaveRequest();
                  },
                ),
              ),
            ),
    );
  }
}
