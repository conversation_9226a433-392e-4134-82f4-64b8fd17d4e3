import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/screens/absence_pending.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/no_leaves_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:page_transition/page_transition.dart';

class PendingLeavesList extends StatelessWidget {
  const PendingLeavesList({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeaveCubit>();
    return BlocBuilder<LeaveCubit, LeaveState>(
      buildWhen: (previous, current) => current is UpdateLeaveSuccessState,
      builder: (context, state) {
        return cubit.pendingList.isEmpty
            ? NoLeavesWidget()
            : ListView.separated(
                controller: cubit.scrollController,
                padding: EdgeInsets.symmetric(horizontal: 16.sp),
                separatorBuilder: (context, index) => 16.verticalSpace,
                itemCount: cubit.pendingList.length,
                itemBuilder: (context, index) {
                  final request = cubit.pendingList[index];
                  return ProjectsItemWidget(
                    onTap: () {
                      Navigator.of(context).push(
                        PageTransition(
                          type: PageTransitionType.fade,
                          child: BlocProvider.value(
                            value: cubit,
                            child: AbsencePendingScreen(model: request),
                          ),
                        ),
                      );
                    },
                    title: request.type,
                    description: request.startDate!,
                  );
                },
              );
      },
    );
  }
}
