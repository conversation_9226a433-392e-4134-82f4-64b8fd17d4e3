import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AbsenceSkeletonizer extends StatelessWidget {
  const AbsenceSkeletonizer({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text("Absence"),
          centerTitle: false,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(
            vertical: 15.sp,
          ),
          child: DefaultTabController(
            length: 3,
            child: Column(
              spacing: 20.sp,
              children: [
                TabBar(
                  unselectedLabelColor: AppColors.neutralColor600,
                  labelStyle: Styles.highlightEmphasis.copyWith(
                    color: AppColors.primaryColor800,
                  ),
                  unselectedLabelStyle: Styles.highlightEmphasis.copyWith(
                    color: AppColors.neutralColor600,
                  ),
                  indicatorColor: AppColors.primaryColor800,
                  dividerColor: AppColors.neutralColor300,
                  automaticIndicatorColorAdjustment: true,
                  indicatorSize: TabBarIndicatorSize.tab,
                  tabs: [
                    Tab(text: "Reject"),
                    Tab(text: "Accept"),
                    Tab(text: "Pending"),
                  ],
                ),
                Expanded(
                  child: TabBarView(
                    children: [
                      ListView.separated(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.sp,
                        ),
                        separatorBuilder: (context, index) {
                          return 16.verticalSpace;
                        },
                        itemCount: 3,
                        itemBuilder: (context, index) {
                          return ProjectsItemWidget(
                              title: "type", description: "2025-03-04");
                        },
                      ),
                      ListView.separated(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.sp,
                        ),
                        separatorBuilder: (context, index) {
                          return 16.verticalSpace;
                        },
                        itemCount: 3,
                        itemBuilder: (context, index) {
                          return ProjectsItemWidget(
                              title: "type", description: "2025-03-04");
                        },
                      ),
                      ListView.separated(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.sp,
                        ),
                        separatorBuilder: (context, index) {
                          return 16.verticalSpace;
                        },
                        itemCount: 3,
                        itemBuilder: (context, index) {
                          return ProjectsItemWidget(
                              title: "type", description: "2025-03-04");
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: SafeArea(
          minimum: EdgeInsets.symmetric(vertical: 10.sp, horizontal: 16.sp),
          child: CustomButtonWidget(
            text: "Request Absence",
          ),
        ),
      ),
    );
  }
}
