import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NoLeavesWidget extends StatelessWidget {
  const NoLeavesWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.0.sp),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ImagesWidget(image: Assets.assetsImagesSvgsNoAbsenceIcon),
            SizedBox(height: 20.sp),
            Text('absence.haventSubmitted'.tr(),
                textAlign: TextAlign.center,
                style:
                    Styles.featureEmphasis.copyWith(color: Color(0xff000000))),
            Sized<PERSON><PERSON>(height: 10.sp),
            Text('absence.submitYourAbsenceRequest'.tr(),
                textAlign: TextAlign.center,
                style: Styles.contentEmphasis
                    .copyWith(color: Color(0xff000000).withValues(alpha: 0.6))),
          ],
        ),
      ),
    );
  }
}
