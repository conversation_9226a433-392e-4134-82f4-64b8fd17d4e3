import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_continer_widgt.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AbsenceWidget extends StatelessWidget {
  final List<Map<String, String>> projectInfo;

  final bool isCompleted;
  final String reason;
  final String status;
  final String? rejectonReason;
  final String? attachment;
  const AbsenceWidget({
    super.key,
    required this.projectInfo,
    required this.isCompleted,
    required this.reason,
    required this.status,
    this.rejectonReason,
    this.attachment,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 16.sp,
      children: [
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: projectInfo.map((info) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(info["title"]!,
                        style: Styles.contentEmphasis.copyWith(
                          fontWeight: FontWeight.w600,
                        )),
                    Text(info["value"]!,
                        style: Styles.contentEmphasis
                            .copyWith(fontWeight: FontWeight.w400)),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        CustomCard(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('absence.image'.tr(),
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                  )),
              ImagesWidget(
                  height: 30.sp, image: Assets.assetsImagesSvgsGallery),
            ],
          ),
        ),
        CustomCard(
          child: Column(
            spacing: 12.sp,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('absence.reason'.tr(),
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                  )),
              Text(
                reason,
                style: Styles.contentEmphasis.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
        CustomCard(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('clientsDetails.Status'.tr(),
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                  )),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 5.h),
                decoration: BoxDecoration(
                  color: isCompleted
                      ? AppColors.greenColor200.withValues(alpha: 0.1)
                      : AppColors.redColor100.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4.r),
                ),
                child: Text(
                  status,
                  style: Styles.captionEmphasis.copyWith(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.bold,
                    color: isCompleted
                        ? AppColors.greenColor200
                        : AppColors.redColor100,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (!isCompleted)
          CustomCard(
            child: Column(
              spacing: 12.sp,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('absence.reasonOfRejection'.tr(),
                    style: Styles.contentEmphasis.copyWith(
                      fontWeight: FontWeight.w600,
                    )),
                Text(
                  rejectonReason!,
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
