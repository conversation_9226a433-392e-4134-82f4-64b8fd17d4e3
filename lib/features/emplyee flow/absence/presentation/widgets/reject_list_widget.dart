import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/features/emplyee%20flow/absence/bloc/cubit/leave_cubit.dart';
import 'package:erp/features/emplyee%20flow/absence/presentation/widgets/no_leaves_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RejectedLeavesList extends StatelessWidget {
  const RejectedLeavesList({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeaveCubit>();

    return cubit.rejectedList.isEmpty
        ? NoLeavesWidget()
        : ListView.separated(
          controller: cubit.scrollController,
            padding: EdgeInsets.symmetric(horizontal: 16.sp),
            separatorBuilder: (context, index) => 16.verticalSpace,
            itemCount: cubit.rejectedList.length,
            itemBuilder: (context, index) {
              final request = cubit.rejectedList[index];
              return ProjectsItemWidget(
                onTap: () {
                  context.pushNamed(Routes.absenceRejectionOrAcceptingScreen,
                      arguments: request);
                },
                title: request.type,
                description: request.startDate!,
              );
            },
          );
  }
}
