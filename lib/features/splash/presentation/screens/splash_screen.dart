import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/splash/business_logic/splash_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';

class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.neutralColor100,
      body: BlocBuilder<SplashCubit, SplashState>(
        builder: (context, state) {
          return Stack(
            children: [
              Center(
                child: SvgPicture.asset('assets/images/svgs/splash_logo.svg'),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 140,
                child: Column(
                  children: [
                    Text(
                      'JEC ERP',
                      style: Styles.heading2,
                    ),
                    Text(
                      'splash.poweringEfficiency'.tr(),
                      style: Styles.captionRegular.copyWith(
                        color: AppColors.neutralColor1600,
                      ),
                    ),
                    if (state is SplashLoading) ...[
                      const SizedBox(height: 24),
                      const CircularProgressIndicator(),
                    ],
                    if (state is SplashError) ...[
                      const SizedBox(height: 16),
                      Text(
                        state.message,
                        style: TextStyle(color: Colors.red),
                      )
                    ]
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
