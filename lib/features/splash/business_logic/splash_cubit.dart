import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/cache_helper/cache_helper.dart';
import 'package:erp/core/cache_helper/cache_keys.dart';
import 'package:erp/core/helper_functions/roles_function.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

part 'splash_state.dart';

class SplashCubit extends Cubit<SplashState> {
  SplashCubit() : super(SplashInitial());

  Future<void> startAppFlow() async {
    emit(SplashLoading());
    final statuses = await [Permission.location].request();

    if (statuses[Permission.location]?.isGranted ?? false) {
      await _getCurrentLocation();
    } else {
      await openAppSettings();
      emit(SplashError("Location permission denied."));
    }
  }

  Future<void> _getCurrentLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        await _showLocationServiceDialog();
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        permission = await Geolocator.requestPermission();
        if (permission != LocationPermission.whileInUse &&
            permission != LocationPermission.always) {
          emit(SplashError("Location permission permanently denied."));
          return;
        }
      }

      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      if (kDebugMode) {
        print("Location: ${position.latitude}, ${position.longitude}");
      }

      await CacheHelper.saveData(
        key: CacheKeys.userLatitude,
        value: position.latitude.toString(),
      );
      await CacheHelper.saveData(
        key: CacheKeys.userLongitude,
        value: position.longitude.toString(),
      );

      navigateBasedOnRole();
    } catch (e) {
      emit(SplashError("Failed to get location: $e"));
    }
  }

  Future<void> _showLocationServiceDialog() async {
    await showDialog(
      context: AppConstants.navigatorKey.currentContext!,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          backgroundColor: AppColors.neutralColor100,
          title: Row(
            children: [
              Icon(Icons.location_disabled, color: AppColors.primaryColor900),
              SizedBox(width: 8),
              Text(
                'location_disabled'.tr(),
                style: Styles.heading4.copyWith(
                  color: AppColors.neutralColor1000,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'enable_location_services_description'.tr(),
                style: Styles.contentRegular.copyWith(
                  color: AppColors.neutralColor1300,
                ),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.neutralColor200,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.primaryColor100),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb_outline,
                        color: AppColors.primaryColor900),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'location_features_explanation'.tr(),
                        style: Styles.captionEmphasis.copyWith(
                          color: AppColors.neutralColor1300,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            CustomButtonWidget(
              text: 'enable_location'.tr(),
              color: AppColors.primaryColor800,
              borderRadius: 8,
              textStyle: Styles.highlightBold.copyWith(
                color: AppColors.neutralColor100,
              ),
              onPressed: () async {
                Navigator.of(
                  AppConstants.navigatorKey.currentContext!,
                ).pop();
                await Geolocator.openLocationSettings();
                Future.delayed(const Duration(seconds: 3), () {
                  _getCurrentLocation();
                });
              },
            ),
          ],
        );
      },
    );
  }
}
