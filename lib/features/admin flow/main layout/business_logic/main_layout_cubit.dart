import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/features/admin%20flow/main%20layout/business_logic/main_layout_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MainLayoutAdminCubit extends Cubit<MainLayoutAdminState> {
  MainLayoutAdminCubit() : super(MainLayoutAdminInitial());

  static MainLayoutAdminCubit get(context) => BlocProvider.of(context);
  void changeBottomNavBar(index) {
    AppConstants.mainLayoutInitialScreenIndex = index;
    emit(AppBottomAdminNavState(AppConstants.mainLayoutInitialScreenIndex));
  }
}
