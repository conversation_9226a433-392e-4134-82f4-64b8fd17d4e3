import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/hr/absence/bloc/cubit/hr_absence_state.dart';
import 'package:erp/features/hr/absence/data/model/hr_leave_model.dart';
import 'package:erp/features/hr/absence/data/repo/hr_absence_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HRAbsneceCubit extends Cubit<HrAbsneceState> {
  HRAbsneceCubit(this._repo) : super(AbsneceInitial());

  final HrAbsneceRepo _repo;

  HrLeaveModel? permissionRequestResponse;
  List<PermissionRequestItem> permissions = [];

  final ScrollController scrollController = ScrollController();
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final TextEditingController reasonController = TextEditingController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMorePermissionRequests();
      }
    });
  }

  Future<void> hrGetLeaves() async {
    emit(GetPermissonLoadingState());

    final result = await _repo.hrGetLeaves(page: currentPage); // Pass page
    result.when(
      success: (data) {
        permissionRequestResponse = data;
        permissions = data.data.data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetPermissonSuccessState());
      },
      failure: (error) {
        emit(GetPermissonErrorState());
      },
    );
  }

  Future<void> loadMorePermissionRequests() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    emit(GetPermissonLoadingMoreState());
    isLoadingMore = true;
    final nextPage = currentPage + 1;

    final result = await _repo.hrGetLeaves(page: nextPage);
    result.when(
      success: (data) {
        permissions.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        emit(GetPermissonSuccessState());
      },
      failure: (error) {
        emit(GetPermissonErrorState());
      },
    );

    isLoadingMore = false;
  }

  Future<void> updatePermissionStatus({
    required String status,
    required int id,
  }) async {
    emit(UpdatePermissonLoadingState());
    showLoading();
    final result = await _repo.hrUpdateLeave(
      status: status,
      reason: reasonController.text,
      id: id,
    );

    result.when(
      success: (data) {
        final index =
            permissions.indexWhere((element) => element.id == data.id);
        if (index != -1) {
          permissions[index] = data;
        }
        reasonController.clear();
        hideLoading();
        customToast(msg: "success", color: AppColors.greenColor100);

        emit(UpdatePermissonSuccessState());
      },
      failure: (error) {
        hideLoading();

        emit(UpdatePermissonErrorState());
      },
    );
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    reasonController.dispose();
    return super.close();
  }
}
