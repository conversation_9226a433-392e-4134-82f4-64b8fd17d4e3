import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class HRAskingAbsneceApiServices {
  HRAskingAbsneceApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> hrGetLeaves({required int page}) async {
    return _dioFactory.get(
      endPoint: EndPoints.hrGetLeaves,
      data: {
        "page": page,
      },
    );
  }

  Future<Response?> hrUpdateLeave({
    required String status,
    required int id,
    String? reason,
  }) async {
    return _dioFactory.post(
      endPoint: EndPoints.hrUpdateLeave(id),
      data: {"status": status, "reject_reason": reason},
    );
  }
}
