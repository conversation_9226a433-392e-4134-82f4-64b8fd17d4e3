import 'package:json_annotation/json_annotation.dart';

part 'hr_leave_model.g.dart';

@JsonSerializable(explicitToJson: true)
class HrLeaveModel {
  final HrLeaveData data;
  final String status;
  final String error;
  final int code;

  HrLeaveModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory HrLeaveModel.fromJson(Map<String, dynamic> json) =>
      _$HrLeaveModelFromJson(json);
  Map<String, dynamic> toJson() => _$HrLeaveModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class HrLeaveData {
  final List<PermissionRequestItem> data;
  final HrLeaveLinks links;
  final HrLeaveMeta meta;

  HrLeaveData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory HrLeaveData.fromJson(Map<String, dynamic> json) =>
      _$HrLeaveDataFromJson(json);
  Map<String, dynamic> toJson() => _$HrLeaveDataToJson(this);
}

@JsonSerializable()
class PermissionRequestItem {
  final int id;
  @JsonKey(name: 'start_date')
  final String startDate;
  @JsonKey(name: 'end_date')
  final String endDate;
  final String reason;
  final String? attachments;
  final String type;
  final String status;
  @JsonKey(name: 'reject_reason')
  final String? rejectReason;
  final String? manager;
  final String employee;
  @JsonKey(name: 'employee_id')
  final int employeeId;
  final String email;

  PermissionRequestItem({
    required this.id,
    required this.startDate,
    required this.endDate,
    required this.reason,
    this.attachments,
    required this.type,
    required this.status,
    this.rejectReason,
    this.manager,
    required this.employee,
    required this.employeeId,
    required this.email,
  });

  factory PermissionRequestItem.fromJson(Map<String, dynamic> json) =>
      _$PermissionRequestItemFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRequestItemToJson(this);
}

@JsonSerializable()
class HrLeaveLinks {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  HrLeaveLinks({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory HrLeaveLinks.fromJson(Map<String, dynamic> json) =>
      _$HrLeaveLinksFromJson(json);
  Map<String, dynamic> toJson() => _$HrLeaveLinksToJson(this);
}

@JsonSerializable(explicitToJson: true)
class HrLeaveMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;
  final int from;
  @JsonKey(name: 'last_page')
  final int lastPage;
  final List<HrLeaveMetaLink> links;
  final String path;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int to;
  final int total;

  HrLeaveMeta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory HrLeaveMeta.fromJson(Map<String, dynamic> json) =>
      _$HrLeaveMetaFromJson(json);
  Map<String, dynamic> toJson() => _$HrLeaveMetaToJson(this);
}

@JsonSerializable()
class HrLeaveMetaLink {
  final String? url;
  final String label;
  final bool active;

  HrLeaveMetaLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory HrLeaveMetaLink.fromJson(Map<String, dynamic> json) =>
      _$HrLeaveMetaLinkFromJson(json);
  Map<String, dynamic> toJson() => _$HrLeaveMetaLinkToJson(this);
}
