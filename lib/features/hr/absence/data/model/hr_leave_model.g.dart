// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hr_leave_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HrLeaveModel _$HrLeaveModelFromJson(Map<String, dynamic> json) => HrLeaveModel(
      data: HrLeaveData.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$HrLeaveModelToJson(HrLeaveModel instance) =>
    <String, dynamic>{
      'data': instance.data.toJson(),
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

HrLeaveData _$HrLeaveDataFromJson(Map<String, dynamic> json) => HrLeaveData(
      data: (json['data'] as List<dynamic>)
          .map((e) => PermissionRequestItem.fromJson(e as Map<String, dynamic>))
          .toList(),
      links: HrLeaveLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: HrLeaveMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$HrLeaveDataToJson(HrLeaveData instance) =>
    <String, dynamic>{
      'data': instance.data.map((e) => e.toJson()).toList(),
      'links': instance.links.toJson(),
      'meta': instance.meta.toJson(),
    };

PermissionRequestItem _$PermissionRequestItemFromJson(
        Map<String, dynamic> json) =>
    PermissionRequestItem(
      id: (json['id'] as num).toInt(),
      startDate: json['start_date'] as String,
      endDate: json['end_date'] as String,
      reason: json['reason'] as String,
      attachments: json['attachments'] as String?,
      type: json['type'] as String,
      status: json['status'] as String,
      rejectReason: json['reject_reason'] as String?,
      manager: json['manager'] as String?,
      employee: json['employee'] as String,
      employeeId: (json['employee_id'] as num).toInt(),
      email: json['email'] as String,
    );

Map<String, dynamic> _$PermissionRequestItemToJson(
        PermissionRequestItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'start_date': instance.startDate,
      'end_date': instance.endDate,
      'reason': instance.reason,
      'attachments': instance.attachments,
      'type': instance.type,
      'status': instance.status,
      'reject_reason': instance.rejectReason,
      'manager': instance.manager,
      'employee': instance.employee,
      'employee_id': instance.employeeId,
      'email': instance.email,
    };

HrLeaveLinks _$HrLeaveLinksFromJson(Map<String, dynamic> json) => HrLeaveLinks(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$HrLeaveLinksToJson(HrLeaveLinks instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };

HrLeaveMeta _$HrLeaveMetaFromJson(Map<String, dynamic> json) => HrLeaveMeta(
      currentPage: (json['current_page'] as num).toInt(),
      from: (json['from'] as num).toInt(),
      lastPage: (json['last_page'] as num).toInt(),
      links: (json['links'] as List<dynamic>)
          .map((e) => HrLeaveMetaLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      path: json['path'] as String,
      perPage: (json['per_page'] as num).toInt(),
      to: (json['to'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$HrLeaveMetaToJson(HrLeaveMeta instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'links': instance.links.map((e) => e.toJson()).toList(),
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
    };

HrLeaveMetaLink _$HrLeaveMetaLinkFromJson(Map<String, dynamic> json) =>
    HrLeaveMetaLink(
      url: json['url'] as String?,
      label: json['label'] as String,
      active: json['active'] as bool,
    );

Map<String, dynamic> _$HrLeaveMetaLinkToJson(HrLeaveMetaLink instance) =>
    <String, dynamic>{
      'url': instance.url,
      'label': instance.label,
      'active': instance.active,
    };
