import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/hr/absence/data/api_services/hr_absence_api_services.dart';
import 'package:erp/features/hr/absence/data/model/hr_leave_model.dart';

class HrAbsneceRepo {
  final HRAskingAbsneceApiServices _leaveApiServices;
  HrAbsneceRepo(this._leaveApiServices);

  Future<ApiResult<HrLeaveModel>> hrGetLeaves({required int page}) async {
    final response = await _leaveApiServices.hrGetLeaves(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        HrLeaveModel model = HrLeaveModel.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<PermissionRequestItem>> hrUpdateLeave({
    required String status,
    String? reason,
    required int id,
  }) async {
    try {
      final response = await _leaveApiServices.hrUpdateLeave(
        id: id,
        status: status,
        reason: reason,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        PermissionRequestItem model =
            PermissionRequestItem.fromJson(response.data["data"]);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    } catch (e) {
      return ApiResult.failure(
          FailureException(errMessage: 'Unexpected error occurred'));
    }
  }
}
