import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/rejected_and_accetp_widget.dart';
import 'package:erp/features/hr/absence/bloc/cubit/hr_absence_cubit.dart';
import 'package:erp/features/hr/absence/bloc/cubit/hr_absence_state.dart';
import 'package:erp/features/hr/absence/data/model/hr_leave_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HrAbsenceDetails extends StatelessWidget {
  const HrAbsenceDetails({super.key, required this.model});
  final PermissionRequestItem model;
  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HRAbsneceCubit>();

    return Scaffold(
        appBar: AppBar(
          title: Text(model.employee),
          centerTitle: false,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: 12.sp,
            vertical: 18.sp,
          ),
          child: AskingPermissonWidget(
            projectInfo: [
              {"title": 'absence.absenceType'.tr(), "value": model.type},
              {
                "title": 'absence.requestedOn'.tr(),
                "value": model.startDate,
              },
            ],
            isCompleted: false,
            rejectonReason: model.rejectReason,
            reason: model.reason,
            status: model.status,
          ),
        ),
        bottomNavigationBar: model.status != "pending"
            ? SizedBox.shrink()
            : Padding(
                padding: EdgeInsets.all(20.0.sp),
                child: Row(
                  spacing: 20.sp,
                  children: [
                    Expanded(
                      child: CustomButtonWidget(
                        text: 'moreSales.reject'.tr(),
                        textColor: AppColors.primaryColor800,
                        color: Colors.white,
                        borderColor:
                            BorderSide(color: AppColors.primaryColor800),
                        onPressed: () {
                          context.pushNamed(Routes.sendAbsencePermission,
                              arguments: HrAbsenceArguments(
                                  cubit: cubit, model: model));
                        },
                      ),
                    ),
                    Expanded(
                      child: BlocListener<HRAbsneceCubit, HrAbsneceState>(
                        listenWhen: (previous, current) =>
                            current is UpdatePermissonErrorState ||
                            current is UpdatePermissonLoadingState ||
                            current is UpdatePermissonSuccessState,
                        listener: (context, state) {
                          if (state is UpdatePermissonSuccessState) {
                            context.pop();
                            cubit.emit(GetPermissonSuccessState());
                          }
                        },
                        child: CustomButtonWidget(
                          text: 'moreSales.approve'.tr(),
                          onPressed: () {
                            cubit.updatePermissionStatus(
                                status: "approved", id: model.id);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ));
  }
}
