import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/cubit/home_cubit.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/banners_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/custom_make_attendance_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/home_skelton_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/home_top_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/statistics_item_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HrHomeScreen extends StatelessWidget {
  const HrHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<HomeCubit>();
    return BlocBuilder<HomeCubit, HomeState>(
      buildWhen: (previous, current) =>
          current is GetHomeLoadingState ||
          current is GetHomeSuccessState ||
          current is GetHomeErrorState,
      builder: (context, state) {
        if (state is GetHomeLoadingState) {
          return HomeSkeltonWidget();
        } else {
          return Scaffold(
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                HomeTopWidget(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: EdgeInsets.all(16.sp),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          BannerWidget(),
                          16.verticalSpace,
                          CustomMakeAttendanceWidget(),
                          16.verticalSpace,
                          Text(
                            'homeHr.statisticsoftheyears'.tr(),
                            style: Styles.contentBold.copyWith(
                                fontSize: 18.sp, fontWeight: FontWeight.w500),
                          ),
                          12.verticalSpace,
                          Row(
                            spacing: 16.sp,
                            children: [
                              StatisticsItemWidget(
                                title: 'homeHr.absence'.tr(),
                                total: homeCubit.model!.data.annualLeaveDays
                                    .toString(),
                                value: (homeCubit.model!.data.annualLeaveDays -
                                        homeCubit.model!.data
                                            .availableAnnualLeaveDays)
                                    .toString(),
                              ),
                              Expanded(child: SizedBox.fromSize())
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
          );
        }
      },
    );
  }
}
