import 'package:erp/features/hr/all%20empolyee/data/models/all_emplyee_model.dart';
import 'package:erp/features/hr/all%20empolyee/data/repos/all_emplyee_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'all_emplyee_state.dart';

class AllEmplyeeCubit extends Cubit<AllEmplyeeState> {
  AllEmplyeeCubit(this._departmentRepo) : super(AllEmplyeeInitial());
  final AllEmplyeeRepo _departmentRepo;
  AllEmplyeeModel? allEmployeeResponse;
  final ScrollController allEmployeeScrollController = ScrollController();
  int allEmployeeCurrentPage = 1;
  int allEmployeeLastPage = 1;
  bool isLoadingMoreAllEmployees = false;

  void setupAllEmployeeScrollController() {
    allEmployeeScrollController.addListener(() {
      if (allEmployeeScrollController.position.pixels >=
              allEmployeeScrollController.position.maxScrollExtent - 100 &&
          !isLoadingMoreAllEmployees) {
        loadMoreAllEmployees();
      }
    });
  }

  Future<void> getAllEmployees() async {
    allEmployeeCurrentPage = 1;
    emit(AllEmployeesLoading());

    final result =
        await _departmentRepo.getAllEmployees(page: allEmployeeCurrentPage);

    result.when(
      success: (data) {
        allEmployeeResponse = data;
        allEmployeeCurrentPage = data.data!.meta!.currentPage!;
        allEmployeeLastPage = data.data!.meta!.lastPage!;
        emit(AllEmployeesSuccess());
      },
      failure: (error) {
        emit(AllEmployeesFailure());
      },
    );
  }

  Future<void> loadMoreAllEmployees() async {
    if (isLoadingMoreAllEmployees ||
        allEmployeeCurrentPage >= allEmployeeLastPage) {
      return;
    }

    emit(AllEmployeesLoadingMore());
    isLoadingMoreAllEmployees = true;

    final result =
        await _departmentRepo.getAllEmployees(page: allEmployeeCurrentPage + 1);

    result.when(
      success: (data) {
        allEmployeeResponse!.data!.data!.addAll(data.data!.data!);
        allEmployeeCurrentPage = data.data!.meta!.currentPage!;
        emit(AllEmployeesSuccess());
      },
      failure: (error) {
        emit(AllEmployeesFailure());
      },
    );

    isLoadingMoreAllEmployees = false;
  }

  @override
  Future<void> close() {
    allEmployeeScrollController.dispose();
    return super.close();
  }
}
