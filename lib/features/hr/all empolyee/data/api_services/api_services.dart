import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class AllEmpleyeeApiServices {
  AllEmpleyeeApiServices(this._dioFactory);

  final DioHelper _dioFactory;

 
  Future<Response?> getAllEmployees({
    required int page,
  }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getAllEmployees,
      data: {"page": page},
    );
  }
}
