// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'all_emplyee_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AllEmplyeeModel _$AllEmplyeeModelFromJson(Map<String, dynamic> json) =>
    AllEmplyeeModel(
      data: json['data'] == null
          ? null
          : Data.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String?,
      error: json['error'] as String?,
      code: (json['code'] as num?)?.toInt(),
    );

Map<String, dynamic> _$AllEmplyeeModelToJson(AllEmplyeeModel instance) =>
    <String, dynamic>{
      'data': instance.data?.toJson(),
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

Data _$DataFromJson(Map<String, dynamic> json) => Data(
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Emplyeee.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: json['meta'] == null
          ? null
          : Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DataToJson(Data instance) => <String, dynamic>{
      'data': instance.data?.map((e) => e.toJson()).toList(),
      'meta': instance.meta?.toJson(),
    };

Emplyeee _$EmplyeeeFromJson(Map<String, dynamic> json) => Emplyeee(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] as String?,
      email: json['email'] as String?,
      phone: json['phone'] as String?,
      imag: json['imag'] as String?,
      attachments: (json['attachments'] as List<dynamic>?)
          ?.map((e) => Attachments.fromJson(e as Map<String, dynamic>))
          .toList(),
      branch: json['branch'] == null
          ? null
          : Branch.fromJson(json['branch'] as Map<String, dynamic>),
      department: json['department'] == null
          ? null
          : Department.fromJson(json['department'] as Map<String, dynamic>),
      position: json['position'] == null
          ? null
          : Position.fromJson(json['position'] as Map<String, dynamic>),
      shift: json['shift'] == null
          ? null
          : Shift.fromJson(json['shift'] as Map<String, dynamic>),
      salary: (json['salary'] as num?)?.toInt(),
      status: json['status'] as String?,
      contractType: json['contract_type'] as String?,
      maritalStatus: json['marital_status'] as String?,
      annualLeaveDays: (json['annual_leave_days'] as num?)?.toInt(),
      normalDays: (json['normal_days'] as num?)?.toInt(),
      sickDays: (json['sick_days'] as num?)?.toInt(),
      availableAnnualLeaveDays:
          (json['available_annual_leave_days'] as num?)?.toInt(),
      availableNormalDays: (json['available_normal_days'] as num?)?.toInt(),
      availableSickDays: (json['available_sick_days'] as num?)?.toInt(),
      contractStartDate: json['contract_start_date'] as String?,
      contractEndDate: json['contract_end_date'] as String?,
      dateOfBirth: json['date_of_birth'] as String?,
      nationalityId: (json['nationality_id'] as num?)?.toInt(),
      roles:
          (json['roles'] as List<dynamic>?)?.map((e) => e as String).toList(),
      permissions: (json['permissions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
    );

Map<String, dynamic> _$EmplyeeeToJson(Emplyeee instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'imag': instance.imag,
      'attachments': instance.attachments?.map((e) => e.toJson()).toList(),
      'branch': instance.branch?.toJson(),
      'department': instance.department?.toJson(),
      'position': instance.position?.toJson(),
      'shift': instance.shift?.toJson(),
      'salary': instance.salary,
      'status': instance.status,
      'contract_type': instance.contractType,
      'marital_status': instance.maritalStatus,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'available_annual_leave_days': instance.availableAnnualLeaveDays,
      'available_normal_days': instance.availableNormalDays,
      'available_sick_days': instance.availableSickDays,
      'contract_start_date': instance.contractStartDate,
      'contract_end_date': instance.contractEndDate,
      'date_of_birth': instance.dateOfBirth,
      'nationality_id': instance.nationalityId,
      'roles': instance.roles,
      'permissions': instance.permissions,
    };

Attachments _$AttachmentsFromJson(Map<String, dynamic> json) => Attachments(
      id: (json['id'] as num?)?.toInt(),
      url: json['url'] as String?,
    );

Map<String, dynamic> _$AttachmentsToJson(Attachments instance) =>
    <String, dynamic>{
      'id': instance.id,
      'url': instance.url,
    };

Branch _$BranchFromJson(Map<String, dynamic> json) => Branch(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      country: json['country'] == null
          ? null
          : Country.fromJson(json['country'] as Map<String, dynamic>),
      shift: json['shift'] == null
          ? null
          : Shift.fromJson(json['shift'] as Map<String, dynamic>),
      city: json['city'] == null
          ? null
          : Name.fromJson(json['city'] as Map<String, dynamic>),
      address: json['address'] as String?,
      phone: json['phone'] as String?,
      email: json['email'] as String?,
      annualLeaveDays: (json['annual_leave_days'] as num?)?.toInt(),
      normalDays: (json['normal_days'] as num?)?.toInt(),
      sickDays: (json['sick_days'] as num?)?.toInt(),
      long: json['long'] as String?,
      lat: json['lat'] as String?,
    );

Map<String, dynamic> _$BranchToJson(Branch instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name?.toJson(),
      'country': instance.country?.toJson(),
      'shift': instance.shift?.toJson(),
      'city': instance.city?.toJson(),
      'address': instance.address,
      'phone': instance.phone,
      'email': instance.email,
      'annual_leave_days': instance.annualLeaveDays,
      'normal_days': instance.normalDays,
      'sick_days': instance.sickDays,
      'long': instance.long,
      'lat': instance.lat,
    };

Name _$NameFromJson(Map<String, dynamic> json) => Name(
      en: json['en'] as String?,
      ar: json['ar'] as String?,
    );

Map<String, dynamic> _$NameToJson(Name instance) => <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };

Country _$CountryFromJson(Map<String, dynamic> json) => Country(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      code: json['code'] as String?,
    );

Map<String, dynamic> _$CountryToJson(Country instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name?.toJson(),
      'code': instance.code,
    };

Department _$DepartmentFromJson(Map<String, dynamic> json) => Department(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DepartmentToJson(Department instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name?.toJson(),
    };

Position _$PositionFromJson(Map<String, dynamic> json) => Position(
      id: (json['id'] as num?)?.toInt(),
      name: json['name'] == null
          ? null
          : Name.fromJson(json['name'] as Map<String, dynamic>),
      mangers: json['mangers'] == null
          ? null
          : Position.fromJson(json['mangers'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PositionToJson(Position instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name?.toJson(),
      'mangers': instance.mangers?.toJson(),
    };

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
      currentPage: (json['current_page'] as num?)?.toInt(),
      from: (json['from'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
      path: json['path'] as String?,
      perPage: (json['per_page'] as num?)?.toInt(),
      to: (json['to'] as num?)?.toInt(),
      total: (json['total'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
    };
