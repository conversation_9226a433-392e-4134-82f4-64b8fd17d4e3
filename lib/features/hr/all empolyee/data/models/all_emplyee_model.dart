import 'package:erp/features/profile/data/model/profile_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'all_emplyee_model.g.dart';

@JsonSerializable(explicitToJson: true)
class AllEmplyeeModel {
  Data? data;
  String? status;
  String? error;
  int? code;

  AllEmplyeeModel({this.data, this.status, this.error, this.code});

  factory AllEmplyeeModel.fromJson(Map<String, dynamic> json) =>
      _$AllEmplyeeModelFromJson(json);
  Map<String, dynamic> toJson() => _$AllEmplyeeModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Data {
  List<Emplyeee>? data;
  // Links? links;
  Meta? meta;

  Data({this.data, this.meta});

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Emplyeee {
  int? id;
  String? name;
  String? email;
  String? phone;
  String? imag;
  List<Attachments>? attachments;
  Branch? branch;
  Department? department;
  Position? position;
  Shift? shift;
  int? salary;
  String? status;
  @JsonKey(name: 'contract_type')
  String? contractType;
  @JsonKey(name: 'marital_status')
  String? maritalStatus;
  @JsonKey(name: 'annual_leave_days')
  int? annualLeaveDays;
  @JsonKey(name: 'normal_days')
  int? normalDays;
  @JsonKey(name: 'sick_days')
  int? sickDays;
  @JsonKey(name: 'available_annual_leave_days')
  int? availableAnnualLeaveDays;
  @JsonKey(name: 'available_normal_days')
  int? availableNormalDays;
  @JsonKey(name: 'available_sick_days')
  int? availableSickDays;
  @JsonKey(name: 'contract_start_date')
  String? contractStartDate;
  @JsonKey(name: 'contract_end_date')
  String? contractEndDate;
  @JsonKey(name: 'date_of_birth')
  String? dateOfBirth;
  @JsonKey(name: 'nationality_id')
  int? nationalityId;
  List<String>? roles;
  List<String>? permissions;

  Emplyeee({
    this.id,
    this.name,
    this.email,
    this.phone,
    this.imag,
    this.attachments,
    this.branch,
    this.department,
    this.position,
    this.shift,
    this.salary,
    this.status,
    this.contractType,
    this.maritalStatus,
    this.annualLeaveDays,
    this.normalDays,
    this.sickDays,
    this.availableAnnualLeaveDays,
    this.availableNormalDays,
    this.availableSickDays,
    this.contractStartDate,
    this.contractEndDate,
    this.dateOfBirth,
    this.nationalityId,
    this.roles,
    this.permissions,
  });

  factory Emplyeee.fromJson(Map<String, dynamic> json) =>
      _$EmplyeeeFromJson(json);
  Map<String, dynamic> toJson() => _$EmplyeeeToJson(this);
}

@JsonSerializable()
class Attachments {
  int? id;
  String? url;

  Attachments({this.id, this.url});

  factory Attachments.fromJson(Map<String, dynamic> json) =>
      _$AttachmentsFromJson(json);
  Map<String, dynamic> toJson() => _$AttachmentsToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Branch {
  int? id;
  Name? name;
  Country? country;
  Shift? shift;
  Name? city;
  String? address;
  String? phone;
  String? email;
  @JsonKey(name: 'annual_leave_days')
  int? annualLeaveDays;
  @JsonKey(name: 'normal_days')
  int? normalDays;
  @JsonKey(name: 'sick_days')
  int? sickDays;
  String? long;
  String? lat;

  Branch(
      {this.id,
      this.name,
      this.country,
      this.shift,
      this.city,
      this.address,
      this.phone,
      this.email,
      this.annualLeaveDays,
      this.normalDays,
      this.sickDays,
      this.long,
      this.lat});

  factory Branch.fromJson(Map<String, dynamic> json) => _$BranchFromJson(json);
  Map<String, dynamic> toJson() => _$BranchToJson(this);
}

@JsonSerializable()
class Name {
  String? en;
  String? ar;

  Name({this.en, this.ar});

  factory Name.fromJson(Map<String, dynamic> json) => _$NameFromJson(json);
  Map<String, dynamic> toJson() => _$NameToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Country {
  int? id;
  Name? name;
  String? code;

  Country({this.id, this.name, this.code});

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);
  Map<String, dynamic> toJson() => _$CountryToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Department {
  int? id;
  Name? name;

  Department({this.id, this.name});

  factory Department.fromJson(Map<String, dynamic> json) =>
      _$DepartmentFromJson(json);
  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Position {
  int? id;
  Name? name;
  Position? mangers;

  Position({this.id, this.name, this.mangers});

  factory Position.fromJson(Map<String, dynamic> json) =>
      _$PositionFromJson(json);
  Map<String, dynamic> toJson() => _$PositionToJson(this);
}


@JsonSerializable()
@JsonSerializable(explicitToJson: true)
class Meta {
  @JsonKey(name: 'current_page')
  int? currentPage;
  int? from;
  @JsonKey(name: 'last_page')
  int? lastPage;
  // List<Links>? links;
  String? path;
  @JsonKey(name: 'per_page')
  int? perPage;
  int? to;
  int? total;

  Meta(
      {this.currentPage,
      this.from,
      this.lastPage,
      this.path,
      this.perPage,
      this.to,
      this.total});

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);
  Map<String, dynamic> toJson() => _$MetaToJson(this);
}
