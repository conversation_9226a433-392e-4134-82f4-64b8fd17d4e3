import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/hr/all%20empolyee/data/api_services/api_services.dart';
import 'package:erp/features/hr/all%20empolyee/data/models/all_emplyee_model.dart';

class AllEmplyeeRepo {
  final AllEmpleyeeApiServices departmentApiServices;

  AllEmplyeeRepo(this.departmentApiServices);

  Future<ApiResult<AllEmplyeeModel>> getAllEmployees({
    required int page,
  }) async {
    try {
      final response = await departmentApiServices.getAllEmployees(
        page: page,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        final model = AllEmplyeeModel.fromJson(response!.data);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response?.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
