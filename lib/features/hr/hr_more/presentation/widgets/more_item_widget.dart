import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MoreItemWIdget extends StatelessWidget {
  final String image;
  final String label;
  final bool isLastItem;
  final String? subTitle;
  final bool isRedColor;
  final bool isDarkMode;
  final VoidCallback onPressed;
  final bool? value;
  final Function(bool)? onChange;
  const MoreItemWIdget({
    super.key,
    required this.image,
    required this.label,
    this.subTitle,
    this.isDarkMode = false,
    required this.onPressed,
    this.isLastItem = false,
    this.isRedColor = false,
    this.value,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        InkWell(
          onTap: onPressed,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 14.sp, horizontal: 13.sp),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    color: isRedColor
                        ? AppColors.redColor100.withValues(alpha: 0.1)
                        : AppColors.primaryColor800.withValues(alpha: 0.1),
                  ),
                  child: SvgPicture.asset(image, fit: BoxFit.scaleDown)),
              SizedBox(
                width: 12.w,
              ),
              Text(
                label,
                style: Styles.contentEmphasis.copyWith(
                  fontWeight: FontWeight.w500,
                  fontSize: 16.sp,
                ),
              ),
              const Spacer(),
              if (subTitle != null)
                Text(
                  subTitle!,
                  style: Styles.contentEmphasis.copyWith(
                      color: AppColors.neutralColor600,
                      fontWeight: FontWeight.w500,
                      fontSize: 14.sp),
                ),
              8.horizontalSpace,
              isDarkMode
                  ? SizedBox(
                      width: 36.sp,
                      height: 18.sp,
                      child: Transform.scale(
                        scale: 0.8,
                        child: Switch(
                          value: value!,
                          onChanged: onChange,
                          trackOutlineColor:
                              WidgetStateProperty.resolveWith<Color?>((states) {
                            if (states.contains(WidgetState.selected)) {
                              return AppColors.primaryColor800;
                            }
                            return AppColors.neutralColor600;
                          }),
                          inactiveThumbColor: AppColors.primaryColor800,
                          inactiveTrackColor: AppColors.scaffoldBackground,
                        ),
                      ),
                    )
                  : Icon(
                      Icons.keyboard_arrow_right_outlined,
                      color: isRedColor
                          ? AppColors.redColor100
                          : AppColors.primaryColor800,
                    )
            ],
          ),
        ),
        if (!isLastItem) ...[
          16.verticalSpace,
          Container(
            height: 1.sp,
            color: AppColors.dividerColor,
          ),
          16.verticalSpace,
        ],
      ],
    );
  }
}
