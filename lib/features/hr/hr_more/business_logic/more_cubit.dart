// import 'dart:async';

// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';

// part 'more_state.dart';

// class MoreCubit extends Cubit<MoreState> {
//   MoreCubit() : super(MoreInitial());

//   final TextEditingController confirmNewPassword = TextEditingController();
//   final TextEditingController oldPassword = TextEditingController();
//   final TextEditingController newPassword = TextEditingController();


//   bool isObscure = true;
//   bool isObscure1 = true;
//   bool isObscure2 = true;

//   final formKey = GlobalKey<FormState>();

//   /// Toggle Password
//   void toggleObscure() {
//     isObscure = !isObscure;
//     emit(TogglePasswordState());
//   }
//   /// Toggle Password
//   void toggleObscure1() {
//     isObscure1 = !isObscure1;
//     emit(TogglePasswordState());
//   }

//   /// Toggle Password
//   void toggleObscure2() {
//     isObscure2 = !isObscure2;
//     emit(TogglePasswordState());
//   }

//   bool isConfirm = true;

//   void changeToggleFunction() {
//     isConfirm = !isConfirm;
//     emit(IsConfirmGoTo());
//   }

//   @override
//   Future<void> close() {
//     confirmNewPassword.dispose();
//     return super.close();
//   }
// }
