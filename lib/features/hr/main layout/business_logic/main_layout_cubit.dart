import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/features/hr/main%20layout/business_logic/main_layout_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MainLayoutHrCubit extends Cubit<MainLayoutHrState> {
  MainLayoutHrCubit() : super(MainLayoutHrInitial());

  static MainLayoutHrCubit get(context) => BlocProvider.of(context);
  void changeBottomNavBar(index) {
    AppConstants.mainLayoutInitialScreenIndex = index;
    emit(AppBottomHrNavState(AppConstants.mainLayoutInitialScreenIndex));
  }
}
