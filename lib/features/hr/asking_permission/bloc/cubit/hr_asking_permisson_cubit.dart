import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_state.dart';
import 'package:erp/features/hr/asking_permission/data/model/permission_request.dart';
import 'package:erp/features/hr/asking_permission/data/repo/hr_permison_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class HRAskingPermissonCubit extends Cubit<HrAskingPermsionState> {
  HRAskingPermissonCubit(this._repo) : super(AskingPermsionInitial());

  final HrPermisonRepo _repo;

  PermissionRequestResponse? permissionRequestResponse;
  List<PermissionRequest> permissions = [];

  final ScrollController scrollController = ScrollController();
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final TextEditingController reasonController = TextEditingController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMorePermissionRequests();
      }
    });
  }

  Future<void> getPermissionRequests() async {
    emit(GetPermissonLoadingState());

    final result =
        await _repo.hrGetPermissonRequests(page: currentPage); // Pass page
    result.when(
      success: (data) {
        permissionRequestResponse = data;
        permissions = data.data.data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetPermissonSuccessState());
      },
      failure: (error) {
        emit(GetPermissonErrorState());
      },
    );
  }

  Future<void> loadMorePermissionRequests() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    emit(GetPermissonLoadingMoreState());
    isLoadingMore = true;
    final nextPage = currentPage + 1;

    final result = await _repo.hrGetPermissonRequests(page: nextPage);
    result.when(
      success: (data) {
        permissions.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        emit(GetPermissonSuccessState());
      },
      failure: (error) {
        emit(GetPermissonErrorState());
      },
    );

    isLoadingMore = false;
  }

  Future<void> updatePermissionStatus({
    required String status,
    required int id,
  }) async {
    emit(UpdatePermissonLoadingState());
    showLoading();
    final result = await _repo.updateAskingPermsionRequesteRequest(
      status: status,
      reason: reasonController.text,
      id: id,
    );

    result.when(
      success: (data) {
        final index =
            permissions.indexWhere((element) => element.id == data.id);
        if (index != -1) {
          permissions[index] = data;
        }
        reasonController.clear();
        hideLoading();
        customToast(msg: "success", color: AppColors.greenColor100);

        emit(UpdatePermissonSuccessState());
      },
      failure: (error) {
        hideLoading();

        emit(UpdatePermissonErrorState());
      },
    );
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    reasonController.dispose();
    return super.close();
  }
}
