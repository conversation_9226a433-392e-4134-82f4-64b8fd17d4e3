import 'package:json_annotation/json_annotation.dart';

part 'permission_request.g.dart';

@JsonSerializable()
class PermissionRequestResponse {
  final String status;
  final String error;
  final int code;
  final PermissionRequestData data;

  PermissionRequestResponse({
    required this.status,
    required this.error,
    required this.code,
    required this.data,
  });

  factory PermissionRequestResponse.fromJson(Map<String, dynamic> json) =>
      _$PermissionRequestResponseFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRequestResponseToJson(this);
}

@JsonSerializable()
class PermissionRequestData {
  final List<PermissionRequest> data;
  final PermissionLinks links;
  final PermissionMeta meta;

  PermissionRequestData({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory PermissionRequestData.fromJson(Map<String, dynamic> json) =>
      _$PermissionRequestDataFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRequestDataToJson(this);
}

@JsonSerializable()
class PermissionRequest {
  @JsonKey(name: 'time_from')
  final String timeFrom;

  @JsonKey(name: 'time_to')
  final String timeTo;

  final String date;
  final String reason;
  final String? attachments;
  final String status;

  @Json<PERSON>ey(name: 'reject_reason')
  final String? rejectReason;

  final String? manager;
  final String employee;

  final int? id;
  final int? employeeId;
  final String email;
  PermissionRequest({
    required this.id,
    required this.timeFrom,
    required this.timeTo,
    required this.date,
    required this.reason,
    this.attachments,
    required this.status,
    this.rejectReason,
    this.manager,
    required this.employee,
    required this.employeeId,
    required this.email,
  });

  factory PermissionRequest.fromJson(Map<String, dynamic> json) =>
      _$PermissionRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRequestToJson(this);
}

@JsonSerializable()
class PermissionLinks {
  final String? first;
  final String? last;
  final String? prev;
  final String? next;

  PermissionLinks({
    this.first,
    this.last,
    this.prev,
    this.next,
  });

  factory PermissionLinks.fromJson(Map<String, dynamic> json) =>
      _$PermissionLinksFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionLinksToJson(this);
}

@JsonSerializable()
class PermissionMeta {
  @JsonKey(name: 'current_page')
  final int currentPage;

  final int from;

  @JsonKey(name: 'last_page')
  final int lastPage;

  final List<PermissionMetaLink> links;
  final String path;

  @JsonKey(name: 'per_page')
  final int perPage;

  final int to;
  final int total;

  PermissionMeta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory PermissionMeta.fromJson(Map<String, dynamic> json) =>
      _$PermissionMetaFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionMetaToJson(this);
}

@JsonSerializable()
class PermissionMetaLink {
  final String? url;
  final String label;
  final bool active;

  PermissionMetaLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory PermissionMetaLink.fromJson(Map<String, dynamic> json) =>
      _$PermissionMetaLinkFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionMetaLinkToJson(this);
}
