// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_request.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PermissionRequestResponse _$PermissionRequestResponseFromJson(
        Map<String, dynamic> json) =>
    PermissionRequestResponse(
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
      data:
          PermissionRequestData.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PermissionRequestResponseToJson(
        PermissionRequestResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
      'data': instance.data,
    };

PermissionRequestData _$PermissionRequestDataFromJson(
        Map<String, dynamic> json) =>
    PermissionRequestData(
      data: (json['data'] as List<dynamic>)
          .map((e) => PermissionRequest.fromJson(e as Map<String, dynamic>))
          .toList(),
      links: PermissionLinks.fromJson(json['links'] as Map<String, dynamic>),
      meta: PermissionMeta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PermissionRequestDataToJson(
        PermissionRequestData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'links': instance.links,
      'meta': instance.meta,
    };

PermissionRequest _$PermissionRequestFromJson(Map<String, dynamic> json) =>
    PermissionRequest(
      id: (json['id'] as num?)?.toInt(),
      timeFrom: json['time_from'] as String,
      timeTo: json['time_to'] as String,
      date: json['date'] as String,
      reason: json['reason'] as String,
      attachments: json['attachments'] as String?,
      status: json['status'] as String,
      rejectReason: json['reject_reason'] as String?,
      manager: json['manager'] as String?,
      employee: json['employee'] as String,
      employeeId: (json['employeeId'] as num?)?.toInt(),
      email: json['email'] as String,
    );

Map<String, dynamic> _$PermissionRequestToJson(PermissionRequest instance) =>
    <String, dynamic>{
      'time_from': instance.timeFrom,
      'time_to': instance.timeTo,
      'date': instance.date,
      'reason': instance.reason,
      'attachments': instance.attachments,
      'status': instance.status,
      'reject_reason': instance.rejectReason,
      'manager': instance.manager,
      'employee': instance.employee,
      'id': instance.id,
      'employeeId': instance.employeeId,
      'email': instance.email,
    };

PermissionLinks _$PermissionLinksFromJson(Map<String, dynamic> json) =>
    PermissionLinks(
      first: json['first'] as String?,
      last: json['last'] as String?,
      prev: json['prev'] as String?,
      next: json['next'] as String?,
    );

Map<String, dynamic> _$PermissionLinksToJson(PermissionLinks instance) =>
    <String, dynamic>{
      'first': instance.first,
      'last': instance.last,
      'prev': instance.prev,
      'next': instance.next,
    };

PermissionMeta _$PermissionMetaFromJson(Map<String, dynamic> json) =>
    PermissionMeta(
      currentPage: (json['current_page'] as num).toInt(),
      from: (json['from'] as num).toInt(),
      lastPage: (json['last_page'] as num).toInt(),
      links: (json['links'] as List<dynamic>)
          .map((e) => PermissionMetaLink.fromJson(e as Map<String, dynamic>))
          .toList(),
      path: json['path'] as String,
      perPage: (json['per_page'] as num).toInt(),
      to: (json['to'] as num).toInt(),
      total: (json['total'] as num).toInt(),
    );

Map<String, dynamic> _$PermissionMetaToJson(PermissionMeta instance) =>
    <String, dynamic>{
      'current_page': instance.currentPage,
      'from': instance.from,
      'last_page': instance.lastPage,
      'links': instance.links,
      'path': instance.path,
      'per_page': instance.perPage,
      'to': instance.to,
      'total': instance.total,
    };

PermissionMetaLink _$PermissionMetaLinkFromJson(Map<String, dynamic> json) =>
    PermissionMetaLink(
      url: json['url'] as String?,
      label: json['label'] as String,
      active: json['active'] as bool,
    );

Map<String, dynamic> _$PermissionMetaLinkToJson(PermissionMetaLink instance) =>
    <String, dynamic>{
      'url': instance.url,
      'label': instance.label,
      'active': instance.active,
    };
