import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class HRAskingPermsionApiServices {
  HRAskingPermsionApiServices(this._dioFactory);

  final DioHelper _dioFactory;
  Future<Response?> hrGetPermissonRequests({required int page}) async {
    return _dioFactory.get(
      endPoint: EndPoints.hrGetPermissonRequests,
      data: {
        "page": page,
      },
    );
  }

  Future<Response?> updateStatus({
    required String status,
    required int id,
    String? reason,
  }) async {
    return _dioFactory.post(
      endPoint: EndPoints.hrUpdateStateus(id),
      data: {"status": status, "reject_reason": reason},
    );
  }
}
