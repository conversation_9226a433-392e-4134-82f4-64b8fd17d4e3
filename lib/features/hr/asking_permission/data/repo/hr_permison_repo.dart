import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/hr/asking_permission/data/api_services/hr_api_services.dart';
import 'package:erp/features/hr/asking_permission/data/model/permission_request.dart';

class HrPermisonRepo {
  final HRAskingPermsionApiServices _leaveApiServices;
  HrPermisonRepo(this._leaveApiServices);

  Future<ApiResult<PermissionRequestResponse>> hrGetPermissonRequests(
      {required int page}) async {
    final response = await _leaveApiServices.hrGetPermissonRequests(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        PermissionRequestResponse model =
            PermissionRequestResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }

  Future<ApiResult<PermissionRequest>> updateAskingPermsionRequesteRequest({
    required String status,
    String? reason,
    required int id,
  }) async {
    try {
      final response = await _leaveApiServices.updateStatus(
        id: id,
        status: status,
        reason: reason,
      );

      if (response!.statusCode == 200 || response.statusCode == 201) {
        PermissionRequest model =
            PermissionRequest.fromJson(response.data["data"]);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      return ApiResult.failure(handleDioException(e));
    } catch (e) {
      return ApiResult.failure(
          FailureException(errMessage: 'Unexpected error occurred'));
    }
  }
}
