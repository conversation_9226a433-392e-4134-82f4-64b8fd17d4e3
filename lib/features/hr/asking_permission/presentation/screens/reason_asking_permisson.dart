import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_cubit.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_state.dart';
import 'package:erp/features/hr/asking_permission/data/model/permission_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ReasonAskingPermissonHrScreen extends StatelessWidget {
  const ReasonAskingPermissonHrScreen({super.key, required this.model});
  final PermissionRequest model;

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HRAskingPermissonCubit>();
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'askingPermission.rejectReason'.tr(),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 18.0.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            18.verticalSpace,
            Text(
              'askingPermission.reson'.tr(),
              style: Styles.contentEmphasis,
            ),
            8.verticalSpace,
            CustomTextFormFieldWidget(
              height: 50,
              controller: cubit.reasonController,
              textAlignVertical: TextAlignVertical.top,
              hintText: 'askingPermission.enterTheReason'.tr(),
              keyboardType: TextInputType.text,
              validator: (e) {
                return null;
              },
              borderColor: AppColors.neutralColor600,
              borderRadius: AppConstants.borderRadius - 2,
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.all(20.sp),
        child: BlocListener<HRAskingPermissonCubit, HrAskingPermsionState>(
          listenWhen: (previous, current) =>
              current is UpdatePermissonErrorState ||
              current is UpdatePermissonLoadingState ||
              current is UpdatePermissonSuccessState,
          listener: (context, state) {
            if (state is UpdatePermissonSuccessState) {
              context.pop();
              // context.pop();
              cubit.emit(GetPermissonSuccessState());
            }
          },
          child: CustomButtonWidget(
            text: 'askingPermission.Send'.tr(),
            width: double.infinity,
            elevation: 0,
            onPressed: () {
              if (cubit.reasonController.text.isEmpty) {
                customToast(
                    msg: 'askingPermission.pleaseenterthereason'.tr(),
                    color: AppColors.redColor200);
              } else {
                cubit.updatePermissionStatus(status: "rejected", id: model.id!);
              }
            },
          ),
        ),
      ),
    );
  }
}
