import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/asking%20permission/presentation/widgets/rejected_and_accetp_widget.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_cubit.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_state.dart';
import 'package:erp/features/hr/asking_permission/data/model/permission_request.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HrAskingPermsionDetails extends StatelessWidget {
  const HrAskingPermsionDetails({super.key, required this.model});
  final PermissionRequest model;

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HRAskingPermissonCubit>();

    return Scaffold(
        appBar: AppBar(
          title: Text(model.employee),
          centerTitle: false,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: 12.sp,
            vertical: 18.sp,
          ),
          child: AskingPermissonWidget(
            projectInfo: [
              {
                "title": 'absence.requestedOn'.tr(),
                "value": model.date,
              },
              {"title": 'absence.timefrom'.tr(), "value": model.timeFrom},
              {"title": 'absence.timeon'.tr(), "value": model.timeTo},
            ],
            isCompleted: false,
            reason: model.reason,
            status: model.status,
            rejectonReason: model.rejectReason,
          ),
        ),
        bottomNavigationBar: model.status != "pending"
            ? SizedBox.shrink()
            : Padding(
                padding: EdgeInsets.all(20.0.sp),
                child: Row(
                  spacing: 20.sp,
                  children: [
                    Expanded(
                      child: CustomButtonWidget(
                        text: 'moreSales.reject'.tr(),
                        textColor: AppColors.primaryColor800,
                        color: Colors.white,
                        borderColor:
                            BorderSide(color: AppColors.primaryColor800),
                        onPressed: () {
                          // context.pushNamed(Routes.sendAskingPermission);
                          context.pushNamed(Routes.hrReasonsendAskingPermission,
                              arguments: HrAskingPermissionArguments(
                                  cubit: cubit, model: model));
                        },
                      ),
                    ),
                    Expanded(
                      child: BlocListener<HRAskingPermissonCubit,
                          HrAskingPermsionState>(
                        listenWhen: (previous, current) =>
                            current is UpdatePermissonErrorState ||
                            current is UpdatePermissonLoadingState ||
                            current is UpdatePermissonSuccessState,
                        listener: (context, state) {
                          if (state is UpdatePermissonSuccessState) {
                            context.pop();
                            cubit.emit(GetPermissonSuccessState());
                          }
                        },
                        child: CustomButtonWidget(
                          text: 'moreSales.approve'.tr(),
                          onPressed: () {
                            cubit.updatePermissionStatus(
                                status: "approved", id: model.id!);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ));
  }
}
