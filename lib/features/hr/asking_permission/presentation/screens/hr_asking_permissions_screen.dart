import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_divider_widget.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_cubit.dart';
import 'package:erp/features/hr/asking_permission/bloc/cubit/hr_asking_permisson_state.dart';
import 'package:erp/features/hr/asking_permission/presentation/widget/asking_permission_skeltonizer.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class HrAskingPermissionsScreen extends StatelessWidget {
  const HrAskingPermissionsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<HRAskingPermissonCubit>();
    return BlocBuilder<HRAskingPermissonCubit, HrAskingPermsionState>(
      buildWhen: (previous, current) =>
          current is GetPermissonErrorState ||
          current is GetPermissonLoadingState ||
          current is GetPermissonSuccessState,
      builder: (context, state) {
        if (state is GetPermissonLoadingState) {
          return HrAskingPermissionsScreenSkelonizer();
        }
        return Scaffold(
          appBar: AppBar(
            title: Text('askingPermission.askingpermission'.tr()),
            // centerTitle: false,

            leading: SvgPicture.asset(
              'assets/images/svgs/notification_icon.svg',
              fit: BoxFit.scaleDown,
            ),
          ),
          body: Column(
            children: [
              Expanded(
                child: ListView.separated(
                  controller: cubit.scrollController,
                  padding:
                      EdgeInsets.symmetric(vertical: 15.sp, horizontal: 16.sp),
                  itemCount: cubit.permissionRequestResponse!.data.data.length,
                  separatorBuilder: (context, index) => Column(
                    children: [
                      16.verticalSpace,
                      const CustomDividerWidget(),
                      16.verticalSpace,
                    ],
                  ),
                  itemBuilder: (context, index) {
                    final item =
                        cubit.permissionRequestResponse!.data.data[index];
                    return GestureDetector(
                      onTap: () {
                        context.pushNamed(Routes.hrAskingPermsionDetails,
                            arguments: HrAskingPermissionArguments(
                                cubit: cubit, model: item));
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(item.employee,
                                    style: Styles.highlightEmphasis),
                                Text(
                                  item.email,
                                  style: Styles.captionEmphasis.copyWith(
                                    color: AppColors.neutralColor600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 3.h),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4.r),
                                color: item.status == "approved"
                                    ? AppColors.greenColor200
                                        .withValues(alpha: 0.1)
                                    : item.status == "rejected"
                                        ? AppColors.redColor200
                                            .withValues(alpha: 0.1)
                                        : AppColors.yellowColor200
                                            .withValues(alpha: 0.1)),
                            // color: Color(0xFFFFF7E6)),
                            child: Text(
                              item.status,
                              style: Styles.captionRegular.copyWith(
                                color: item.status == "approved"
                                    ? AppColors.greenColor200
                                    : item.status == "rejected"
                                        ? AppColors.redColor200
                                        : AppColors.yellowColor200,
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Icon(Icons.arrow_forward_ios, size: 14.sp),
                        ],
                      ),
                    );
                  },
                ),
              ),
              BlocBuilder<HRAskingPermissonCubit, HrAskingPermsionState>(
                buildWhen: (previous, current) =>
                    current is GetPermissonLoadingMoreState ||
                    current is GetPermissonLoadingState ||
                    current is GetPermissonSuccessState,
                builder: (context, state) {
                  if (state is GetPermissonLoadingMoreState) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else {
                    return SizedBox.shrink();
                  }
                },
              )
            ],
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.symmetric(vertical: 15.sp, horizontal: 16.sp),
            child: CustomButtonWidget(
              onPressed: () {
                context.pushNamed(Routes.askingPermissionScreen);
              },
              color: AppColors.primaryColor800,
              borderRadius: 8.r,
              padding: EdgeInsets.symmetric(vertical: 12.h),
              text: 'askingPermission.sendanAskingpermissionrequest'.tr(),
              haveTextStyle: true,
              textStyle: Styles.contentEmphasis.copyWith(
                color: AppColors.neutralColor100,
              ),
              textColor: AppColors.neutralColor100,
            ),
          ),
        );
      },
    );
  }
}
