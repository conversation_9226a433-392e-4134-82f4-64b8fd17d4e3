import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_divider_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HrAskingPermissionsScreenSkelonizer extends StatelessWidget {
  const HrAskingPermissionsScreenSkelonizer({super.key});

  final List<Map<String, dynamic>> permissions = const [
    {
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'status': 'Approved',
      'statusColor': AppColors.greenColor200,
      'statusBg': Color(0xFFE6F4EA),
    },
    {
      'name': '<PERSON>',
      'email': '<EMAIL>',
      'status': 'Rejected',
      'statusColor': Colors.redAccent,
      'statusBg': Color(0xFFFDECEC),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.orange,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.amber,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Rejected',
      'statusColor': Colors.redAccent,
      'statusBg': Color(0xFFFDECEC),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.orange,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.amber,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Rejected',
      'statusColor': Colors.redAccent,
      'statusBg': Color(0xFFFDECEC),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.orange,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.amber,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Rejected',
      'statusColor': Colors.redAccent,
      'statusBg': Color(0xFFFDECEC),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.orange,
      'statusBg': Color(0xFFFFF7E6),
    },
    {
      'name': 'Ahmed',
      'email': '<EMAIL>',
      'status': 'Pending',
      'statusColor': Colors.amber,
      'statusBg': Color(0xFFFFF7E6),
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text("Asking permission"),
          // centerTitle: false,

          leading: SvgPicture.asset(
            'assets/images/svgs/notification_icon.svg',
            fit: BoxFit.scaleDown,
          ),
        ),
        body: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.sp, horizontal: 16.sp),
          child: Column(
            children: [
              Expanded(
                child: ListView.separated(
                  itemCount: permissions.length,
                  separatorBuilder: (context, index) => Column(
                    children: [
                      16.verticalSpace,
                      const CustomDividerWidget(),
                      16.verticalSpace,
                    ],
                  ),
                  itemBuilder: (context, index) {
                    final item = permissions[index];
                    return GestureDetector(
                      onTap: () {
                        context.pushNamed(Routes.hrAskingPermsionDetails);
                      },
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Image.asset(
                          //   'assets/images/pngs/edit_profile_image.png',
                          //   height: 50.h,
                          // ),
                          SizedBox(width: 12.w),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(item['name'],
                                    style: Styles.highlightEmphasis),
                                Text(
                                  item['email'],
                                  style: Styles.captionEmphasis.copyWith(
                                    color: AppColors.neutralColor600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.w, vertical: 3.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.r),
                              color: item['statusBg'],
                            ),
                            child: Text(
                              item['status'],
                              style: Styles.captionRegular.copyWith(
                                color: item['statusColor'],
                              ),
                            ),
                          ),
                          SizedBox(width: 8.w),
                          Icon(Icons.arrow_forward_ios, size: 14.sp),
                        ],
                      ),
                    );
                  },
                ),
              ),
              CustomButtonWidget(
                color: AppColors.primaryColor800,
                borderRadius: 8.r,
                padding: EdgeInsets.symmetric(vertical: 12.h),
                text: 'Send an Asking permission request',
                haveTextStyle: true,
                textStyle: Styles.contentEmphasis.copyWith(
                  color: AppColors.neutralColor100,
                ),
                textColor: AppColors.neutralColor100,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
