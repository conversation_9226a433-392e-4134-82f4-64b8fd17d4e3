import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class DepartmentApiServices {
  DepartmentApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  /// Get  all departments
  Future<Response?> getAllDepartments({required int page}) async {
    return _dioFactory
        .get(endPoint: EndPoints.getAllDepartments, data: {"page": page});
  }

  Future<Response?> getEmployeesForDepartment({
    required int departmentId,
    required int page,
  }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getDepartmentEmplyee(departmentId),
      data: {"page": page},
    );
  }

  Future<Response?> getAllEmployees({
    required int page,
  }) async {
    return _dioFactory.get(
      endPoint: EndPoints.getAllEmployees,
      data: {"page": page},
    );
  }
}
