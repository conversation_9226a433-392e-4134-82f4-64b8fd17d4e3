// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'department_employee_response.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DepartmentEmployeeResponse _$DepartmentEmployeeResponseFromJson(
        Map<String, dynamic> json) =>
    DepartmentEmployeeResponse(
      data: DataWrapper.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$DepartmentEmployeeResponseToJson(
        DepartmentEmployeeResponse instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

DataWrapper _$DataWrapperFromJson(Map<String, dynamic> json) => DataWrapper(
      data: (json['data'] as List<dynamic>)
          .map((e) => Employee.fromJson(e as Map<String, dynamic>))
          .toList(),
      meta: Meta.fromJson(json['meta'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$DataWrapperToJson(DataWrapper instance) =>
    <String, dynamic>{
      'data': instance.data,
      'meta': instance.meta,
    };

ShiftTitle _$ShiftTitleFromJson(Map<String, dynamic> json) => ShiftTitle(
      en: json['en'] as String,
      ar: json['ar'] as String,
    );

Map<String, dynamic> _$ShiftTitleToJson(ShiftTitle instance) =>
    <String, dynamic>{
      'en': instance.en,
      'ar': instance.ar,
    };

Employee _$EmployeeFromJson(Map<String, dynamic> json) => Employee(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      imag: json['imag'] as String,
      position: json['position'] as String,
      shift: json['shift'] == null
          ? null
          : Shift.fromJson(json['shift'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$EmployeeToJson(Employee instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'imag': instance.imag,
      'position': instance.position,
      'shift': instance.shift,
    };

Meta _$MetaFromJson(Map<String, dynamic> json) => Meta(
      currentPage: (json['current_page'] as num?)?.toInt(),
      lastPage: (json['last_page'] as num?)?.toInt(),
    );

Map<String, dynamic> _$MetaToJson(Meta instance) => <String, dynamic>{
      'current_page': instance.currentPage,
      'last_page': instance.lastPage,
    };
