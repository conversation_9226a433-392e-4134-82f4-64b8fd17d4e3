import 'package:erp/features/emplyee%20flow/company%20profile/data/model/compauny_profile_model.dart';
import 'package:erp/features/profile/data/model/profile_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'department_response.g.dart';

@JsonSerializable(explicitToJson: true)
class DepartmentResponse {
  final DataWrapper data;
  final String status;
  final String error;
  final int code;

  DepartmentResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory DepartmentResponse.fromJson(Map<String, dynamic> json) =>
      _$DepartmentResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentResponseToJson(this);
}

@JsonSerializable(explicitToJson: true)
class DataWrapper {
  final List<DepartmentModel> data;
  final Links links;
  final Meta meta;

  DataWrapper({
    required this.data,
    required this.links,
    required this.meta,
  });

  factory DataWrapper.fromJson(Map<String, dynamic> json) =>
      _$DataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$DataWrapperToJson(this);
}

@JsonSerializable(explicitToJson: true)
class DepartmentModel {
  final int id;
  final LocalizedText name;
  final List<EmployeeModel> employees;

  DepartmentModel({
    required this.id,
    required this.name,
    required this.employees,
  });

  factory DepartmentModel.fromJson(Map<String, dynamic> json) =>
      _$DepartmentModelFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentModelToJson(this);
}

@JsonSerializable(explicitToJson: true)
class EmployeeModel {
  final int id;
  final String name;
  final String email;
  final String imag;
  final String position;
  final Shift? shift;

  EmployeeModel({
    required this.id,
    required this.name,
    required this.email,
    required this.imag,
    required this.position,
    this.shift,
  });

  factory EmployeeModel.fromJson(Map<String, dynamic> json) =>
      _$EmployeeModelFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeModelToJson(this);
}

@JsonSerializable()
class Links {
  final String first;
  final String last;
  final String? prev;
  final String? next;

  Links({
    required this.first,
    required this.last,
    this.prev,
    this.next,
  });

  factory Links.fromJson(Map<String, dynamic> json) => _$LinksFromJson(json);
  Map<String, dynamic> toJson() => _$LinksToJson(this);
}

@JsonSerializable(explicitToJson: true)
class Meta {
  @JsonKey(name: 'current_page')
  final int currentPage;
  final int from;
  @JsonKey(name: 'last_page')
  final int lastPage;
  final List<PageLink> links;
  final String path;
  @JsonKey(name: 'per_page')
  final int perPage;
  final int to;
  final int total;

  Meta({
    required this.currentPage,
    required this.from,
    required this.lastPage,
    required this.links,
    required this.path,
    required this.perPage,
    required this.to,
    required this.total,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);
  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class PageLink {
  final String? url;
  final String label;
  final bool active;

  PageLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory PageLink.fromJson(Map<String, dynamic> json) =>
      _$PageLinkFromJson(json);
  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}
