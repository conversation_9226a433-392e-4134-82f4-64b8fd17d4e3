import 'package:erp/features/profile/data/model/profile_model.dart';
import 'package:json_annotation/json_annotation.dart';


part 'department_employee_response.g.dart';

@JsonSerializable()
class DepartmentEmployeeResponse {
  final DataWrapper data;
  final String status;
  final String error;
  final int code;

  DepartmentEmployeeResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory DepartmentEmployeeResponse.fromJson(Map<String, dynamic> json) =>
      _$DepartmentEmployeeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$DepartmentEmployeeResponseToJson(this);
}

@JsonSerializable()
class DataWrapper {
  final List<Employee> data;
  final Meta meta;

  DataWrapper({
    required this.data,
    required this.meta,
  });

  factory DataWrapper.fromJson(Map<String, dynamic> json) =>
      _$DataWrapperFromJson(json);

  Map<String, dynamic> toJson() => _$DataWrapperToJson(this);
}


@JsonSerializable()
class ShiftTitle {
  final String en;
  final String ar;

  ShiftTitle({
    required this.en,
    required this.ar,
  });

  factory ShiftTitle.fromJson(Map<String, dynamic> json) =>
      _$ShiftTitleFromJson(json);

  Map<String, dynamic> toJson() => _$ShiftTitleToJson(this);
}


@JsonSerializable()
class Employee {
  final int id;
  final String name;
  final String email;
  final String imag;
  final String position;
  final Shift? shift;

  Employee({
    required this.id,
    required this.name,
    required this.email,
    required this.imag,
    required this.position,
    this.shift,
  });

  factory Employee.fromJson(Map<String, dynamic> json) =>
      _$EmployeeFromJson(json);

  Map<String, dynamic> toJson() => _$EmployeeToJson(this);
}


@JsonSerializable()
class Meta {
  @JsonKey(name: 'current_page')
  final int? currentPage;

  @JsonKey(name: 'last_page')
  final int? lastPage;

  Meta({
    this.currentPage,
    this.lastPage,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);

  Map<String, dynamic> toJson() => _$MetaToJson(this);
}
