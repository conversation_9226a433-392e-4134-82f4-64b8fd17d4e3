import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/hr/all%20department/data/api_services/api_services.dart';
import 'package:erp/features/hr/all%20department/data/models/department_employee_response.dart';
import 'package:erp/features/hr/all%20department/data/models/department_response.dart';

class DepartmentRepo {
  final DepartmentApiServices departmentApiServices;

  DepartmentRepo(this.departmentApiServices);

  Future<ApiResult<DepartmentResponse>> getAllDepartments(
      {required int page}) async {
    final response = await departmentApiServices.getAllDepartments(page: page);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        DepartmentResponse model = DepartmentResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<DepartmentEmployeeResponse>> getEmployeesForDepartment({
    required int departmentId,
    required int page,
  }) async {
    try {
      final response = await departmentApiServices.getEmployeesForDepartment(
        departmentId: departmentId,
        page: page,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        final model = DepartmentEmployeeResponse.fromJson(response!.data);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response?.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

}
