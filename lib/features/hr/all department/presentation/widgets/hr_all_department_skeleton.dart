import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/hr/all%20department/presentation/widgets/department_skeleton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class HrAllDepartmentskeleton extends StatelessWidget {
  const HrAllDepartmentskeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        appBar: AppBar(
          title: const Text(
            'All  Departments',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
        
        ),
        body: ListView.separated(
          itemCount: 3,
          padding: EdgeInsets.symmetric(horizontal: 18.sp, vertical: 20.sp),
          separatorBuilder: (BuildContext context, int index) {
            return 20.verticalSpace;
          },
          itemBuilder: (BuildContext context, int index) {
            return Container(
              padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
              decoration: BoxDecoration(
                // color: AppColors.neutralColor100,
                borderRadius: BorderRadius.circular(8.sp),
                border: Border.all(
                  color: AppColors.neutralColor600,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            // 'Department Name',
                            "Sales Department",
                            style: Styles.highlightEmphasis,
                          ),
                          Text(
                            "14 Members",
                            style: Styles.contentEmphasis.copyWith(
                              color: AppColors.neutralColor600,
                            ),
                          ),
                        ],
                      ),
                      // Spacer(),
                      TextButton(
                        onPressed: () {
                          // Handle view all action
                        },
                        child: Text(
                          "View All",
                          style: Styles.contentEmphasis.copyWith(
                            color: AppColors.primaryColor800,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Departmentskeleton()
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
