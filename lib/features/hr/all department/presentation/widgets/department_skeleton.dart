import 'package:cached_network_image/cached_network_image.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class Departmentskeleton extends StatelessWidget {
  const Departmentskeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: 10,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      separatorBuilder: (_, __) => const Divider(
        height: 1,
        color: AppColors.neutralColor600,
      ),
      itemBuilder: (context, index) {
        // final lead = leads[index];
        return Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
          child: Row(
            children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(8.sp),
                  child: CachedNetworkImage(
                    imageUrl: 'https://i.pravatar.cc/150?img=3',
                    height: 50.sp,
                    width: 50.sp,
                    errorWidget: (context, url, error) =>
                        Center(child: const Icon(Icons.error)),
                  )),
              SizedBox(width: 12.sp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "lead.name",
                      style: Styles.highlightEmphasis,
                    ),
                    SizedBox(height: 4.sp),
                    Text(
                      "DFSDFSDFDSFSFSD",
                      style: Styles.contentEmphasis
                          .copyWith(color: AppColors.neutralColor600),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              SizedBox(width: 10.sp),
              const Icon(Icons.chevron_right, color: Colors.grey),
            ],
          ),
        );
      },
    );
  }
}
