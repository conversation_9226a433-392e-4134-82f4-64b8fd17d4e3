import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/hr/all%20department/business_logic/cubit/department_cubit.dart';
import 'package:erp/features/hr/all%20department/presentation/widgets/department_skeleton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class AllDepartmentEmployeesScreen extends StatelessWidget {
  final String departmentName;

  const AllDepartmentEmployeesScreen({super.key, required this.departmentName});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<DepartmentCubit>();

    return Scaffold(
      appBar: AppBar(
        title: Text(
          departmentName,
          style:
              const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
        ),
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: BlocBuilder<DepartmentCubit, DepartmentState>(
        buildWhen: (previous, current) =>
            current is DepartmentEmployeesLoading ||
            current is DepartmentEmployeesSuccess ||
            current is DepartmentEmployeesFailure,
        builder: (context, state) {
          if (state is DepartmentEmployeesLoading) {
            return Skeletonizer(enabled: true, child: Departmentskeleton());
          }

          if (state is DepartmentEmployeesFailure) {
            return Center(child: Text('moreSales.failedtoloademployees'.tr()));
          }

          final employees = cubit.employeeResponse!.data.data;

          return Column(
            children: [
              Expanded(
                child: ListView.separated(
                  controller: cubit.employeeScrollController,
                  itemCount: employees.length,
                  padding:
                      EdgeInsets.symmetric(horizontal: 18.sp, vertical: 20.sp),
                  separatorBuilder: (_, __) => Column(
                    children: [
                      10.verticalSpace,
                      const Divider(
                        height: 1,
                        color: AppColors.neutralColor600,
                      ),
                      10.verticalSpace,
                    ],
                  ),
                  itemBuilder: (context, index) {
                    final employee = employees[index];

                    return Row(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.sp),
                          child: CachedNetworkImage(
                            imageUrl: employee.imag,
                            height: 50.sp,
                            width: 50.sp,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        ),
                        SizedBox(width: 12.sp),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                employee.name,
                                style: Styles.highlightEmphasis,
                              ),
                              SizedBox(height: 4.sp),
                              Text(
                                employee.email,
                                style: Styles.contentEmphasis
                                    .copyWith(color: AppColors.neutralColor600),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                        const Icon(Icons.chevron_right, color: Colors.grey),
                      ],
                    );
                  },
                ),
              ),
              BlocBuilder<DepartmentCubit, DepartmentState>(
                buildWhen: (previous, current) =>
                    current is DepartmentEmployeesSuccess ||
                    current is DepartmentEmployeesLoadingMore,
                builder: (context, state) {
                  if (state is DepartmentEmployeesLoadingMore) {
                    return const Padding(
                      padding: EdgeInsets.all(12.0),
                      child: Center(child: CircularProgressIndicator()),
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ],
          );
        },
      ),
    );
  }
}
