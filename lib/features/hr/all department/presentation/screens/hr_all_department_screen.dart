import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/hr/all%20department/business_logic/cubit/department_cubit.dart';
import 'package:erp/features/hr/all%20department/presentation/widgets/hr_all_department_skeleton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class HrAllDepartmentScreen extends StatelessWidget {
  const HrAllDepartmentScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<DepartmentCubit>();

    return BlocBuilder<DepartmentCubit, DepartmentState>(
      buildWhen: (previous, current) =>
          current is DepartmentLoading ||
          current is DepartmentSuccess ||
          current is DepartmentFailure,
      builder: (context, state) {
        if (state is DepartmentLoading) {
          return HrAllDepartmentskeleton();
        }
        return Scaffold(
          appBar: AppBar(
            title: Text(
              'allDepartmentsHr.alldepartments'.tr(),
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
            ),

          ),
          body: Column(
            children: [
              Expanded(
                child: ListView.separated(
                  controller: cubit.departmentScrollController,
                  itemCount: cubit.departmentResponse!.data.data.length,
                  padding:
                      EdgeInsets.symmetric(horizontal: 18.sp, vertical: 20.sp),
                  separatorBuilder: (BuildContext context, int index) {
                    return 20.verticalSpace;
                  },
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 16.sp, vertical: 12.sp),
                      decoration: BoxDecoration(
                        // color: AppColors.neutralColor100,
                        borderRadius: BorderRadius.circular(8.sp),
                        border: Border.all(
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    cubit.departmentResponse!.data.data[index]
                                        .name.en,
                                    // 'Department Name',

                                    style: Styles.highlightEmphasis,
                                  ),
                                  Text(
                                    "${cubit.departmentResponse!.data.data[index].employees.length} Employees",
                                    style: Styles.contentEmphasis.copyWith(
                                      color: AppColors.neutralColor600,
                                    ),
                                  ),
                                ],
                              ),
                              // Spacer(),
                              TextButton(
                                onPressed: () {
                                  // Handle view all action
                                  context.pushNamed(
                                      Routes.departmentEmplyeesScreen,
                                      arguments: DepartMentAllEmplyee(
                                          id: cubit.departmentResponse!.data
                                              .data[index].id,
                                          name: cubit.departmentResponse!.data
                                              .data[index].name.en));
                                },
                                child: Text(
                                  'allDepartmentsHr.viewAll'.tr(),
                                  style: Styles.contentEmphasis.copyWith(
                                    color: AppColors.primaryColor800,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          ListView.separated(
                            itemCount: cubit.departmentResponse!.data
                                .data[index].employees.length,
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            separatorBuilder: (_, __) => const Divider(
                              height: 1,
                              color: AppColors.neutralColor600,
                            ),
                            itemBuilder: (context, index2) {
                              final department = cubit.departmentResponse!.data
                                  .data[index].employees[index2];
                              return Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.sp, vertical: 12.sp),
                                child: Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8.sp),
                                      child: CachedNetworkImage(
                                        height: 50.sp,
                                        width: 50.sp,
                                        imageUrl: department.imag,
                                        placeholder: (context, url) => Center(
                                          child: CircularProgressIndicator(),
                                        ),
                                        errorWidget: (context, url, error) =>
                                            Center(
                                          child: Icon(Icons.error),
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: 12.sp),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            department.name,
                                            style: Styles.highlightEmphasis,
                                          ),
                                          SizedBox(height: 4.sp),
                                          Text(
                                            department.email,
                                            style: Styles.contentEmphasis
                                                .copyWith(
                                                    color: AppColors
                                                        .neutralColor600),
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                    SizedBox(width: 10.sp),
                                    const Icon(Icons.chevron_right,
                                        color: Colors.grey),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              BlocBuilder<DepartmentCubit, DepartmentState>(
                buildWhen: (previous, current) =>
                    current is DepartmentSuccess ||
                    current is DepartmentLoadingMore,
                builder: (context, state) {
                  if (state is DepartmentLoadingMore) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  } else {
                    return SizedBox.shrink();
                  }
                },
              )
            ],
          ),
        );
      },
    );
  }
}
