import 'package:erp/features/hr/all%20department/data/models/department_employee_response.dart';
import 'package:erp/features/hr/all%20department/data/models/department_response.dart';
import 'package:erp/features/hr/all%20department/data/repos/department_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'department_state.dart';

class DepartmentCubit extends Cubit<DepartmentState> {
  DepartmentCubit(this._departmentRepo) : super(DepartmentInitial());

  final DepartmentRepo _departmentRepo;

  DepartmentResponse? departmentResponse;
  final ScrollController departmentScrollController = ScrollController();
  int departmentCurrentPage = 1;
  int departmentLastPage = 1;
  bool isLoadingMoreDepartments = false;

  void setupDepartmentScrollController() {
    departmentScrollController.addListener(() {
      if (departmentScrollController.position.pixels >=
              departmentScrollController.position.maxScrollExtent - 100 &&
          !isLoadingMoreDepartments) {
        loadMoreDepartments();
      }
    });
  }

  Future<void> getAllDepartments() async {
    emit(DepartmentLoading());

    final result =
        await _departmentRepo.getAllDepartments(page: departmentCurrentPage);

    result.when(
      success: (data) {
        departmentResponse = data;
        departmentCurrentPage = data.data.meta.currentPage;
        departmentLastPage = data.data.meta.lastPage;
        emit(DepartmentSuccess());
      },
      failure: (error) {
        emit(DepartmentFailure());
      },
    );
  }

  Future<void> loadMoreDepartments() async {
    if (isLoadingMoreDepartments ||
        departmentCurrentPage >= departmentLastPage) {
      return;
    }
    emit(DepartmentLoadingMore());
    isLoadingMoreDepartments = true;

    final nextPage = departmentCurrentPage + 1;

    final result = await _departmentRepo.getAllDepartments(page: nextPage);

    result.when(
      success: (data) {
        departmentResponse!.data.data.addAll(data.data.data);
        departmentCurrentPage = data.data.meta.currentPage;
        emit(DepartmentSuccess());
      },
      failure: (error) {
        emit(DepartmentFailure());
      },
    );

    isLoadingMoreDepartments = false;
  }

  DepartmentEmployeeResponse? employeeResponse;
  final ScrollController employeeScrollController = ScrollController();
  int employeeCurrentPage = 1;
  int employeeLastPage = 1;
  bool isLoadingMoreEmployees = false;
  int selectedDepartmentId = 1;

  void setupEmployeeScrollController() {
    employeeScrollController.addListener(() {
      if (employeeScrollController.position.pixels >=
              employeeScrollController.position.maxScrollExtent - 100 &&
          !isLoadingMoreEmployees) {
        loadMoreEmployees();
      }
    });
  }

  Future<void> getEmployeesForDepartment(int departmentId) async {
    selectedDepartmentId = departmentId;
    employeeCurrentPage = 1;
    emit(DepartmentEmployeesLoading());

    final result = await _departmentRepo.getEmployeesForDepartment(
      departmentId: departmentId,
      page: employeeCurrentPage,
    );

    result.when(
      success: (data) {
        employeeResponse = data;
        employeeCurrentPage = data.data.meta.currentPage!;
        employeeLastPage = data.data.meta.lastPage!;
        emit(DepartmentEmployeesSuccess());
      },
      failure: (error) {
        emit(DepartmentEmployeesFailure());
      },
    );
  }

  Future<void> loadMoreEmployees() async {
    if (isLoadingMoreEmployees || employeeCurrentPage >= employeeLastPage) {
      return;
    }
    emit(DepartmentEmployeesLoadingMore());
    isLoadingMoreEmployees = true;

    final result = await _departmentRepo.getEmployeesForDepartment(
      departmentId: selectedDepartmentId,
      page: employeeCurrentPage + 1,
    );

    result.when(
      success: (data) {
        employeeResponse!.data.data.addAll(data.data.data);
        employeeCurrentPage = data.data.meta.currentPage!;
        emit(DepartmentEmployeesSuccess());
      },
      failure: (error) {
        emit(DepartmentEmployeesFailure());
      },
    );

    isLoadingMoreEmployees = false;
  }

// NEW: All Employees (across departments)
}
