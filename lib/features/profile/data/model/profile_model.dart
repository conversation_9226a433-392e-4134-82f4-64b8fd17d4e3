import 'package:json_annotation/json_annotation.dart';

part 'profile_model.g.dart';

@JsonSerializable()
class ProfileResponse {
  final Profile data;

  ProfileResponse({required this.data});

  factory ProfileResponse.fromJson(Map<String, dynamic> json) =>
      _$ProfileResponseFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileResponseToJson(this);
}

@JsonSerializable()
class Profile {
  final int id;
  final String name;
  final String email;
  final String phone;
  final Branch branch;
  final Department department;
  final Position position;
  final Shift? shift;
  final int salary;
  final String status;
  @JsonKey(name: 'contract_type')
  final String contractType;
  @JsonKey(name: 'marital_status')
  final String maritalStatus;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'annual_leave_days')
  final int annualLeaveDays;
  @Json<PERSON>ey(name: 'normal_days')
  final int normalDays;
  @<PERSON>sonKey(name: 'sick_days')
  final int sickDays;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'available_annual_leave_days')
  final int availableAnnualLeaveDays;
  @Json<PERSON>ey(name: 'available_normal_days')
  final int availableNormalDays;
  @Json<PERSON>ey(name: 'available_sick_days')
  final int availableSickDays;
  @Json<PERSON>ey(name: 'contract_start_date')
  final String contractStartDate;
  @JsonKey(name: 'contract_end_date')
  final String contractEndDate;
  @JsonKey(name: 'access_token')
  final String? accessToken;
  final String role;
  final List<String> permissions;

  Profile({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.branch,
    required this.department,
    required this.position,
    required this.shift,
    required this.salary,
    required this.status,
    required this.contractType,
    required this.maritalStatus,
    required this.annualLeaveDays,
    required this.normalDays,
    required this.sickDays,
    required this.availableAnnualLeaveDays,
    required this.availableNormalDays,
    required this.availableSickDays,
    required this.contractStartDate,
    required this.contractEndDate,
    required this.accessToken,
    required this.role,
    required this.permissions,
  });

  factory Profile.fromJson(Map<String, dynamic> json) =>
      _$ProfileFromJson(json);
  Map<String, dynamic> toJson() => _$ProfileToJson(this);
}

@JsonSerializable()
class Branch {
  final int id;
  final Localized name;
  final Country country;
  final Localized city;

  @JsonKey(name: 'annual_leave_days')
  final int annualLeaveDays;

  @JsonKey(name: 'normal_days')
  final int normalDays;

  @JsonKey(name: 'sick_days')
  final int sickDays;

  Branch({
    required this.id,
    required this.name,
    required this.country,
    required this.city,
    required this.annualLeaveDays,
    required this.normalDays,
    required this.sickDays,
  });

  factory Branch.fromJson(Map<String, dynamic> json) => _$BranchFromJson(json);
  Map<String, dynamic> toJson() => _$BranchToJson(this);
}

@JsonSerializable()
class Country {
  final int id;
  final Localized name;
  final String code;

  Country({
    required this.id,
    required this.name,
    required this.code,
  });

  factory Country.fromJson(Map<String, dynamic> json) =>
      _$CountryFromJson(json);
  Map<String, dynamic> toJson() => _$CountryToJson(this);
}

@JsonSerializable()
class Department {
  final int id;
  final Localized name;

  Department({
    required this.id,
    required this.name,
  });

  factory Department.fromJson(Map<String, dynamic> json) =>
      _$DepartmentFromJson(json);
  Map<String, dynamic> toJson() => _$DepartmentToJson(this);
}

@JsonSerializable()
class Position {
  final int id;
  final Localized name;
  final Manager? mangers;

  Position({
    required this.id,
    required this.name,
    this.mangers,
  });

  factory Position.fromJson(Map<String, dynamic> json) =>
      _$PositionFromJson(json);
  Map<String, dynamic> toJson() => _$PositionToJson(this);
}

@JsonSerializable()
class Manager {
  final int id;
  final Localized name;
  final Manager? mangers;

  Manager({
    required this.id,
    required this.name,
    this.mangers,
  });

  factory Manager.fromJson(Map<String, dynamic> json) =>
      _$ManagerFromJson(json);
  Map<String, dynamic> toJson() => _$ManagerToJson(this);
}

@JsonSerializable()
class Shift {
  final int id;

  @TitleConverter()
  final Localized? title;

  @JsonKey(name: 'date_from')
  final String dateFrom;

  @JsonKey(name: 'date_to')
  final String dateTo;

  @JsonKey(name: 'time_from')
  final String timeFrom;

  @JsonKey(name: 'time_to')
  final String timeTo;

  Shift({
    required this.id,
    required this.title,
    required this.dateFrom,
    required this.dateTo,
    required this.timeFrom,
    required this.timeTo,
  });

  factory Shift.fromJson(Map<String, dynamic> json) => _$ShiftFromJson(json);
  Map<String, dynamic> toJson() => _$ShiftToJson(this);
}

class TitleConverter implements JsonConverter<Localized?, dynamic> {
  const TitleConverter();

  @override
  Localized? fromJson(dynamic json) => json is String
      ? Localized(en: json, ar: json)
      : (json is Map<String, dynamic> ? Localized.fromJson(json) : null);

  @override
  dynamic toJson(Localized? object) => object?.toJson();
}

@JsonSerializable()
class Localized {
  final String en;
  final String ar;

  Localized({
    required this.en,
    required this.ar,
  });

  factory Localized.fromJson(Map<String, dynamic> json) =>
      _$LocalizedFromJson(json);
  Map<String, dynamic> toJson() => _$LocalizedToJson(this);
}
