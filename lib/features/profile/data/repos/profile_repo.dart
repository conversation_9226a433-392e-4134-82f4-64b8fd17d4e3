import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/profile/data/api_services/api_services.dart';
import 'package:erp/features/profile/data/model/profile_model.dart';

class ProfileRepo {
  ProfileRepo(this.profileApiServices);
  ProfileApiServices profileApiServices;

  /// update password
  Future<ApiResult<String>> updatePassword({
    required String oldPassword,
    required String newPassword,
    required String newRePassword,
  }) async {
    final response = await profileApiServices.updatePassword(
      oldPassword: oldPassword,
      newPassword: newPassword,
      newRePassword: newRePassword,
    );

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success('Update Success');
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<ProfileResponse>> getProfile() async {
    final response = await profileApiServices.getProfile();

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        return ApiResult.success(ProfileResponse.fromJson(response.data));
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }
}
