import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/core/widgets/custom_text_rich_widget.dart';
import 'package:erp/features/auth/presentation/widgets/password_condition_item_widget.dart';
import 'package:erp/features/auth/presentation/widgets/show_change_password_bottom_sheet_widget.dart';
import 'package:erp/features/profile/bloc/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class ChangePasswordScreen extends StatelessWidget {
  const ChangePasswordScreen({super.key});
  @override
  Widget build(BuildContext context) {
    ProfileCubit profileCubit = BlocProvider.of<ProfileCubit>(context);
    return BlocBuilder<ProfileCubit, ProfileState>(
      buildWhen: (previous, current) =>
          current is UpdatePasswordLoadingState ||
          current is UpdatePasswordSuccessState ||
          current is IsConfirmGoTo ||
          current is UpdatePasswordSuccessState,
      builder: (context, state) {
        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              'moreSales.changepassword'.tr(),
              style: TextStyle(color: AppColors.neutralColor1600),
            ),
          ),
          body: Padding(
            padding: EdgeInsets.symmetric(horizontal: 18.0.sp),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  30.verticalSpace,
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          children: [
                            SvgPicture.asset(
                              Assets.assetsImagesSvgsOldPassword,
                              width: 34.w,
                              height: 34.h,
                            ),
                            8.verticalSpace,
                            Text(
                              'moreSales.oldPassword'.tr(),
                              style: Styles.captionRegular
                                  .copyWith(color: AppColors.neutralColor1600),
                            ),
                          ],
                        ),
                      ),
                      16.horizontalSpace,
                      Expanded(
                        child: Column(
                          children: [
                            SvgPicture.asset(
                              Assets.assetsImagesSvgsNewPassword,
                              width: 34.w,
                              height: 34.h,
                            ),
                            8.verticalSpace,
                            Text(
                              'moreSales.newPassword'.tr(),
                              style: Styles.captionRegular
                                  .copyWith(color: AppColors.neutralColor600),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  12.verticalSpace,
                  if (profileCubit.isConfirm = !true)
                    Form(
                      key: profileCubit.formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SvgPicture.asset(Assets
                              .assetsImagesSvgsContainerChangeNewPassword),
                          24.verticalSpace,
                          Text(
                            'moreSales.newPassword'.tr(),
                            style: Styles.contentEmphasis,
                          ),
                          8.verticalSpace,
                          BlocBuilder<ProfileCubit, ProfileState>(
                            buildWhen: (previous, current) =>
                                current is TogglePasswordState,
                            builder: (context, state) {
                              return CustomTextFormFieldWidget(
                                controller:
                                    context.read<ProfileCubit>().newPassword,
                                hintText: 'moreSales.enterNewPassword'.tr(),
                                keyboardType: TextInputType.visiblePassword,
                                // textInputAction: TextInputAction.done,
                                obscureText:
                                    context.read<ProfileCubit>().isObscure1,
                                suffixIcon: IconButton(
                                  onPressed: () => context
                                      .read<ProfileCubit>()
                                      .toggleObscure1(),
                                  icon: Icon(
                                      context.read<ProfileCubit>().isObscure1
                                          ? Icons.visibility
                                          : Icons.visibility_off),
                                ),
                                borderRadius: AppConstants.borderRadius,
                                validator: (value) =>
                                    AppValidator.validatePassword(value),
                                onChanged: (_) =>
                                    profileCubit.validatePasswordConditions(),
                              );
                            },
                          ),
                          16.verticalSpace,
                          Text(
                            'moreSales.confirmPassword'.tr(),
                            style: Styles.contentEmphasis,
                          ),
                          8.verticalSpace,
                          BlocBuilder<ProfileCubit, ProfileState>(
                            buildWhen: (previous, current) =>
                                current is TogglePasswordState,
                            builder: (context, state) {
                              return CustomTextFormFieldWidget(
                                controller: context
                                    .read<ProfileCubit>()
                                    .confirmNewPassword,
                                hintText: 'moreSales.enterConfirmPassword'.tr(),

                                keyboardType: TextInputType.visiblePassword,
                                // textInputAction: TextInputAction.done,
                                obscureText:
                                    context.read<ProfileCubit>().isObscure2,
                                suffixIcon: IconButton(
                                  onPressed: () => context
                                      .read<ProfileCubit>()
                                      .toggleObscure2(),
                                  icon: Icon(
                                      context.read<ProfileCubit>().isObscure2
                                          ? Icons.visibility
                                          : Icons.visibility_off),
                                ),
                                borderRadius: AppConstants.borderRadius,
                                validator: (value) =>
                                    AppValidator.validateConfirmPassword(
                                        value, profileCubit.newPassword.text),
                                onChanged: (_) =>
                                    profileCubit.validatePasswordConditions(),
                              );
                            },
                          ),
                          12.verticalSpace,
                          BlocBuilder<ProfileCubit, ProfileState>(
                            buildWhen: (previous, current) =>
                                current is PasswordValidationChanged,
                            builder: (context, state) {
                              final cubit = context.read<ProfileCubit>();
                              return Column(
                                children: [
                                  PasswordConditionItem(
                                    iconPath: cubit.hasMinLength
                                        ? "assets/images/svgs/check_Icon_green.svg"
                                        : "assets/images/svgs/check_Icon_red.svg",
                                    text: 'auth.min8Characters'.tr(),
                                    textColor: cubit.hasMinLength
                                        ? AppColors.greenColor200
                                        : AppColors.redColor100,
                                  ),
                                  12.verticalSpace,
                                  PasswordConditionItem(
                                    iconPath: cubit.passwordsMatch
                                        ? "assets/images/svgs/check_Icon_green.svg"
                                        : "assets/images/svgs/check_Icon_red.svg",
                                    text: 'auth.2passwords'.tr(),
                                    textColor: cubit.passwordsMatch
                                        ? AppColors.greenColor200
                                        : AppColors.redColor100,
                                  ),
                                  12.verticalSpace,
                                  PasswordConditionItem(
                                    iconPath: cubit.hasUpperLowerCase
                                        ? "assets/images/svgs/check_Icon_green.svg"
                                        : "assets/images/svgs/check_Icon_red.svg",
                                    text: 'auth.leastoneCase'.tr(),
                                    textColor: cubit.hasUpperLowerCase
                                        ? AppColors.greenColor200
                                        : AppColors.redColor100,
                                  ),
                                  12.verticalSpace,
                                  PasswordConditionItem(
                                    iconPath: cubit.hasNumber
                                        ? "assets/images/svgs/check_Icon_green.svg"
                                        : "assets/images/svgs/check_Icon_red.svg",
                                    text: 'auth.oneNumericCharacter'.tr(),
                                    textColor: cubit.hasNumber
                                        ? AppColors.greenColor200
                                        : AppColors.redColor100,
                                  ),
                                ],
                              );
                            },
                          ),

                          // 12.verticalSpace,
                          // PasswordConditionItem(
                          //   iconPath: "assets/images/svgs/check_Icon_white.svg",
                          //   text: "Min 8 Characters",
                          //   textColor: AppColors.neutralColor600,
                          // ),
                          // 12.verticalSpace,
                          // PasswordConditionItem(
                          //   iconPath: "assets/images/svgs/check_Icon_white.svg",
                          //   text: "The 2 passwords are the same",
                          //   textColor: AppColors.neutralColor600,
                          // ),
                          // 12.verticalSpace,
                          // PasswordConditionItem(
                          //   iconPath: "assets/images/svgs/check_Icon_red.svg",
                          //   text: "At least one Case Character",
                          //   textColor: AppColors.redColor100,
                          // ),
                          // 12.verticalSpace,
                          // PasswordConditionItem(
                          //   iconPath: "assets/images/svgs/check_Icon_green.svg",
                          //   text: "At least one Case Character",
                          //   textColor: AppColors.greenColor200,
                          // ),
                          // 12.verticalSpace,
                          // PasswordConditionItem(
                          //   iconPath: "assets/images/svgs/check_Icon_green.svg",
                          //   text: "At least one Numeric Character",
                          //   textColor: AppColors.greenColor200,
                          // ),
                        ],
                      ),
                    )
                  else
                    Form(
                      key: profileCubit.formKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SvgPicture.asset(Assets
                              .assetsImagesSvgsContainerChangeOldPassword),
                          24.verticalSpace,
                          Text('moreSales.oldPassword'.tr()),
                          8.verticalSpace,
                          BlocBuilder<ProfileCubit, ProfileState>(
                            buildWhen: (previous, current) =>
                                current is TogglePasswordState,
                            builder: (context, state) {
                              return CustomTextFormFieldWidget(
                                controller:
                                    context.read<ProfileCubit>().oldPassword,
                                hintText: 'moreSales.enterOldPassword'.tr(),
                                keyboardType: TextInputType.visiblePassword,
                                // textInputAction: TextInputAction.done,
                                obscureText:
                                    context.read<ProfileCubit>().isObscure,
                                suffixIcon: IconButton(
                                  onPressed: () => context
                                      .read<ProfileCubit>()
                                      .toggleObscure(),
                                  icon: Icon(
                                      context.read<ProfileCubit>().isObscure
                                          ? Icons.visibility
                                          : Icons.visibility_off),
                                ),
                                borderRadius: AppConstants.borderRadius,
                                validator: (value) =>
                                    AppValidator.validatePassword(value),
                              );
                            },
                          ),
                          12.verticalSpace,
                          SizedBox(
                            width: double.infinity,
                            child: CustomRichText(
                              textAlign: TextAlign.right,
                              textStyle1: Styles.captionRegular.copyWith(
                                color: AppColors.primaryColor800,
                              ),
                              text1: 'moreSales.forgotPassword'.tr(),
                              onTap1: () {},
                            ),
                          )
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.all(20.sp),
            child: BlocListener<ProfileCubit, ProfileState>(
              listener: (context, state) {
                if (state is UpdatePasswordSuccessState) {
                  showchangePasswordBottonSheet(context);
                }
              },
              child: CustomButtonWidget(
                text: profileCubit.isConfirm == true
                    ? 'moreSales.next'.tr()
                    : 'moreSales.update'.tr(),
                width: double.infinity,
                elevation: 0,
                onPressed: () {
                  if (profileCubit.formKey.currentState?.validate() ?? false) {
                    profileCubit.isConfirm != true
                        ? profileCubit.updatePassword()
                        : profileCubit.changeToggleFunction();
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }
}
