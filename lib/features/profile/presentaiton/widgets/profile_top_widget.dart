import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/services/di/dependency_injection.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:erp/features/profile/bloc/cubit/profile_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProfileTopWidget extends StatelessWidget {
  const ProfileTopWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ProfileCubit(getIt())..getProfile(),
      child: BlocBuilder<ProfileCubit, ProfileState>(
        buildWhen: (previous, current) =>
            current is GetProfileErrorState ||
            current is GetProfileLoadingState ||
            current is GetProfileSuccessState,
        builder: (context, state) {
          if (state is GetProfileLoadingState) {
            return Skeletonizer(
              enabled: true,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 8.sp,
                  vertical: 12.sp,
                ),
                decoration: BoxDecoration(
                  borderRadius:
                      BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(
                    width: 1.sp,
                    color: AppColors.primaryColor800,
                  ),
                ),
                child: Row(
                  children: [
                    ClipRRect(
                      borderRadius:
                          BorderRadius.circular(AppConstants.borderRadius),
                      child: ImagesWidget(
                        image: Assets.assetsImagesPngsEditProfileImage,
                        height: 50.sp,
                        width: 50.sp,
                      ),
                    ),
                    20.horizontalSpace,
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Ahmed",
                            style: Styles.highlightStandard,
                          ),
                          SizedBox(height: 4.sp),
                          Text(
                            "<EMAIL>",
                            style: Styles.highlightEmphasis.copyWith(
                              color: AppColors.neutralColor600,
                              fontSize: 12.sp,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                    ImagesWidget(image: Assets.assetsImagesSvgsEditProfileIcon)
                  ],
                ),
              ),
            );
          }
          return InkWell(
            onTap: () {
              context.pushNamed(Routes.profileScreen,
                  arguments: context.read<ProfileCubit>());
            },
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: 8.sp,
                vertical: 12.sp,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(
                  width: 1.sp,
                  color: AppColors.primaryColor800,
                ),
              ),
              child: Row(
                children: [
                  // ClipRRect(
                  //   borderRadius:
                  //       BorderRadius.circular(AppConstants.borderRadius),
                  //   child: CachedNetworkImage(
                  //     placeholder: (context, url) => Skeletonizer(
                  //       enabled: true,
                  //       child: Container(
                  //         decoration: BoxDecoration(
                  //           shape: BoxShape.circle,
                  //           color: AppColors.neutralColor300,
                  //         ),
                  //       ),
                  //     ),
                  //     imageUrl: "",
                  //     fit: BoxFit.fill,
                  //     width: 50.sp,
                  //     height: 50.sp,
                  //   ),
                  // ),
                  // 20.horizontalSpace,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.read<ProfileCubit>().profileModel!.data.name,
                          style: Styles.highlightStandard,
                        ),
                        SizedBox(height: 4.sp),
                        Text(
                          context.read<ProfileCubit>().profileModel!.data.email,
                          style: Styles.highlightEmphasis.copyWith(
                            color: AppColors.neutralColor600,
                            fontSize: 12.sp,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                  ImagesWidget(image: Assets.assetsImagesSvgsEditProfileIcon)
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
