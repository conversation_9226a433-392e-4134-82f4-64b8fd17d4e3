import 'dart:async';

import 'package:erp/core/helper_functions/roles_function.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/auth/data/repos/auth_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  AuthCubit(this.authRepository) : super(AuthInitial());

  final AuthRepository authRepository;
  // final TextEditingController emailController =
  //     TextEditingController(text: "<EMAIL>");
  // final TextEditingController passwordController =
  //     TextEditingController(text: "mm123456");
  final TextEditingController emailController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  final TextEditingController confirmNewPassword = TextEditingController();
  final pinController = TextEditingController();
  final resonController = TextEditingController();

  Timer? timer;
  int seconds = 60;
  bool canResend = false;

  String? otpCode;

  /// Start Countdown Timer
  void startTimer() {
    seconds = 60;
    canResend = false;
    emit(CountdownUpdated(seconds, canResend));

    timer?.cancel();
    timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (seconds > 1) {
        seconds--;
        emit(CountdownUpdated(seconds, canResend));
      } else {
        timer.cancel();
        canResend = true;
        emit(CountdownUpdated(seconds, canResend));
      }
    });
  }

  /// Stop Timer
  void stopTimer() {
    timer?.cancel();
  }

  bool isObscure = true;
  bool isObscure2 = true;

  final formKey = GlobalKey<FormState>();

  /// Toggle Password
  void toggleObscure() {
    isObscure = !isObscure;
    emit(TogglePasswordState());
  }

  void toggleObscure2() {
    isObscure2 = !isObscure2;
    emit(TogglePasswordState());
  }

  /// Login
  Future userLogin() async {
    showLoading();
    emit(LoginLoading());
    final result = await authRepository.userLogin(
      email: emailController.text,
      password: passwordController.text,
    );
    result.when(success: (success) {
      hideLoading();
      navigateBasedOnRole();

      emit(LoginSuccess());
    }, failure: (error) {
      hideLoading();
      emit(LoginError());
    });
  }

  @override
  Future<void> close() {
    emailController.dispose();
    passwordController.dispose();
    stopTimer();
    return super.close();
  }
}
