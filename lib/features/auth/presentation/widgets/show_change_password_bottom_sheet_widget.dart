import 'dart:ui';

import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/image_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

void showchangePasswordBottonSheet(BuildContext context) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) {
      return Stack(
        children: [
          Positioned.fill(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
              child: Container(
                color: Colors.black.withValues(alpha: 0.1),
              ),
            ),
          ),
          PopScope(
            canPop: false,
            child: Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                padding: EdgeInsets.all(20.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20.r)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ImagesWidget(image: Assets.assetsImagesSvgsTaskFinshIcon),
                    SizedBox(height: 32.h),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text: 'auth.congratulation!'.tr(),
                            style: Styles.featureEmphasis.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.primaryColor800,
                            ),
                          ),
                          TextSpan(
                            text: 'auth.passwordhasbeenchanged'.tr(),
                            style: Styles.featureEmphasis.copyWith(
                              fontWeight: FontWeight.w600,
                              color: AppColors.neutralColor1600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'auth.trytokeepthepassword'.tr(),
                      textAlign: TextAlign.center,
                      style: Styles.captionEmphasis.copyWith(
                        fontWeight: FontWeight.w600,
                        fontSize: 14.sp,
                        color: AppColors.neutralColor600,
                      ),
                    ),
                    SizedBox(height: 32.h),
                    CustomButtonWidget(
                      text: 'auth.login'.tr(),
                      onPressed: () {
                        context.pop();
                      },
                    ),
                    SizedBox(height: 10.h),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    },
  );
}
