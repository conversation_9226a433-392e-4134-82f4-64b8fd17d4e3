import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/model/lead_details_model.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/repo/lead_details_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

class ClientDetailsCubit extends Cubit<ClientDetailsState> {
  final LeadDetailsRepo _leadDetailsRepo;

  ClientDetailsCubit(this._leadDetailsRepo) : super(LeadDetailsInitial()) {
    setupScrollController();
  }
  LeadDetailsModel? leadDetailsModel;
  Future<void> getLeadDetails({required int leadId}) async {
    emit(LeadDetailsLoading());

    final result = await _leadDetailsRepo.getLeadDetails(leadId: leadId);

    result.when(
      success: (data) async {
        leadDetailsModel = data;
        await getListProjects();
        // fillControllers();

        emit(LeadDetailsSuccess());
      },
      failure: (error) => emit(LeadDetailsFailure()),
    );
  }

  void fillControllers() {
    final lead = leadDetailsModel!.data;
    nameController.text = lead.name;
    emailController.text = lead.email;
    phoneController.text = lead.phone;
    genderController.text = lead.gender;
    countryController.text = lead.country;
    companyNameController.text = lead.companyName ?? "";
    companyAddressController.text = lead.companyAddress ?? "";
    companyPhoneController.text = lead.companyPhone ?? "";
    notesController.text = lead.notes ?? "";
    profileImage = null;
    companyLogo = null;
  }

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final genderController = TextEditingController();
  final countryController = TextEditingController();
  final companyNameController = TextEditingController();
  final companyAddressController = TextEditingController();
  final companyPhoneController = TextEditingController();
  final notesController = TextEditingController();
  File? companyLogo;
  File? profileImage;

  final ImagePicker picker = ImagePicker();

  Future<void> pickCompanyLogo() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      companyLogo = File(pickedFile.path);
    }
    emit(PickCompanyImage());
  }

  Future<void> pickProfileImage() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      profileImage = File(pickedFile.path);
    }
    emit(PickClientImage());
  }

  Future<void> updateClient() async {
    emit(UpdateClientLoading());
    showLoading();
    final data = FormData.fromMap({
      "_method": "put",
      'name': nameController.text,
      'email': emailController.text,
      'phone': phoneController.text,
      'gender': genderController.text,
      'country': countryController.text,
      'employee_id': "",
      'type': "client",
      'company_name': companyNameController.text,
      'company_address': companyAddressController.text,
      'company_phone': companyPhoneController.text,
      'notes': notesController.text,
      if (companyLogo != null)
        'company_logo': await MultipartFile.fromFile(
          companyLogo!.path,
          filename: 'company_logo.jpg',
        ),
      if (profileImage != null)
        'image': await MultipartFile.fromFile(
          profileImage!.path,
          filename: 'image.jpg',
        ),
    });
    final result = await _leadDetailsRepo.updateClient(
      clientId: leadDetailsModel!.data.id,
      data: data,
    );

    result.when(
      success: (data) {
        leadDetailsModel = data;
        hideLoading();
        emit(UpdateClientSuccess());
      },
      failure: (error) {
        hideLoading();

        emit(UpdateClientFailure());
      },
    );
  }

  Future<void> deleteClient() async {
    emit(DeleteClientLoading());
    showLoading();
    final result = await _leadDetailsRepo.deleteClient(
      clientId: leadDetailsModel!.data.id,
    );

    result.when(
      success: (_) {
        hideLoading();
        emit(DeleteClientSuccess());
      },
      failure: (error) {
        hideLoading();

        emit(DeleteClientFailure());
      },
    );
  }

  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreProjectslist();
      }
    });
  }

  Future<void> getListProjects() async {
    final result = await _leadDetailsRepo.getListProjects(
        page: currentPage, clientId: [leadDetailsModel!.data.id]);
    {
      result.when(
          success: (data) {
            getListProjectModel = data;
            currentPage = data.data.meta.currentPage;
            lastPage = data.data.meta.lastPage;
          },
          failure: (error) {});
    }
  }

  Future<void> loadMoreProjectslist() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(GetListProjectsSalesLoadingMore());

    final nextPage = currentPage + 1;
    final result = await _leadDetailsRepo
        .getListProjects(page: nextPage, clientId: [leadDetailsModel!.data.id]);

    result.when(
      success: (data) {
        getListProjectModel!.data.data.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(GetListProjectSalesSuccess());
      },
      failure: (error) {
        emit(GetListProjectSalesError());
      },
    );

    isLoadingMore = false;
  }

  ProjectResponse? getListProjectModel;

  final TextEditingController projectCode = TextEditingController();
  final TextEditingController projectName = TextEditingController();
  final TextEditingController startDate = TextEditingController();
  final TextEditingController deadline = TextEditingController();
  final TextEditingController notes = TextEditingController();
  final TextEditingController details = TextEditingController();
  List<int> selectedEmployees = [];
  List<int> selectedDepartments = [];
  List<MultipartFile> attachedFiles = [];
  Future<void> createProject() async {
    emit(CreateProjectLoading());
    showLoading();
    final result = await _leadDetailsRepo.createProject(
      name: projectName.text,
      clientId: leadDetailsModel!.data.id,
      startdate: startDate.text,
      deadline: deadline.text,
      employees: selectedEmployees,
      departments: selectedDepartments,
      notes: notes.text,
      details: details.text,
      files: attachedFiles,
    );
    result.when(
      success: (data) async {
        hideLoading();
        await getListProjects();
        leadDetailsModel!.data.haveProject = true;
        emit(CreateProjectSuccess());
      },
      failure: (error) {
        hideLoading();
        emit(CreateProjectFailure());
      },
    );
  }

  void setStartDate(DateTime date) {
    startDate.text = date.toIso8601String().split('T')[0];
    emit(CreateProjectStartDateSet());
  }

  void clearControllers() {
    projectCode.clear();
    projectName.clear();
    startDate.clear();
    deadline.clear();
    notes.clear();
    details.clear();
  }

  @override
  Future<void> close() {
    scrollController.dispose();

    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    genderController.dispose();
    countryController.dispose();
    companyNameController.dispose();
    companyAddressController.dispose();
    companyPhoneController.dispose();
    notesController.dispose();
    projectCode.dispose();
    projectName.dispose();
    startDate.dispose();
    deadline.dispose();
    notes.dispose();
    details.dispose();

    companyLogo = null;
    profileImage = null;

    return super.close();
  }
}
