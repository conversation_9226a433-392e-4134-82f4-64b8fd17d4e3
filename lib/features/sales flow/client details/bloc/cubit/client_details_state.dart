abstract class ClientDetailsState {}

class LeadDetailsInitial extends ClientDetailsState {}

class LeadDetailsLoading extends ClientDetailsState {}

class LeadDetailsSuccess extends ClientDetailsState {}

class LeadDetailsFailure extends ClientDetailsState {}

class GetLeadDetailsFailure extends ClientDetailsState {}

class UpdateClientLoading extends ClientDetailsState {}

class UpdateClientSuccess extends ClientDetailsState {}

class UpdateClientFailure extends ClientDetailsState {}

class DeleteClientLoading extends ClientDetailsState {}

class DeleteClientSuccess extends ClientDetailsState {}

class DeleteClientFailure extends ClientDetailsState {}

class PickCompanyImage extends ClientDetailsState {}

class PickClientImage extends ClientDetailsState {}

class UpdateAfterEditState extends ClientDetailsState {}
class PickquotePdf extends ClientDetailsState {}

/// get list projects
class GetListProjectSalesLoading extends ClientDetailsState {}

class GetListProjectSalesSuccess extends ClientDetailsState {}

class GetListProjectSalesError extends ClientDetailsState {}

class GetListProjectsSalesLoadingMore extends ClientDetailsState {}




class CreateProjectLoading extends ClientDetailsState {}

class CreateProjectSuccess extends ClientDetailsState {}

class CreateProjectFailure extends ClientDetailsState {}



class CreateProjectEmployeeSelected extends ClientDetailsState {}

class CreateProjectDepartmentSelected extends ClientDetailsState {}

class CreateProjectStartDateSet extends ClientDetailsState {}