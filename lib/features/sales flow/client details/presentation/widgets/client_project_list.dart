import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_cubit.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/quote_sales_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClientProjectsSection extends StatelessWidget {
  const ClientProjectsSection({super.key, required this.cubit});

  final ClientDetailsCubit cubit;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: BlocBuilder<ClientDetailsCubit, ClientDetailsState>(
            buildWhen: (previous, current) =>
                current is GetListProjectSalesSuccess ||
                current is CreateProjectSuccess,
            builder: (context, state) {
              final hasProjects =
                  cubit.leadDetailsModel?.data.haveProject ?? false;

              if (!hasProjects) {
                return NoProjectWidget(
                  title: 'clientsDetails.noProjectsFound'.tr(),
                  description: 'clientsDetails.toAddAQuote'.tr(),
                  buttomText: 'clientsDetails.addProject'.tr(),
                  onPressed: () {
                    context.pushNamed(Routes.addClientProjectScreen,
                        arguments: cubit);
                  },
                );
              }

              final projects = cubit.getListProjectModel?.data.data ?? [];

              return ListView.separated(
                controller: cubit.scrollController,
                itemCount: projects.length,
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                separatorBuilder: (context, index) => 16.sp.verticalSpace,
                itemBuilder: (context, index) {
                  final project = projects[index];
                  return ProjectsItemWidget(
                    onTap: () => context.pushNamed(
                      Routes.salesProjectDetails,
                      arguments: project.id,
                    ),
                    title: project.name,
                    description: project.deadline ?? '',
                  );
                },
              );
            },
          ),
        ),
        BlocBuilder<ClientDetailsCubit, ClientDetailsState>(
          buildWhen: (previous, current) =>
              current is GetListProjectsSalesLoadingMore ||
              current is GetListProjectSalesSuccess,
          builder: (context, state) {
            if (state is GetListProjectsSalesLoadingMore) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }
            return SizedBox.shrink();
          },
        ),
        10.verticalSpace,
        if (cubit.getListProjectModel!.data.data.isNotEmpty)
          CustomButtonWidget(
            text: 'clientsDetails.addProject'.tr(),
            onPressed: () => context.pushNamed(
              Routes.addClientProjectScreen,
              arguments: cubit,
            ),
          ),
        20.verticalSpace,
      ],
    );
  }
}
