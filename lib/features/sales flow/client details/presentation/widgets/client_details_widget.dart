import 'package:easy_localization/easy_localization.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/leads_widge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClientDetails extends StatelessWidget {
  const ClientDetails({
    super.key,
    required this.cubit,
  });

  final ClientDetailsCubit cubit;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(
        horizontal: 12.sp,
        vertical: 18.sp,
      ),
      child: LeadsWidget(
        projectInfo: [
          {
            "title": 'clientsDetails.email'.tr(),
            "value": cubit.leadDetailsModel!.data.email
          },
          {
            "title": 'clientsDetails.gender'.tr(),
            "value": cubit.leadDetailsModel!.data.gender
          },
          {
            "title": 'clientsDetails.phone'.tr(),
            "value": cubit.leadDetailsModel!.data.phone
          },
          {
            "title": 'clientsDetails.country'.tr(),
            "value": cubit.leadDetailsModel!.data.country
          },
        ],
        companyLogo: cubit.leadDetailsModel!.data.companyLogo,
        note: cubit.leadDetailsModel!.data.notes ?? "",
        projectInfoDetails: [
          {
            "title": 'clientsDetails.companyName'.tr(),
            "value": cubit.leadDetailsModel!.data.companyName ?? ""
          },
          {
            "title": 'clientsDetails.companyAddress'.tr(),
            "value": cubit.leadDetailsModel!.data.companyAddress ?? "",
          },
          {
            "title": 'clientsDetails.officePhoneNumber'.tr(),
            "value": cubit.leadDetailsModel!.data.companyPhone ?? ""
          },
        ],
      ),
    );
  }
}
