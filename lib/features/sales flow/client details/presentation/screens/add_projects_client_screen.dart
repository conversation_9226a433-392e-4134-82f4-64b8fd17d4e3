import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/sales%20flow/add%20lead/presentation/widgets/form_widget.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_cubit.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddClientProjectScreen extends StatelessWidget {
  const AddClientProjectScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ClientDetailsCubit>();
    final formKey = GlobalKey<FormState>();
    cubit.clearControllers();

    return Scaffold(
      appBar: AppBar(
        title: Text('clientsDetails.addProject'.tr()),
        leading: BackButton(),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.0.sp),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              FormLabel(text: 'clientsDetails.projectCode'.tr()),
              CustomTextFormFieldWidget(
                controller: cubit.projectCode,
                keyboardType: TextInputType.number,
                validator: (value) => AppValidator.validateNotEmpty(value,
                    fieldName: "projectCode"),
                borderRadius: AppConstants.borderRadius,
              ),
              16.verticalSpace,
              FormLabel(text: 'clientsDetails.ProjectName'.tr()),
              CustomTextFormFieldWidget(
                controller: cubit.projectName,
                hintText: 'clientsDetails.enterProjectName'.tr(),
                validator: (value) => AppValidator.validateNotEmpty(value,
                    fieldName: "projectName"),
                borderRadius: AppConstants.borderRadius,
              ),
              16.verticalSpace,
              FormLabel(
                text: 'clientsDetails.StartDate'.tr(),
              ),
              CustomTextFormFieldWidget(
                controller: cubit.startDate,
                readOnly: true,
                onTap: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime(2101),
                  );
                  if (picked != null) cubit.setStartDate(picked);
                },
                validator: (value) => value == null || value.isEmpty
                    ? 'clientsDetails.selectStartDate'.tr()
                    : null,
                suffixIcon: Icon(Icons.calendar_month,
                    color: AppColors.neutralColor700),
              ),
              16.verticalSpace,
              FormLabel(
                text: 'clientsDetails.Deadline'.tr(),
              ),
              CustomTextFormFieldWidget(
                controller: cubit.deadline,
                readOnly: true,
                onTap: () async {
                  final picked = await showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime(2101),
                  );
                  if (picked != null) {
                    cubit.deadline.text =
                        picked.toIso8601String().split('T')[0];
                  }
                },
                validator: (value) => value == null || value.isEmpty
                    ? 'clientsDetails.selectDeadline'.tr()
                    : null,
                suffixIcon: Icon(Icons.calendar_month,
                    color: AppColors.neutralColor700),
              ),
              16.verticalSpace,
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
        child: BlocListener<ClientDetailsCubit, ClientDetailsState>(
          listener: (context, state) {
            if (state is CreateProjectSuccess) {
              context.pop();
            }
          },
          child: CustomButtonWidget(
            text: 'clientsDetails.addProject'.tr(),
            onPressed: () {
              if (formKey.currentState!.validate()) {
                cubit.createProject();
              }
            },
          ),
        ),
      ),
    );
  }
}
