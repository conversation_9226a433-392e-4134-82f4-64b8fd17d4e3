import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_cubit.dart';
import 'package:erp/features/sales%20flow/client%20details/bloc/cubit/client_details_state.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/widgets/client_details_widget.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/widgets/client_project_list.dart';
import 'package:erp/features/sales%20flow/client%20details/presentation/widgets/client_skelton_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClientDeatilsScreen extends StatelessWidget {
  const ClientDeatilsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<ClientDetailsCubit>();
    return BlocBuilder<ClientDetailsCubit, ClientDetailsState>(
      buildWhen: (previous, current) =>
          current is LeadDetailsLoading ||
          current is LeadDetailsSuccess ||
          current is UpdateAfterEditState ||
          current is LeadDetailsFailure,
      builder: (context, state) {
        if (state is LeadDetailsLoading) {
          return const ClientSkeltonWidget();
        }

        return DefaultTabController(
          length: 2,
          child: WillPopScope(
            onWillPop: () async {
              Navigator.of(context).pop(cubit.leadDetailsModel);
              return true;
            },
            child: Scaffold(
              appBar: AppBar(
                title: Text(cubit.leadDetailsModel!.data.name),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () =>
                      Navigator.of(context).pop(cubit.leadDetailsModel),
                ),
              ),
              body: Column(
                children: [
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 16.sp),
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(vertical: 16.sp),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.neutralColor300),
                      borderRadius: BorderRadius.circular(8.sp),
                    ),
                    child: Column(
                      children: [
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8.sp),
                          child: CachedNetworkImage(
                            imageUrl: cubit.leadDetailsModel!.data.image,
                            height: 60.sp,
                            width: 60.sp,
                            fit: BoxFit.cover,
                            placeholder: (context, url) =>
                                const CircularProgressIndicator(),
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        ),
                        12.verticalSpace,
                        Text(
                          cubit.leadDetailsModel!.data.name,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w500,
                            color: Colors.black,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        4.verticalSpace,
                        Text(
                          cubit.leadDetailsModel!.data.email,
                        )
                      ],
                    ),
                  ),
                  12.verticalSpace,
                  TabBar(
                    dividerHeight: 0.h,
                    dividerColor: AppColors.neutralColor300,
                    labelColor: AppColors.primaryColor800,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: AppColors.primaryColor800,
                    tabs: [
                      Tab(text: 'clientsDetails.accountDetails'.tr()),
                      Tab(text: 'clientsDetails.projects'.tr()),
                    ],
                  ),
                  Expanded(
                    child: TabBarView(
                      children: [
                        Column(
                          children: [
                            Expanded(child: ClientDetails(cubit: cubit)),
                            Row(
                              spacing: 20.sp,
                              children: [
                                Expanded(
                                  child: BlocListener<ClientDetailsCubit,
                                      ClientDetailsState>(
                                    listenWhen: (previous, current) =>
                                        current is DeleteClientFailure ||
                                        current is DeleteClientSuccess ||
                                        current is DeleteClientLoading,
                                    listener: (context, state) {
                                      if (state is DeleteClientSuccess) {
                                        Navigator.of(context).pop(true);
                                      }
                                    },
                                    child: CustomButtonWidget(
                                      margin: EdgeInsets.all(13.sp),
                                      text: 'clientsDetails.delete'.tr(),
                                      textColor: AppColors.primaryColor800,
                                      color: Colors.white,
                                      borderColor: BorderSide(
                                          color: AppColors.primaryColor800),
                                      onPressed: () {
                                        cubit.deleteClient();
                                      },
                                    ),
                                  ),
                                ),
                                Expanded(
                                  child: CustomButtonWidget(
                                    margin: EdgeInsets.all(13.sp),
                                    text: 'clientsDetails.edit'.tr(),
                                    color: Colors.white,
                                    textColor: AppColors.primaryColor800,
                                    borderColor: BorderSide(
                                        color: AppColors.primaryColor800),
                                    onPressed: () {
                                      context
                                          .pushNamed(Routes.editClientScreen,
                                              arguments: cubit)
                                          .then((v) {
                                        if (v == true) {
                                          cubit.emit(UpdateAfterEditState());
                                        }
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                        ClientProjectsSection(cubit: cubit),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
