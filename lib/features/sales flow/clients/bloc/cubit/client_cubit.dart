import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_state.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';
import 'package:erp/features/sales%20flow/leads/data/repo/leads_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ClientCubit extends Cubit<ClientState> {
  final LeadRepo _leadRepo;

  ClientCubit(this._leadRepo) : super(ClientInitial()) {
    setupScrollController();
  }

  LeadResponse? leadResponse;
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;
  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreClients();
      }
    });
  }

  Future<void> getLeads() async {
    emit(ClientLoading());
    currentPage = 1;

    final result = await _leadRepo.getLeads(type: "client", page: currentPage);

    result.when(
      success: (data) {
        leadResponse = data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(ClientSuccess());
      },
      failure: (error) => emit(ClientFailure()),
    );
  }

  Future<void> loadMoreClients() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(ClientLoadingMore());

    final nextPage = currentPage + 1;
    final result = await _leadRepo.getLeads(type: "client", page: nextPage);

    result.when(
      success: (data) {
        leadResponse!.data.data.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(ClientSuccess());
      },
      failure: (error) {
        emit(ClientFailure());
      },
    );

    isLoadingMore = false;
  }

  void removeIndex(id) {
    leadResponse!.data.data.removeWhere((element) => element.id == id);
    emit(RemoveItemAfterDeleted());
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    return super.close();
  }
}
