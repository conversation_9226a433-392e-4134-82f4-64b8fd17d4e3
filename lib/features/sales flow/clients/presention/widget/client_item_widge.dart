import 'package:cached_network_image/cached_network_image.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_cubit.dart';
import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/model/lead_details_model.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClientItemWidget extends StatelessWidget {
  const ClientItemWidget({super.key, required this.lead});
  final LeadItem lead;
  @override
  Widget build(BuildContext context) {
    final leadsCubit = context.read<ClientCubit>();

    return InkWell(
      onTap: () {
        context.pushNamed(Routes.clientDetails, arguments: lead.id).then((v) {
          if (v == true) {
            leadsCubit.removeIndex(lead.id);
          } else if (v is LeadDetailsModel) {
            lead.country = v.data.country;
            lead.email = v.data.email;
            lead.employee = v.data.employee;
            lead.gender = v.data.gender;
            lead.image = v.data.image;
            lead.phone = v.data.phone;
            lead.name = v.data.name;
            leadsCubit.emit(RemoveItemAfterDeleted());
          } else if (v is int) {
            leadsCubit.removeIndex(lead.id);
          }
        });
      },
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
        child: Row(
          children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(8.sp),
                child: CachedNetworkImage(
                  imageUrl: lead.image,
                  height: 50.sp,
                  width: 50.sp,
                  placeholder: (context, url) => const Center(
                    child: CircularProgressIndicator(),
                  ),
                  errorWidget: (context, url, error) =>
                      Center(child: const Icon(Icons.error)),
                )),
            SizedBox(width: 12.sp),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    lead.name,
                    style: Styles.highlightEmphasis,
                  ),
                  SizedBox(height: 4.sp),
                  Text(
                    lead.email,
                    style: Styles.contentEmphasis
                        .copyWith(color: AppColors.neutralColor600),
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            SizedBox(width: 10.sp),
            const Icon(Icons.chevron_right, color: Colors.grey),
          ],
        ),
      ),
    );
  }
}
