import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_cubit.dart';
import 'package:erp/features/sales%20flow/clients/bloc/cubit/client_state.dart';
import 'package:erp/features/sales%20flow/clients/presention/widget/client_item_widge.dart';
import 'package:erp/features/sales%20flow/clients/presention/widget/client_skelton_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ClientScreen extends StatelessWidget {
  const ClientScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final clientCubit = context.read<ClientCubit>();
    return BlocBuilder<ClientCubit, ClientState>(
      buildWhen: (previous, current) =>
          current is ClientLoading ||
          current is ClientSuccess ||
          current is ClientFailure,
      builder: (context, state) {
        if (state is ClientLoading) {
          return ClientSkeltoonWidget();
        }
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            title: Text(
              'clients.clients'.tr(),
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
            ),
          
          ),
          body: BlocBuilder<ClientCubit, ClientState>(
            buildWhen: (previous, current) => current is RemoveItemAfterDeleted,
            builder: (context, state) {
              if (clientCubit.leadResponse!.data.data.isEmpty) {
                return Center(
                  child: Text(
                    'clients.noClientsFound'.tr(),
                    style: TextStyle(
                      color: AppColors.neutralColor600,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }
              return Column(
                children: [
                  Expanded(
                    child: ListView.separated(
                      controller: clientCubit.scrollController,
                      itemCount: clientCubit.leadResponse!.data.data.length,
                      separatorBuilder: (_, __) => Divider(
                        height: 1.sp,
                        color: AppColors.neutralColor600,
                      ),
                      itemBuilder: (context, index) {
                        return ClientItemWidget(
                            lead: clientCubit.leadResponse!.data.data[index]);
                      },
                    ),
                  ),
                  BlocBuilder<ClientCubit, ClientState>(
                    buildWhen: (previous, current) =>
                        current is ClientLoadingMore ||
                        current is ClientSuccess,
                    builder: (context, state) {
                      if (state is ClientLoadingMore) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(16.0),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  )
                ],
              );
            },
          ),
        );
      },
    );
  }
}
