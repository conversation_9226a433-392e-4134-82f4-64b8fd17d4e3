import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class CustomAccountDetailsWidget extends StatelessWidget {
  final String text;
  final dynamic icon;
  final bool isActive;

  const CustomAccountDetailsWidget({
    super.key,
    required this.text,
    required this.icon,
    required this.isActive,
  });

  @override
  Widget build(BuildContext context) {
    Color textColor =
        isActive ? AppColors.neutralColor1400 : AppColors.neutralColor600;
    Color iconColor =
        isActive ? AppColors.primaryColor900 : AppColors.neutralColor600;

    return Column(
      spacing: 10.sp,
      children: [
        Container(
          padding: EdgeInsets.all(15.sp),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: iconColor,
              width: 1.5.sp,
            ),
          ),
          child: icon is String
              ? SvgPicture.asset(
                  icon,
                  color: iconColor,
                  height: 24.sp,
                )
              : Icon(
                  icon,
                  color: iconColor,
                ),
        ),
        Text(text,
            style: Styles.contentEmphasis.copyWith(
              color: textColor,
              fontSize: 14.sp,
            )),
      ],
    );
  }
}
