import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_drop_down_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/sales%20flow/add%20lead/bloc/cubit/add_lead_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class FormWidget extends StatelessWidget {
  const FormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    AddLeadCubit addLeadCubit = BlocProvider.of<AddLeadCubit>(context);

    // return BlocBuilder<AddLeadCubit, AddLeadState>(
    //   buildWhen: (previous, current) => current is ChangeToggleState,
    //   builder: (context, state) {
    if (addLeadCubit.isAccountDetails) {
      return Form(
        key: addLeadCubit.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24.h),
            FormLabel(text: 'lead.clientName'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterClientName'.tr(),
              controller: addLeadCubit.nameController,
              keyboardType: TextInputType.name,
              validator: (value) => AppValidator.validateNotEmpty(value,
                  fieldName: "Client Name"),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.emailAddress'.tr()),
            CustomTextFormFieldWidget(
              controller: addLeadCubit.emailController,
              hintText: 'lead.enterEmailAddress'.tr(),
              keyboardType: TextInputType.emailAddress,
              validator: (value) => AppValidator.validateEmail(value),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(
              text: 'lead.gender'.tr(),
            ),
            CustomDropdownButton(
              items: const ['male', 'female', 'other'],
              hint: 'lead.selectGender'.tr(),
              validator: (value) => AppValidator.validateNotEmpty(value,
                  fieldName: "Select Gender"),
              onChanged: (value) {
                addLeadCubit.genderController.text = value!;
              },
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.phoneNumber'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterPhoneNumber'.tr(),
              keyboardType: TextInputType.phone,
              controller: addLeadCubit.phoneController,
              validator: (value) => AppValidator.validatePhoneNumber(value),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.country'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterCountry'.tr(),
              keyboardType: TextInputType.name,
              controller: addLeadCubit.countryController,
              validator: (value) =>
                  AppValidator.validateNotEmpty(value, fieldName: "Country"),
              borderRadius: AppConstants.borderRadius,
            ),
          ],
        ),
      );
    } else {
      return Form(
        key: addLeadCubit.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 24.h),
            FormLabel(text: 'lead.companyName'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterCompanyName'.tr(),
              keyboardType: TextInputType.name,
              controller: addLeadCubit.companyNameController,
              validator: (value) => AppValidator.validateNotEmpty(value,
                  fieldName: "Company Name"),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.companyAddress'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterCompanyAddress'.tr(),
              keyboardType: TextInputType.streetAddress,
              controller: addLeadCubit.companyAddressController,
              validator: (value) => AppValidator.validateNotEmpty(value,
                  fieldName: "Company Address"),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.companyLogo'.tr()),
            BlocBuilder<AddLeadCubit, AddLeadState>(
              buildWhen: (previous, current) => current is PickCompanyImage,
              builder: (context, state) {
                return CustomTextFormFieldWidget(
                  hintText: 'lead.uploadPicture'.tr(),
                  keyboardType: TextInputType.text,
                  readOnly: true,
                  onTap: () {
                    addLeadCubit.pickCompanyLogo();
                  },
                  borderRadius: AppConstants.borderRadius,
                  suffixIcon: addLeadCubit.companyLogo != null
                      ? Image.file(
                          addLeadCubit.companyLogo!,
                          height: 40,
                        )
                      : SvgPicture.asset(Assets.assetsImagesSvgsGallery),
                );
              },
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.officePhoneNumber'.tr()),
            CustomTextFormFieldWidget(
              hintText: 'lead.enterOfficePhoneNumber'.tr(),
              keyboardType: TextInputType.phone,
              controller: addLeadCubit.companyPhoneController,
              validator: (value) => AppValidator.validatePhoneNumber(value),
              borderRadius: AppConstants.borderRadius,
            ),
            16.verticalSpace,
            FormLabel(text: 'lead.notes'.tr()),
            CustomTextFormFieldWidget(
              height: 50.sp,
              hintText: 'lead.enterNotes'.tr(),
              keyboardType: TextInputType.multiline,
              controller: addLeadCubit.notesController,
              validator: (value) => AppValidator.validateOptionalText(value),
              borderRadius: AppConstants.borderRadius,
            ),
          ],
        ),
      );
    }
    //   },
    // );
  }
}

class FormLabel extends StatelessWidget {
  final String text;

  const FormLabel({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8.0.sp),
      child: Text(
        text,
        style: Styles.contentEmphasis.copyWith(
          color: AppColors.neutralColor1400,
          fontSize: 16.sp,
        ),
      ),
    );
  }
}
