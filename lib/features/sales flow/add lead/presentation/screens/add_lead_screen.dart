import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/add%20lead/bloc/cubit/add_lead_cubit.dart';
import 'package:erp/features/sales%20flow/add%20lead/presentation/widgets/custom_accont_deatils.dart';
import 'package:erp/features/sales%20flow/add%20lead/presentation/widgets/form_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AddLeadScreen extends StatelessWidget {
  const AddLeadScreen({super.key});

  @override
  Widget build(BuildContext context) {
    AddLeadCubit addLeadCubit = BlocProvider.of<AddLeadCubit>(context);
    return Scaffold(
      appBar: AppBar(title: Text('lead.addLead'.tr()), centerTitle: true),
      body: BlocBuilder<AddLeadCubit, AddLeadState>(
        buildWhen: (previous, current) => current is ChangeToggleState,
        builder: (context, state) {
          return SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.symmetric(horizontal: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      CustomAccountDetailsWidget(
                        icon: Icons.person,
                        isActive: addLeadCubit.isAccountDetails,
                        text: 'lead.accountDetails'.tr(),
                      ),
                      CustomAccountDetailsWidget(
                        icon: Assets.assetsImagesSvgsDocumentIcon,
                        isActive: !addLeadCubit.isAccountDetails,
                        text: 'lead.compantDetails'.tr(),
                      )
                    ],
                  ),
                  16.verticalSpace,
                  if (addLeadCubit.isAccountDetails)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 4.sp,
                      children: [
                        SvgPicture.asset(Assets.assetsImagesSvgsDotBorderIcon),
                        SvgPicture.asset(
                          Assets.assetsImagesSvgsPrgressBarPrimary,
                        ),
                        SvgPicture.asset(Assets.assetsImagesSvgsDotIcon),
                      ],
                    )
                  else
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      spacing: 4.sp,
                      children: [
                        SvgPicture.asset(Assets.assetsImagesSvgsSuccessIcon),
                        SvgPicture.asset(
                          Assets.assetsImagesSvgsPrgressBarGreen,
                        ),
                        SvgPicture.asset(Assets.assetsImagesSvgsDotBorderIcon),
                      ],
                    ),
                  FormWidget(),
                ],
              ),
            ),
          );
        },
      ),
      bottomNavigationBar: BlocListener<AddLeadCubit, AddLeadState>(
        listenWhen: (previous, current) =>
            current is CreateLeadFailure ||
            current is CreateLeadLoading ||
            current is CreateLeadSuccess ||
            current is UpdateClientFailure ||
            current is UpdateClientLoading ||
            current is UpdateClientSuccess,
        listener: (context, state) {
          if (state is CreateLeadSuccess) {
            addLeadCubit.toggleAccountDetails();
          }
          if (state is UpdateClientSuccess) {
            context.pushNamedAndRemoveUntil(Routes.salesMainlayoutScreen);
          }
        },
        child: SafeArea(
            minimum: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
            child: CustomButtonWidget(
              text: addLeadCubit.isAccountDetails
                  ? 'lead.next'.tr()
                  : 'lead.add'.tr(),
              onPressed: () {
                if (addLeadCubit.formKey.currentState!.validate()) {
                  if (addLeadCubit.isAccountDetails) {
                    addLeadCubit.createLead();
                  } else {
                    addLeadCubit.updateClient();
                  }
                }
              },
            )),
      ),
    );
  }
}
