import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class CreateLeadApiServices {
  final DioHelper _dioFactory;

  CreateLeadApiServices(this._dioFactory);

  Future<Response?> createLead({
    required  data,
  }) async {
    return _dioFactory.post(
      endPoint: EndPoints.getLeads,
      data: data,
    );
  }
  
  Future<Response?> updateClient({
    required int clientId,
    required data,
  }) async {
    return _dioFactory.post(
      endPoint: "${EndPoints.getLeads}/$clientId",
      data: data,
    );
  }

}
