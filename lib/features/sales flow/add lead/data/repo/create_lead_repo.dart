import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/features/sales%20flow/add%20lead/data/api%20services/create_lead_api_services.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/model/lead_details_model.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';

class CreateLeadRepo {
  final CreateLeadApiServices _apiServices;

  CreateLeadRepo(this._apiServices);

  Future<ApiResult<LeadItem>> createLead({
    required leadData,
  }) async {
    final response = await _apiServices.createLead(data: leadData);

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(LeadItem.fromJson(response.data["data"]));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }
  
  Future<ApiResult<LeadDetailsModel>> updateClient({
    required int clientId,
    required  data,
  }) async {
    final response = await _apiServices.updateClient(
      clientId: clientId,
      data: data,
    );

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(LeadDetailsModel.fromJson(response.data));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }

}
