part of 'add_lead_cubit.dart';

sealed class AddLeadState {}

final class Add<PERSON>eadInitial extends AddLeadState {}

final class ChangeToggleState extends AddLeadState {}

class CreateLeadLoading extends AddLeadState {}

class CreateLeadSuc<PERSON> extends AddLeadState {}

class CreateLeadFailure extends AddLeadState {}

class PickClientImage extends AddLeadState {}

class PickCompanyImage extends AddLeadState {}

class UpdateClientLoading extends AddLeadState {}

class UpdateClientSuccess extends AddLeadState {}

class UpdateClientFailure extends AddLeadState {}
