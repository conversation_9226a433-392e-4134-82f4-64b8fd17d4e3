import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/sales%20flow/add%20lead/data/repo/create_lead_repo.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';

part 'add_lead_state.dart';

class AddLeadCubit extends Cubit<AddLeadState> {
  AddLeadCubit(this._repo) : super(AddLeadInitial());
  bool isAccountDetails = true;
  void toggleAccountDetails() {
    isAccountDetails = !isAccountDetails;
    emit(ChangeToggleState());
  }

  final CreateLeadRepo _repo;

  final nameController = TextEditingController();
  final emailController = TextEditingController();
  final phoneController = TextEditingController();
  final genderController = TextEditingController();
  final countryController = TextEditingController();
  final companyNameController = TextEditingController();
  final companyAddressController = TextEditingController();
  final companyPhoneController = TextEditingController();
  final notesController = TextEditingController();
  File? companyLogo;
  File? profileImage;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final ImagePicker picker = ImagePicker();

  Future<void> pickCompanyLogo() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      companyLogo = File(pickedFile.path);
    }
    emit(PickCompanyImage());
  }

  Future<void> pickProfileImage() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      profileImage = File(pickedFile.path);
    }
    emit(PickClientImage());
  }

  LeadItem? leadDetailsModel;
  Future<void> createLead() async {
    showLoading();
    emit(CreateLeadLoading());

    final result = await _repo.createLead(leadData: {
      "name": nameController.text,
      "email": emailController.text,
      "phone": phoneController.text,
      "gender": genderController.text,
      "country": countryController.text,
      "type": "lead",
    });

    result.when(success: (data) {
      leadDetailsModel = data;
      hideLoading();
      emit(CreateLeadSuccess());
    }, failure: (error) {
      hideLoading();

      emit(CreateLeadFailure());
    });
  }

  Future<void> updateClient() async {
    emit(UpdateClientLoading());
    showLoading();
    final data = FormData.fromMap({
      "_method": "put",
      'name': nameController.text,
      'email': emailController.text,
      'phone': phoneController.text,
      'gender': genderController.text,
      'country': countryController.text,
      'employee_id': "",
      'type': "lead",
      'company_name': companyNameController.text,
      'company_address': companyAddressController.text,
      'company_phone': companyPhoneController.text,
      'notes': notesController.text,
      if (companyLogo != null)
        'company_logo': await MultipartFile.fromFile(
          companyLogo!.path,
          filename: 'company_logo.jpg',
        ),
      if (companyLogo != null)
        'image': await MultipartFile.fromFile(
          companyLogo!.path,
          filename: 'image.jpg',
        ),
    });
    final result = await _repo.updateClient(
      clientId: leadDetailsModel!.id,
      data: data,
    );

    result.when(
      success: (data) {
        hideLoading();
        emit(UpdateClientSuccess());
      },
      failure: (error) {
        hideLoading();

        emit(UpdateClientFailure());
      },
    );
  }

  @override
  Future<void> close() {
    nameController.dispose();
    emailController.dispose();
    phoneController.dispose();
    genderController.dispose();
    countryController.dispose();
    companyNameController.dispose();
    companyAddressController.dispose();
    companyPhoneController.dispose();
    notesController.dispose();

    companyLogo = null;
    profileImage = null;

    return super.close();
  }
}
