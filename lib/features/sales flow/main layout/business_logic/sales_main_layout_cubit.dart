import 'package:erp/features/sales%20flow/main%20layout/business_logic/sales_main_layout_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:erp/core/utils/app_constants.dart';

class SalesMainLayoutCubit extends Cubit<SalesMainLayoutState> {
  SalesMainLayoutCubit() : super(MainLayoutInitial());

  static SalesMainLayoutCubit get(context) => BlocProvider.of(context);
  void changeBottomNavBar(index) {
    AppConstants.mainLayoutInitialScreenIndex = index;
    emit(AppBottomNavState(AppConstants.mainLayoutInitialScreenIndex)); 
  }
}
 