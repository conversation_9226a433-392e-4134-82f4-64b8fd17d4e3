import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/routing/app_router.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/features/sales%20flow/main%20layout/business_logic/sales_main_layout_cubit.dart';
import 'package:erp/features/sales%20flow/main%20layout/business_logic/sales_main_layout_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SalesMainLayoutScreen extends StatelessWidget {
  const SalesMainLayoutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SalesMainLayoutCubit, SalesMainLayoutState>(
      builder: (BuildContext context, state) {
        return Scaffold(
          backgroundColor: Colors.white,
          body: AppRouter()
              .salesScreens[AppConstants.mainLayoutInitialScreenIndex],
          bottomNavigationBar: BottomNavigationBar(
            currentIndex: AppConstants.mainLayoutInitialScreenIndex,
            selectedItemColor: AppColors.primaryColor900,
            unselectedItemColor: AppColors.neutralColor1200,
            selectedLabelStyle: Styles.contentRegular,
            unselectedLabelStyle: Styles.contentRegular,
            showSelectedLabels: true,
            showUnselectedLabels: true,
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.white,
            items: [
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  Assets.assetsImagesSvgsHomeIcon,
                  fit: BoxFit.scaleDown,
                  width: 24.sp,
                  height: 24.sp,
                  colorFilter: ColorFilter.mode(
                    AppConstants.mainLayoutInitialScreenIndex == 0
                        ? AppColors.primaryColor900
                        : AppColors.neutralColor1200,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'mainLayout.home'.tr(),
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  Assets.assetsImagesSvgsLeadIcon,
                  fit: BoxFit.scaleDown,
                  width: 24.sp,
                  height: 24.sp,
                  colorFilter: ColorFilter.mode(
                    AppConstants.mainLayoutInitialScreenIndex == 1
                        ? AppColors.primaryColor900
                        : AppColors.neutralColor1200,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'mainLayout.leads'.tr(),
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  Assets.assetsImagesSvgsClientsIcon,
                  fit: BoxFit.scaleDown,
                  width: 24.sp,
                  height: 24.sp,
                  colorFilter: ColorFilter.mode(
                    AppConstants.mainLayoutInitialScreenIndex == 2
                        ? AppColors.primaryColor900
                        : AppColors.neutralColor1200,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'mainLayout.clients'.tr(),
              ),
              BottomNavigationBarItem(
                icon: SvgPicture.asset(
                  Assets.assetsImagesSvgsMore,
                  width: 24.sp,
                  height: 24.sp,
                  fit: BoxFit.scaleDown,
                  colorFilter: ColorFilter.mode(
                    AppConstants.mainLayoutInitialScreenIndex == 3
                        ? AppColors.primaryColor900
                        : AppColors.neutralColor1200,
                    BlendMode.srcIn,
                  ),
                ),
                label: 'mainLayout.more'.tr(),
              ),
            ],
            onTap: (index) {
              SalesMainLayoutCubit.get(context).changeBottomNavBar(index);
            },
          ),
        );
      },
    );
  }
}
