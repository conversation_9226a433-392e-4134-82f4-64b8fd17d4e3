// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:erp/core/themes/hex_colors.dart';

// class ContainerInTopOfBottomNavBarWidget extends StatelessWidget {
//   const ContainerInTopOfBottomNavBarWidget({
//     super.key,
//   });

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 20.h,
//       width: double.infinity,
//       transform: Matrix4.translationValues(0.0, -5.sp, 0.0),
//       decoration: BoxDecoration(
//         color: hexToColor('#f6e2e4'),
//         borderRadius: BorderRadius.only(
//           topLeft: Radius.circular(40.r),
//           topRight: Radius.circular(40.r),
//         ),
//       ),
//     );
//   }
// }
