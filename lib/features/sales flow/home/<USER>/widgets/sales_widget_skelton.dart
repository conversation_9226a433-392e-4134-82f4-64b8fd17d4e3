import 'package:carousel_slider/carousel_slider.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

List<String> imageUrls = [
  Assets.assetsImagesSvgsHomeBanners,
  Assets.assetsImagesSvgsHomeBanners,
  Assets.assetsImagesSvgsHomeBanners,
  Assets.assetsImagesSvgsHomeBanners,
  Assets.assetsImagesSvgsHomeBanners,
];

class SalesBannersWidgetSkelton extends StatelessWidget {
  const SalesBannersWidgetSkelton({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CarouselSlider(
            items: imageUrls.map((image) {
              return Container(
                clipBehavior: Clip.antiAliasWithSaveLayer,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  image: DecorationImage(
                    isAntiAlias: true,
                    opacity: 0.9,
                    fit: BoxFit.fill,
                    image: AssetImage(image),
                  ),
                ),
              );
            }).toList(),
            options: CarouselOptions(
              autoPlay: true,
              autoPlayInterval: const Duration(seconds: 5),
              height: 150.h,
              padEnds: true,
              enableInfiniteScroll: true,
              enlargeCenterPage: true,
              viewportFraction: 1,
              initialPage: 0,
              onPageChanged: (index, reason) {},
            ),
          ),
          SizedBox(height: 12.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(imageUrls.length, (index) {
              return AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: EdgeInsets.symmetric(horizontal: 4.w),
                height: 4.w,
                width: 25.w,
                decoration: BoxDecoration(
                  color: AppColors.primaryColor800,
                  borderRadius: BorderRadius.circular(5.r),
                ),
              );
            }),
          )
        ],
      ),
    );
  }
}
