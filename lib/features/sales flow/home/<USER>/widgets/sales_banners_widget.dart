import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/sales%20flow/home/<USER>/cubit/sales_home_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SalesBannersWidget extends StatelessWidget {
  const SalesBannersWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final homeCubit = context.read<SalesHomeCubit>();
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        CarouselSlider(
          items: homeCubit.bannersResponse!.data.map((image) {
            return Container(
              clipBehavior: Clip.antiAliasWithSaveLayer,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.r),
                image: DecorationImage(
                    isAntiAlias: true,
                    opacity: 0.9,
                    fit: BoxFit.fill,
                    image: CachedNetworkImageProvider(image.background)),
              ),
            );
          }).toList(),
          options: CarouselOptions(
            autoPlay: true,
            autoPlayInterval: const Duration(seconds: 5),
            height: 150.h,
            padEnds: true,
            enableInfiniteScroll: true,
            enlargeCenterPage: true,
            viewportFraction: 1,
            initialPage: 0,
            onPageChanged: (index, reason) {
              context.read<SalesHomeCubit>().changePage(index);
            },
          ),
        ),
        SizedBox(height: 12.h),
        BlocBuilder<SalesHomeCubit, SalesHomeState>(
          buildWhen: (previous, current) => current is ChangePageState,
          builder: (context, state) {
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(homeCubit.bannersResponse!.data.length,
                    (index) {
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: EdgeInsets.symmetric(horizontal: 4.w),
                    height: 4.w,
                    width: 25.w,
                    decoration: BoxDecoration(
                      color:
                          context.read<SalesHomeCubit>().currentIndex == index
                              ? AppColors.primaryColor800
                              : AppColors.primaryColor100,
                      borderRadius: BorderRadius.circular(5.r),
                    ),
                  );
                }),
              ),
            );
          },
        ),
      ],
    );
  }
}
