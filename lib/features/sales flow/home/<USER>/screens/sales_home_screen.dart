import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/custom_make_attendance_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/home_top_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/statistics_item_widget.dart';
import 'package:erp/features/sales%20flow/home/<USER>/cubit/sales_home_cubit.dart';
import 'package:erp/features/sales%20flow/home/<USER>/widgets/sales_banners_widget.dart';
import 'package:erp/features/sales%20flow/home/<USER>/widgets/sales_widget_skelton.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SalesHomeScreen extends StatelessWidget {
  const SalesHomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final salesHomeCubit = context.read<SalesHomeCubit>();
    return Scaffold(
      body: BlocBuilder<SalesHomeCubit, SalesHomeState>(
        buildWhen: (previous, current) =>
            current is GetHomeErrorState ||
            current is GetHomeLoadingState ||
            current is GetHomeSuccessState,
        builder: (context, state) {
          if (state is GetHomeLoadingState) {
            return SalesHomeScreenSketon();
          }
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              HomeTopWidget(),
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(16.sp),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SalesBannersWidget(),
                        16.verticalSpace,
                        CustomMakeAttendanceWidget(),
                        16.verticalSpace,
                        Text(
                          'home.Statistics'.tr(),
                          style: Styles.contentBold.copyWith(
                              fontSize: 18.sp, fontWeight: FontWeight.w500),
                        ),
                        12.verticalSpace,
                        Row(
                          children: [
                            StatisticsItemWidget(
                              title: 'mainLayout.leads'.tr(),
                              value: salesHomeCubit.homeCount.toString(),
                            ),
                            Expanded(child: SizedBox.shrink())
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              )
            ],
          );
        },
      ),
    );
  }
}

class SalesHomeScreenSketon extends StatelessWidget {
  const SalesHomeScreenSketon({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
          body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          HomeTopWidget(),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.all(16.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SalesBannersWidgetSkelton(),
                    16.verticalSpace,
                    CustomMakeAttendanceWidget(),
                    16.verticalSpace,
                    Text(
                      "Statistics",
                      style: Styles.contentBold.copyWith(
                          fontSize: 18.sp, fontWeight: FontWeight.w500),
                    ),
                    12.verticalSpace,
                    Row(
                      children: [
                        StatisticsItemWidget(
                          title: 'Leads',
                          value: "10",
                        ),
                        Expanded(child: SizedBox.shrink())
                      ],
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      )),
    );
  }
}
