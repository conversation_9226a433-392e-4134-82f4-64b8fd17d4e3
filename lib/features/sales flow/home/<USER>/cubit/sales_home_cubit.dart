import 'package:erp/features/emplyee%20flow/home/<USER>/model/banners_model.dart';
import 'package:erp/features/sales%20flow/home/<USER>/repo/sales_repo.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'sales_home_state.dart';

class SalesHomeCubit extends Cubit<SalesHomeState> {
  SalesHomeCubit(this._salesRepo) : super(HomeInitial());

  final SalesRepo _salesRepo;
  int? homeCount;
  void getSealesCount() async {
    emit(GetHomeLoadingState());
    final result = await _salesRepo.getsalesHomeCount();
    result.when(
        failure: (failure) => emit(GetHomeErrorState()),
        success: (data) async {
          homeCount = data;
          await getBanners();
          emit(GetHomeSuccessState());
        });
  }

  int currentIndex = 0;
  void changePage(int index) {
    currentIndex = index;
    emit(ChangePageState());
  }

  BannersResponse? bannersResponse;
  Future<void> getBanners() async {
    final result = await _salesRepo.getBanners();
    result.when(
        success: (data) async {
          bannersResponse = data;
        },
        failure: (errorHandler) {});
  }
}
