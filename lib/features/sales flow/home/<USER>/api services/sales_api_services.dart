import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class SalesApiServices {
  final DioHelper _dioFactory;

  SalesApiServices(this._dioFactory);

  Future<Response?> getsalesHomeCount() async {
    return _dioFactory.get(
      endPoint: EndPoints.salesHomeCount,
    );
  }
   Future<Response?> getBanners() async {
    return _dioFactory.get(
      endPoint: EndPoints.getBannersHome,
    );
  }
}
