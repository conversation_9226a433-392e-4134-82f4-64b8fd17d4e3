import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/model/banners_model.dart';
import 'package:erp/features/sales%20flow/home/<USER>/api%20services/sales_api_services.dart';

class SalesRepo {
  final SalesApiServices _leadDetailsApiServices;

  SalesRepo(this._leadDetailsApiServices);

  Future<ApiResult<int>> getsalesHomeCount() async {
    final response = await _leadDetailsApiServices.getsalesHomeCount();

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(response.data['data']);
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }

  Future<ApiResult<BannersResponse>> getBanners() async {
    final response = await _leadDetailsApiServices.getBanners();
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        BannersResponse model = BannersResponse.fromJson(response.data);

        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }

    return ApiResult.failure(
        FailureException(errMessage: 'Unexpected error occurred'));
  }
}
