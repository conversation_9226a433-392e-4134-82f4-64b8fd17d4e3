import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/data/api_services/api_services.dart';

class ProjectsSalesRepo {
  final ProjectsApiSalesServices projectsApisalesServices;

  ProjectsSalesRepo(this.projectsApisalesServices);

  /// get show project
  Future<ApiResult<ProjectDetailsResponse>> getShowProject(
      {required int id}) async {
    final response = await projectsApisalesServices.getShowProject(id);
    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        final model = ProjectDetailsResponse.fromJson(response.data);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  /// add quote
  Future<ApiResult<ProjectDetailsResponse>> addquote({
    required int projectId,
    required File file,
    required String name,
  }) async {
    final response = await projectsApisalesServices.addquote(
      name: name,
      projectId: projectId,
      file: file,
    );
    if (response != null && response.statusCode == 200) {
      return ApiResult.success(
        ProjectDetailsResponse.fromJson(response.data),
      );
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(
          response?.statusCode,
          response,
        ),
      );
    }
  }

  /// post add invoice
  Future<ApiResult<ProjectInvoice>> addInvoice(
      {required String name,
      required int quantity,
      required int price,
      required int vat,
      required int total,
      required String dueDate,
      required int projectId,
      required String clientVat,
      required String status}) async {
    final response = await projectsApisalesServices.addinvoice(
      name: name,
      quantity: quantity,
      price: price,
      vat: vat,
      total: total,
      dueDate: dueDate,
      projectId: projectId,
      clientVat: clientVat,
      status: status,
    );
    if (response != null && response.statusCode == 200) {
      return ApiResult.success(ProjectInvoice.fromJson(response.data["data"]));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(
          response?.statusCode,
          response,
        ),
      );
    }
  }

  /// create project
  Future<ApiResult<String>> createProject({
    required String name,
    required int clientId,
    required String startdate,
    required String deadline,
    required List<int> employees,
    required List<int> departments,
    required String notes,
    required String details,
    required List<MultipartFile> files,
  }) async {
    final response = await projectsApisalesServices.createProjects(
      name: name,
      clientId: clientId,
      startdate: startdate,
      deadline: deadline,
      employees: employees,
      departments: departments,
      notes: notes,
      details: details,
      files: files,
    );
    if (response != null && response.statusCode == 200) {
      return ApiResult.success("Project created successfully");
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(
          response?.statusCode,
          response,
        ),
      );
    }
  }

  /// update project
  Future<ApiResult<String>> updateProject({
    required int id,
    required String name,
    required int clientId,
    required String method,
    required String status,
  }) async {
    final response = await projectsApisalesServices.updateProject(
      id: id,
      name: name,
      clientId: clientId,
      method: method,
      status: status,
    );
    if (response != null && response.statusCode == 200) {
      return ApiResult.success("Project updated successfully");
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(
          response?.statusCode,
          response,
        ),
      );
    }
  }

  /// delete project
  Future<ApiResult<String>> deleteProject({required int id}) async {
    final response = await projectsApisalesServices.deleteProject(id);
    if (response != null && response.statusCode == 200) {
      return ApiResult.success("Project deleted successfully");
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(
          response?.statusCode,
          response,
        ),
      );
    }
  }
}
