import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class ProjectsApiSalesServices {
  final DioHelper _dioFactory;

  ProjectsApiSalesServices(this._dioFactory);

  /// get show project
  Future<Response?> getShowProject(int id) async {
    return _dioFactory.get(
      endPoint: EndPoints.getShowProject(id),
    );
  }

  /// post add quote
  Future<Response?> addquote({
    required int projectId,
    required File file,
    required String name,
  }) async {
    final formData = FormData.fromMap(
      {
        'project_id': projectId,
        'name': name,
        'file': await MultipartFile.fromFile(
          file.path,
          filename: file.path.split('/').last,
        ),
      },
    );
    return _dioFactory.post(
      endPoint: EndPoints.addQuote,
      data: formData,
    );
  }

  /// post add invoice
  Future<Response?> addinvoice({
    required String name,
    required int quantity,
    required int price,
    required int vat,
    required int total,
    required String dueDate,
    required int projectId,
    required String status,
    required String clientVat,
  }) async {
    final formData = FormData.fromMap(
      {
        'name': name,
        'quantity': quantity,
        'price': price,
        'vat': vat,
        'total': total,
        'due_date': dueDate,
        'project_id': projectId,
        // 'client_vat': clientVat,
        'status': status,
      },
    );
    return _dioFactory.post(
      endPoint: EndPoints.addInvoice,
      data: formData,
    );
  }

  /// post create projects
  Future<Response?> createProjects({
    required String name,
    required int clientId,
    required String startdate,
    required String deadline,
    required List<int> employees,
    required List<int> departments,
    required String notes,
    required String details,
    required List<MultipartFile> files,
  }) async {
    final formData = FormData.fromMap({
      'name': name,
      'client_id': clientId,
      'start_date': startdate,
      'deadline': deadline,
      'notes': notes,
      'details': details,
      'employees[]': employees,
      'departments[]': departments,
      'files[]': files,
    });

    return _dioFactory.post(
      endPoint: EndPoints.createProject,
      data: formData,
    );
  }

  /// post update project
  Future<Response?> updateProject({
    required int id,
    required String name,
    required int clientId,
    required String method,
    required String status,
  }) async {
    final formData = FormData.fromMap({
      'name': name,
      'client_id': clientId,
      '_method': 'put',
      'status': 'in_progress',
    });
    return _dioFactory.post(
      endPoint: EndPoints.updateProject(id),
      data: formData,
    );
  }

  /// post destroy project
  Future<Response?> deleteProject(int id) async {
    final data = FormData.fromMap({
      '_method': 'delete',
    });
    return _dioFactory.post(
      endPoint: EndPoints.deleteProject(id),
      data: data,
    );
  }
}
