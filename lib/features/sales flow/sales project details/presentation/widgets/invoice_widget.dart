import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/helper_functions/download_function.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/assets.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/quote_sales_widget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';

class InvoiceWidget extends StatelessWidget {
  const InvoiceWidget({
    super.key,
    required this.projectDetails,
  });

  final SalesProjectDetailsCubit projectDetails;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SalesProjectDetailsCubit, SalesProjectDetailsState>(
      buildWhen: (previous, current) => current is AddInvoiceSuccess,
      builder: (context, state) {
        if (projectDetails.model!.data.projectInvoices!.isEmpty) {
          return NoProjectWidget(
            title: 'clientsDetails.noProjectsFound'.tr(),
            description: 'clientsDetails.toIssueAnInvoice'.tr(),
            buttomText: 'clientsDetails.addInvoice'.tr(),
            onPressed: () {
              context.pushNamed(Routes.addInvoiceScreen,
                  arguments: projectDetails);
            },
          );
        } else {
          return Column(
            children: [
              Expanded(
                child: ListView.separated(
                  padding: EdgeInsets.symmetric(
                    horizontal: 18.sp,
                    vertical: 32.sp,
                  ),
                  itemCount: projectDetails.model!.data.projectInvoices!.length,
                  separatorBuilder: (BuildContext context, int index) {
                    return 20.verticalSpace;
                  },
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.sp,
                        vertical: 8.sp,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.sp),
                        border: Border.all(
                          width: 1.sp,
                          color: AppColors.neutralColor600,
                        ),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(12.sp),
                            child: SvgPicture.asset(
                              Assets.assetsImagesSvgsPdfIcon,
                              height: 36.sp,
                              width: 36.sp,
                              fit: BoxFit.cover,
                            ),
                          ),
                          SizedBox(width: 10.sp),
                          Expanded(
                            child: Text(
                              projectDetails
                                  .model!.data.projectInvoices![index].name!,
                              style: Styles.contentEmphasis.copyWith(
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                          ),
                          if (projectDetails.model!.data.projectInvoices![index]
                                      .url !=
                                  null &&
                              projectDetails.model!.data.projectInvoices![index]
                                      .url !=
                                  "")
                            InkWell(
                              onTap: () async {
                                await downloadPdfFile(
                                    projectDetails.model!.data
                                        .projectInvoices![index].url!,
                                    Uri.parse(projectDetails.model!.data
                                            .projectInvoices![index].url!)
                                        .pathSegments
                                        .last);
                              },
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.download_outlined,
                                    size: 20.sp,
                                    color: AppColors.primaryColor800,
                                  ),
                                  Text(
                                    'clientsDetails.download'.tr(),
                                    style: Styles.contentEmphasis.copyWith(
                                      fontWeight: FontWeight.w400,
                                      color: AppColors.primaryColor800,
                                      fontSize: 12.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
              CustomButtonWidget(
                text: 'clientsDetails.addInvoice'.tr(),
                onPressed: () {
                  context.pushNamed(Routes.addInvoiceScreen,
                      arguments: projectDetails);
                },
              ),
              10.verticalSpace,
            ],
          );
        }
      },
    );
  }
}
