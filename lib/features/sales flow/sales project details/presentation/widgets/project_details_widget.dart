import 'package:easy_localization/easy_localization.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_wiget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ProductDetails extends StatelessWidget {
  const ProductDetails({
    super.key,
    required this.projectDetails,
  });

  final SalesProjectDetailsCubit projectDetails;

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ProjectCard(
            projectInfo: [
              {
                "title": 'clientsDetails.projectName'.tr(),
                "value": projectDetails.model!.data.name
              },
              {
                "title":'clientsDetails.dateofreceipt'.tr(),
                "value": projectDetails.model!.data.startDate,
              },
              {
                "title": 'clientsDetails.projectclosing'.tr(),
                "value": projectDetails.model!.data.deadline,
              },
            ],
            details: projectDetails.model!.data.details ?? "",
            status: projectDetails.model!.data.status,
            isCompleted: projectDetails.model!.data.status != "pending",
            files: projectDetails.model!.data.files,
          ),
        ],
      ),
    );
  }
}
