import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_state.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/widgets/invoice_widget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/widgets/project_details_widget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/presentation/widgets/qoute_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class SalesProjectDetails extends StatelessWidget {
  const SalesProjectDetails({super.key});

  @override
  Widget build(BuildContext context) {
    final projectDetails = context.read<SalesProjectDetailsCubit>();

    return BlocBuilder<SalesProjectDetailsCubit, SalesProjectDetailsState>(
      buildWhen: (previous, current) =>
          current is GetShowProjectLoading ||
          current is GetShowProjectSuccess ||
          current is GetShowProjectError,
      builder: (context, state) {
        if (state is GetShowProjectLoading) {
          return Skeletonizer(
            enabled: true,
            child: Scaffold(
              appBar: AppBar(
                title: const Text("Project Name"),
              ),
              body: const Center(child: CircularProgressIndicator()),
            ),
          );
        }
        return DefaultTabController(
          length: 3,
          child: Scaffold(
            appBar: AppBar(
              title: Text(projectDetails.model!.data.name),
              bottom: TabBar(
                dividerHeight: 0.h,
                labelColor: AppColors.primaryColor800,
                unselectedLabelColor: Colors.grey,
                indicatorColor: AppColors.primaryColor800,
                tabs: [
                  Tab(text: 'clientsDetails.projectsDetails'.tr()),
                  Tab(text: 'clientsDetails.quote'.tr()),
                  Tab(text: 'clientsDetails.invoice'.tr()),
                ],
              ),
            ),
            body: TabBarView(
              children: [
                ProductDetails(projectDetails: projectDetails),
                QuatesWidget(projectDetails: projectDetails),
                InvoiceWidget(projectDetails: projectDetails),
              ],
            ),
          ),
        );
      },
    );
  }
}
