import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AddQuoteScreen extends StatelessWidget {
  const AddQuoteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final projectDetails = context.read<SalesProjectDetailsCubit>();
    projectDetails.quoteNameController.clear();
    projectDetails.quotePdfFile = null;
    return Scaffold(
      appBar: AppBar(
        title: Text('clientsDetails.addQuote'.tr()),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'clientsDetails.quoteName'.tr(),
              style: Styles.contentEmphasis,
            ),
            CustomTextFormFieldWidget(
              controller: projectDetails.quoteNameController,
              hintText: 'clientsDetails.enterQuoteName'.tr(),
              // labelText: "Name",
            ),
            const SizedBox(height: 16),
            Text(
              'clientsDetails.upLoadPDF'.tr(),
              style: Styles.contentEmphasis,
            ),
            const SizedBox(height: 8),
            BlocBuilder<SalesProjectDetailsCubit, SalesProjectDetailsState>(
              buildWhen: (previous, current) => current is PickquotePdf,
              builder: (context, state) {
                return InkWell(
                  onTap: () => projectDetails.pickPdfFile(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 12),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade400),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.attach_file, color: Colors.blue),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Text(
                            projectDetails.quotePdfFile?.path.split('/').last ??
                                'clientsDetails.noFileSelected'.tr(),
                            style: TextStyle(
                              color: projectDetails.quotePdfFile != null
                                  ? Colors.black
                                  : Colors.grey,
                            ),
                          ),
                        ),
                        const Icon(Icons.upload_file, color: Colors.grey),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 30),
            BlocListener<SalesProjectDetailsCubit, SalesProjectDetailsState>(
              listener: (context, state) {
                if (state is AddQuoteSuccess) {
                  customToast(
                      msg: 'clientsDetails.quoteAddedSuccessfully'.tr(),
                      color: AppColors.greenColor200);
                  Navigator.pop(context);
                } else if (state is AddQuoteFailure) {
                  customToast(
                      msg: 'clientsDetails.failedToAddQuote'.tr(),
                      color: AppColors.redColor200);
                }
              },
              child: CustomButtonWidget(
                text: 'clientsDetails.addQuote'.tr(),
                onPressed: () {
                  if (projectDetails.quotePdfFile == null ||
                      projectDetails.quoteNameController.text.isEmpty) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('clientsDetails.enterNameAndSelect'.tr()),
                      ),
                    );
                    return;
                  }

                  projectDetails.addQuote();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
