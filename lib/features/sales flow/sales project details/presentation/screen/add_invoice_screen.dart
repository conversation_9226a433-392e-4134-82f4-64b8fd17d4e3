import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_cubit.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddInvoiceScreen extends StatelessWidget {
  const AddInvoiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<SalesProjectDetailsCubit>();
    final formKey = GlobalKey<FormState>();

    cubit.clearControllers();
    return Scaffold(
      appBar: AppBar(
        title: Text('clientsDetails.addInvoice'.tr()),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.sp),
        child: Form(
          key: formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'clientsDetails.invoiceName'.tr(),
                style: Styles.contentEmphasis,
              ),
              CustomTextFormFieldWidget(
                controller: cubit.invoiceNameController,
                hintText: 'clientsDetails.enterInvoiceName'.tr(),
                keyboardType: TextInputType.text,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Invoice name is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.quantity'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceQuantityController,
                hintText: 'clientsDetails.enterQuantity'.tr(),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Quantity is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.price'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoicePriceController,
                hintText: 'clientsDetails.enterPrice'.tr(),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Price is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.vat'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceVatController,
                hintText: 'clientsDetails.enterVat'.tr(),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'VAT is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.invoicetotal'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceTotalController,
                hintText: 'clientsDetails.enterTotal'.tr(),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Total is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.dueDate'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceDueDateController,
                hintText: 'clientsDetails.enterDueDate'.tr(),
                readOnly: true,
                onTap: () {
                  showDatePicker(
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime.now(),
                    lastDate: DateTime.now().add(const Duration(days: 365)),
                  ).then((value) {
                    if (value != null) {
                      cubit.invoiceDueDateController.text =
                          DateFormat('yyyy-MM-dd').format(value);
                    }
                  });
                },
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Due date is required';
                  }
                  return null;
                },
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.clientVat'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceClientVatController,
                hintText: 'clientsDetails.enterClientVat'.tr(),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Client VAT is required';
                  }
                  return null;
                },
              ),
              Text(
                'clientsDetails.total'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.totalController,
                hintText: 'clientsDetails.entertotal'.tr(),
                keyboardType: TextInputType.number,
              ),
              10.verticalSpace,
              Text(
                'clientsDetails.Status'.tr(),
                style: Styles.contentEmphasis,
              ),
              10.verticalSpace,
              CustomTextFormFieldWidget(
                controller: cubit.invoiceStatusController,
                hintText: 'clientsDetails.enterStatus'.tr(),
                validator: (value) {
                  if (value!.isEmpty) {
                    return 'Status is required';
                  }
                  return null;
                },
              ),
              // const SizedBox(height: 30),
            ],
          ),
        ),
      ),
      bottomNavigationBar:
          BlocListener<SalesProjectDetailsCubit, SalesProjectDetailsState>(
        listenWhen: (previous, current) => current is AddInvoiceSuccess,
        listener: (context, state) {
          if (state is AddInvoiceSuccess) {
            customToast(
              msg: 'clientsDetails.invoiceAddedSuccessfully'.tr(),
              color: AppColors.greenColor200,
            );
            Navigator.pop(context);
          }
        },
        child: CustomButtonWidget(
          margin: EdgeInsets.all(16.sp),
          text: 'clientsDetails.submitInvoice'.tr(),
          onPressed: () {
            if (formKey.currentState!.validate()) {
              cubit.addInvoice();
            }
          },
        ),
      ),
    );
  }
}
