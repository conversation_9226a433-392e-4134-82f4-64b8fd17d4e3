abstract class SalesProjectDetailsState {}

class GetListProjectSalesInitial extends SalesProjectDetailsState {}

/// get show project
class GetShowProjectLoading extends SalesProjectDetailsState {}

class GetShowProjectSuccess extends SalesProjectDetailsState {}

class GetShowProjectError extends SalesProjectDetailsState {}

/// add quote
class AddQuoteLoading extends SalesProjectDetailsState {}

class AddQuoteSuccess extends SalesProjectDetailsState {}

class AddQuoteFailure extends SalesProjectDetailsState {}

/// add invoice

class AddInvoiceLoading extends SalesProjectDetailsState {}

class AddInvoiceSuccess extends SalesProjectDetailsState {}

class AddInvoiceFailure extends SalesProjectDetailsState {}




class PickquotePdf extends SalesProjectDetailsState {}
