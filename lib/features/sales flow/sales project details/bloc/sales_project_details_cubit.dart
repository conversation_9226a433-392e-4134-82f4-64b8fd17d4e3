import 'dart:io';

import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/features/emplyee%20flow/project%20details/data/models/project_details_response.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/bloc/sales_project_details_state.dart';
import 'package:erp/features/sales%20flow/sales%20project%20details/data/repo/create_projects_sales_repo.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SalesProjectDetailsCubit extends Cubit<SalesProjectDetailsState> {
  SalesProjectDetailsCubit(this._createProjectsSalesRepo)
      : super(GetListProjectSalesInitial());

  final ProjectsSalesRepo _createProjectsSalesRepo;

  ProjectDetailsResponse? model;

  Future<void> getShowProject(int id) async {
    emit(GetShowProjectLoading());
    final result = await _createProjectsSalesRepo.getShowProject(id: id);
    result.when(success: (data) {
      model = data;
      emit(
        GetShowProjectSuccess(),
      );
    }, failure: (error) {
      emit(GetShowProjectError());
    });
  }

  File? quotePdfFile;
  Future<void> pickPdfFile() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['pdf'],
    );

    if (result != null && result.files.single.path != null) {
      quotePdfFile = File(result.files.single.path!);
      emit(PickquotePdf());
    }
  }

  final quoteNameController = TextEditingController();
  Future<void> addQuote() async {
    emit(AddQuoteLoading());
    showLoading();
    final result = await _createProjectsSalesRepo.addquote(
        file: quotePdfFile!,
        name: quoteNameController.text,
        projectId: model!.data.id);
    result.when(success: (data) {
      model = data;
      hideLoading();
      emit(AddQuoteSuccess());
    }, failure: (error) {
      hideLoading();
      emit(
        AddQuoteFailure(),
      );
    });
  }

  final TextEditingController invoiceNameController = TextEditingController();
  final TextEditingController invoiceQuantityController =
      TextEditingController();
  final TextEditingController invoicePriceController = TextEditingController();
  final TextEditingController invoiceVatController = TextEditingController();
  final TextEditingController invoiceTotalController = TextEditingController();
  final TextEditingController invoiceDueDateController =
      TextEditingController();
  final TextEditingController invoiceClientVatController =
      TextEditingController();
  final TextEditingController invoiceStatusController = TextEditingController();
  final TextEditingController totalController = TextEditingController();

  void clearControllers() {
    invoiceNameController.clear();
    invoiceQuantityController.clear();
    invoicePriceController.clear();
    invoiceVatController.clear();
    invoiceTotalController.clear();
    invoiceDueDateController.clear();
    invoiceClientVatController.clear();
    invoiceStatusController.clear();
    totalController.clear();
  }

  Future<void> addInvoice() async {
    emit(AddInvoiceLoading());
    showLoading();

    final name = invoiceNameController.text.trim();
    final quantity = int.tryParse(invoiceQuantityController.text) ?? 0;
    final price = int.tryParse(invoicePriceController.text) ?? 0;
    final vat = int.tryParse(invoiceVatController.text) ?? 0;
    final clientVat = invoiceClientVatController.text.trim();
    final status = invoiceStatusController.text.trim();
    final dueDate = invoiceDueDateController.text.trim();

    final result = await _createProjectsSalesRepo.addInvoice(
      name: name,
      quantity: quantity,
      price: price,
      vat: vat,
      total: int.tryParse(totalController.text) ?? 0,
      dueDate: dueDate,
      projectId: model!.data.id,
      clientVat: clientVat,
      status: status,
    );

    result.when(
      success: (data) {
        hideLoading();
        model!.data.projectInvoices!.add(data);
        emit(AddInvoiceSuccess());
      },
      failure: (error) {
        hideLoading();
        emit(AddInvoiceFailure());
      },
    );
  }

  @override
  Future<void> close() {
    quoteNameController.dispose();

    invoiceClientVatController.dispose();

    invoiceStatusController.dispose();

    invoiceDueDateController.dispose();

    invoiceTotalController.dispose();

    invoicePriceController.dispose();

    invoiceQuantityController.dispose();

    invoiceVatController.dispose();

    totalController.dispose();

    invoiceNameController.dispose();

    return super.close();
  }
}
