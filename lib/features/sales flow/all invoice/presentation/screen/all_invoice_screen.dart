import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/loading_widget.dart';
import 'package:erp/features/sales flow/all invoice/bloc/cubit/all_invoice_cubit.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/model/invoice_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AllInvoiceScreen extends StatelessWidget {
  const AllInvoiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<AllInvoiceCubit>();

    return BlocBuilder<AllInvoiceCubit, AllInvoiceState>(
      builder: (context, state) {
        if (state is GetAllInvoiceLoadingState) {
          return const LoadingWidget();
        }

        final invoices = cubit.invoiceResponse?.data.data ?? [];

        return Scaffold(
          appBar: AppBar(
            title: Text('moreSales.invoices'.tr()),
          ),
          body: invoices.isEmpty
              ? Center(
                  child: Text('moreSales.noInvoices'.tr(),
                      style: const TextStyle(fontSize: 16)),
                )
              : Column(
                  children: [
                    Expanded(
                      child: ListView.separated(
                        controller: cubit.scrollController,
                        padding: const EdgeInsets.all(12),
                        itemCount: invoices.length,
                        separatorBuilder: (_, __) => const SizedBox(height: 10),
                        itemBuilder: (context, index) {
                          final invoice = invoices[index];
                          return InvoiceCard(invoice: invoice);
                        },
                      ),
                    ),
                    BlocBuilder<AllInvoiceCubit, AllInvoiceState>(
                      buildWhen: (previous, current) =>
                          current is GetAllInvoiceLoadingMoreState ||
                          current is GetAllInvoiceSucessState,
                      builder: (context, state) {
                        if (state is GetAllInvoiceLoadingMoreState) {
                          return const Center(
                              child: CircularProgressIndicator());
                        } else {
                          return const SizedBox.shrink();
                        }
                      },
                    ),
                  ],
                ),
        );
      },
    );
  }
}

class InvoiceCard extends StatelessWidget {
  final Invoice invoice;

  const InvoiceCard({super.key, required this.invoice});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shadowColor: AppColors.neutralColor300,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            Icon(
              Icons.receipt_long_rounded,
              size: 36.sp,
              color: AppColors.primaryColor900,
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    spacing: 10.sp,
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      Expanded(
                        child: Text(
                          invoice.name,
                          style: Styles.heading5.copyWith(
                              // color: AppColors.neutralColor1600,
                              ),
                        ),
                      ),
                      // Spacer(),
                      Text(
                        '${'moreSales.status'.tr()}: ${invoice.status}',
                        style: Styles.contentBold.copyWith(
                          color: invoice.status == "confirmed"
                              ? AppColors.greenColor200
                              : Colors.orange,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    '${'moreSales.dueDate'.tr()}: ${invoice.dueDate}',
                    style: Styles.contentRegular.copyWith(
                      color: AppColors.neutralColor600,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    "${invoice.total.toStringAsFixed(2)} ${'moreSales.currency'.tr()}",
                    style: Styles.heading5.copyWith(
                      color: AppColors.neutralColor600,
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
