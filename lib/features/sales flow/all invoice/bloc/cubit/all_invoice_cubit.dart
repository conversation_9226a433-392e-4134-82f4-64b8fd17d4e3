import 'package:erp/features/sales%20flow/all%20invoice/data/model/invoice_model.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/repo/all_invoice_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

part 'all_invoice_state.dart';

class AllInvoiceCubit extends Cubit<AllInvoiceState> {
  AllInvoiceCubit(this._allInvoiceRepo) : super(AllInvoiceInitial());
  final AllInvoiceRepo _allInvoiceRepo;
  InvoiceResponse? invoiceResponse;
  Future<void> getAllInvoice() async {
    emit(GetAllInvoiceLoadingState());
    final result = await _allInvoiceRepo.getAllInvoice(page: currentPage);
    result.when(
        success: (InvoiceResponse data) {
          invoiceResponse = data;
          currentPage = data.data.currentPage;
          lastPage = data.data.lastPage;
          emit(GetAllInvoiceSucessState());
        },
        failure: (errorHandler) => emit(GetAllInvoiceErrorState()));
  }

  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;

  final ScrollController scrollController = ScrollController();

  void setupScrollListener() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
              scrollController.position.maxScrollExtent - 100 &&
          !isLoadingMore) {
        loadMoreInvoices();
      }
    });
  }

  Future<void> loadMoreInvoices() async {
    if (currentPage >= lastPage) return;

    isLoadingMore = true;
    currentPage++;
    emit(GetAllInvoiceLoadingMoreState());
    final result = await _allInvoiceRepo.getAllInvoice(page: currentPage);
    result.when(success: (data) {
      invoiceResponse!.data.data.addAll(data.data.data);
      currentPage = data.data.currentPage;
      lastPage = data.data.lastPage;
      emit(GetAllInvoiceSucessState());
    }, failure: (error) {
      emit(GetAllInvoiceErrorMoreState());
    });

    isLoadingMore = false;
  }
}
