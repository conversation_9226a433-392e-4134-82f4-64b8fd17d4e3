import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/api%20services/all_invoice_api_services.dart';
import 'package:erp/features/sales%20flow/all%20invoice/data/model/invoice_model.dart';

class AllInvoiceRepo {
  final AllInvoiceApiServices _apiServices;

  AllInvoiceRepo(this._apiServices);

  Future<ApiResult<InvoiceResponse>> getAllInvoice({required int page}) async {
    final response = await _apiServices.getAllInvoice(page : page);

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(InvoiceResponse.fromJson(response.data));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }
}
