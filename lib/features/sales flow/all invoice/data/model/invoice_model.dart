// import 'package:json_annotation/json_annotation.dart';

// part 'invoice_model.g.dart';

// @JsonSerializable()
// class Invoice {
//   final int id;
//   final String name;
//   final int quantity;
//   final num price;
//   final num vat;
//   final num total;
//   @Json<PERSON>ey(name: 'due_date')
//   final String dueDate;
//   @Json<PERSON>ey(name: 'project_id')
//   final int projectId;
//   @JsonKey(name: 'client_vat')
//   final String? clientVat;
//   final String status;

//   Invoice({
//     required this.id,
//     required this.name,
//     required this.quantity,
//     required this.price,
//     required this.vat,
//     required this.total,
//     required this.dueDate,
//     required this.projectId,
//     this.clientVat,
//     required this.status,
//   });

//   factory Invoice.fromJson(Map<String, dynamic> json) => _$InvoiceFromJson(json);
//   Map<String, dynamic> toJson() => _$InvoiceToJson(this);
// }


// @JsonSerializable()
// class InvoiceResponse {
//   final List<Invoice> data;
//   final String status;
//   final String error;
//   final int code;

//   InvoiceResponse({
//     required this.data,
//     required this.status,
//     required this.error,
//     required this.code,
//   });

//   factory InvoiceResponse.fromJson(Map<String, dynamic> json) => _$InvoiceResponseFromJson(json);
//   Map<String, dynamic> toJson() => _$InvoiceResponseToJson(this);
// }
 
 import 'package:json_annotation/json_annotation.dart';

part 'invoice_model.g.dart';

@JsonSerializable()
class InvoiceResponse {
  final InvoicePaginatedData data;
  final String status;
  final String error;
  final int code;

  InvoiceResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory InvoiceResponse.fromJson(Map<String, dynamic> json) =>
      _$InvoiceResponseFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceResponseToJson(this);
}

@JsonSerializable()
class InvoicePaginatedData {
  @JsonKey(name: 'current_page')
  final int currentPage;

  final List<Invoice> data;

  @JsonKey(name: 'first_page_url')
  final String firstPageUrl;

  final int from;

  @JsonKey(name: 'last_page')
  final int lastPage;

  @JsonKey(name: 'last_page_url')
  final String lastPageUrl;

  final List<PageLink> links;

  @JsonKey(name: 'next_page_url')
  final String? nextPageUrl;

  final String path;

  @JsonKey(name: 'per_page')
  final int perPage;

  @JsonKey(name: 'prev_page_url')
  final String? prevPageUrl;

  final int to;
  final int total;

  InvoicePaginatedData({
    required this.currentPage,
    required this.data,
    required this.firstPageUrl,
    required this.from,
    required this.lastPage,
    required this.lastPageUrl,
    required this.links,
    this.nextPageUrl,
    required this.path,
    required this.perPage,
    this.prevPageUrl,
    required this.to,
    required this.total,
  });

  factory InvoicePaginatedData.fromJson(Map<String, dynamic> json) =>
      _$InvoicePaginatedDataFromJson(json);

  Map<String, dynamic> toJson() => _$InvoicePaginatedDataToJson(this);
}

@JsonSerializable()
class Invoice {
  final int id;
  final String name;
  final int quantity;
  final num price;
  final num vat;
  final num total;

  @JsonKey(name: 'due_date')
  final String dueDate;

  @JsonKey(name: 'project_id')
  final int projectId;

  @JsonKey(name: 'client_vat')
  final String? clientVat;

  final String status;
  final String url;

  Invoice({
    required this.id,
    required this.name,
    required this.quantity,
    required this.price,
    required this.vat,
    required this.total,
    required this.dueDate,
    required this.projectId,
    this.clientVat,
    required this.status,
    required this.url,
  });

  factory Invoice.fromJson(Map<String, dynamic> json) =>
      _$InvoiceFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceToJson(this);
}

@JsonSerializable()
class PageLink {
  final String? url;
  final String label;
  final bool active;

  PageLink({
    this.url,
    required this.label,
    required this.active,
  });

  factory PageLink.fromJson(Map<String, dynamic> json) =>
      _$PageLinkFromJson(json);

  Map<String, dynamic> toJson() => _$PageLinkToJson(this);
}
