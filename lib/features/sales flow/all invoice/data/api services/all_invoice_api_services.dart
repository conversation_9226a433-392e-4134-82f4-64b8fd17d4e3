import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class AllInvoiceApiServices {
  final DioHelper _dioFactory;

  AllInvoiceApiServices(this._dioFactory);

  Future<Response?> getAllInvoice({required int page}) async {
    return _dioFactory
        .get(endPoint: EndPoints.allInvoiceList, data: {"page": page});
  }
}
