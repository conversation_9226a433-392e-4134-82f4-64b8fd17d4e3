import 'package:json_annotation/json_annotation.dart';

part 'lead_model.g.dart';

@JsonSerializable()
class LeadResponse {
  Data data;
  String status;
  String error;
  int code;

  LeadResponse({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory LeadResponse.fromJson(Map<String, dynamic> json) =>
      _$LeadResponseFromJson(json);
  Map<String, dynamic> toJson() => _$LeadResponseToJson(this);
}

@JsonSerializable()
class Data {
  List<LeadItem> data;
  Meta meta;

  Data({
    required this.data,
    required this.meta,
  });

  factory Data.fromJson(Map<String, dynamic> json) => _$DataFromJson(json);
  Map<String, dynamic> toJson() => _$DataToJson(this);
}

@JsonSerializable()
class Meta {
  @JsonKey(name: "current_page")
  int currentPage;
  @Json<PERSON>ey(name: "last_page")
  int lastPage;
  @<PERSON>son<PERSON><PERSON>(name: "per_page")
  int perPage;
  int total;

  Meta({
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.total,
  });

  factory Meta.fromJson(Map<String, dynamic> json) => _$MetaFromJson(json);
  Map<String, dynamic> toJson() => _$MetaToJson(this);
}

@JsonSerializable()
class LeadItem {
  int id;

  String name;
  String email;
  String phone;
  String gender;
  String country;

  dynamic employee;
  String type;
  String image;

  LeadItem({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.gender,
    required this.country,
    this.employee,
    required this.type,
    required this.image,
  });

  factory LeadItem.fromJson(Map<String, dynamic> json) =>
      _$LeadItemFromJson(json);
  Map<String, dynamic> toJson() => _$LeadItemToJson(this);
}
