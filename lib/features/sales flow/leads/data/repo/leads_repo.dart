import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/features/sales%20flow/leads/data/api%20services/lead_api_services.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';

class LeadRepo {
  final LeadApiServices _leadApiServices;

  LeadRepo(this._leadApiServices);

  Future<ApiResult<LeadResponse>> getLeads(
      {String type = "lead", int page = 1}) async {
    final response = await _leadApiServices.getLeads(type: type, page: page);
    if (response != null && response.statusCode == 200) {
      return ApiResult.success(LeadResponse.fromJson(response.data));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }
}
