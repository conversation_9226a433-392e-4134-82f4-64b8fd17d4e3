import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class LeadApiServices {
  LeadApiServices(this._dioFactory);

  final DioHelper _dioFactory;

  Future<Response?> getLeads({String type = "lead", int page = 1}) async {
    return _dioFactory.get(endPoint: EndPoints.getLeads, data: {
      "type": type,
      "page": page,
    });
  }
}
