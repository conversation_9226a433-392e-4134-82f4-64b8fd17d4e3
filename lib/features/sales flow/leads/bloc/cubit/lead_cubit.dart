import 'package:erp/features/sales%20flow/leads/bloc/cubit/lead_state.dart';
import 'package:erp/features/sales%20flow/leads/data/model/lead_model.dart';
import 'package:erp/features/sales%20flow/leads/data/repo/leads_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class LeadsCubit extends Cubit<LeadsState> {
  final LeadRepo _leadRepo;

  LeadsCubit(this._leadRepo) : super(LeadsInitial()) {
    setupScrollController();
  }

  LeadResponse? leadResponse;
  int currentPage = 1;
  int lastPage = 1;
  bool isLoadingMore = false;
  final ScrollController scrollController = ScrollController();

  void setupScrollController() {
    scrollController.addListener(() {
      if (scrollController.position.pixels >=
          scrollController.position.maxScrollExtent - 100) {
        loadMoreLeads();
      }
    });
  }

  Future<void> getLeads() async {
    emit(LeadsLoading());
    currentPage = 1;

    final result = await _leadRepo.getLeads(page: currentPage);

    result.when(
      success: (data) {
        leadResponse = data;
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(LeadsSuccess());
      },
      failure: (error) => emit(LeadsFailure()),
    );
  }

  Future<void> loadMoreLeads() async {
    if (isLoadingMore || currentPage >= lastPage) return;

    isLoadingMore = true;
    emit(LeadsLoadingMore());

    final nextPage = currentPage + 1;
    final result = await _leadRepo.getLeads(page: nextPage);

    result.when(
      success: (data) {
        leadResponse!.data.data.addAll(data.data.data);
        currentPage = data.data.meta.currentPage;
        lastPage = data.data.meta.lastPage;
        emit(LeadsSuccess());
      },
      failure: (error) {
        emit(LeadsFailure());
      },
    );

    isLoadingMore = false;
  }

  void removeIndex(id) {
    leadResponse!.data.data.removeWhere((element) => element.id == id);
    emit(RemoveItemAfterDeleted());
  }

  @override
  Future<void> close() {
    scrollController.dispose();
    return super.close();
  }
}
