import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/leads/bloc/cubit/lead_cubit.dart';
import 'package:erp/features/sales%20flow/leads/bloc/cubit/lead_state.dart';
import 'package:erp/features/sales%20flow/leads/presention/widget/lead_item_widge.dart';
import 'package:erp/features/sales%20flow/leads/presention/widget/lead_skelton_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeadsScreen extends StatelessWidget {
  const LeadsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final leadsCubit = context.read<LeadsCubit>();
    return BlocBuilder<LeadsCubit, LeadsState>(
      buildWhen: (previous, current) =>
          current is LeadsLoading ||
          current is LeadsSuccess ||
          current is LeadsFailure,
      builder: (context, state) {
        if (state is LeadsLoading) {
          return LeadSkltonWidget();
        }
        return Scaffold(
          appBar: AppBar(
            elevation: 0,
            title: Text(
              'mainLayout.leads'.tr(),
              style:
                  TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
            ),
           
          ),
          body: BlocBuilder<LeadsCubit, LeadsState>(
            buildWhen: (previous, current) => current is RemoveItemAfterDeleted,
            builder: (context, state) {
              if (leadsCubit.leadResponse!.data.data.isEmpty) {
                return Center(
                  child: Text(
                    'leads.noLeadsFound'.tr(),
                    style: TextStyle(
                      color: AppColors.neutralColor600,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }
              return Column(
                children: [
                  Expanded(
                    child: ListView.separated(
                      controller: leadsCubit.scrollController,
                      itemCount: leadsCubit.leadResponse!.data.data.length,
                      separatorBuilder: (_, __) => Divider(
                        height: 1.sp,
                        color: AppColors.neutralColor600,
                      ),
                      itemBuilder: (context, index) {
                        return LeadItemWidget(
                            lead: leadsCubit.leadResponse!.data.data[index]);
                      },
                    ),
                  ),
                  BlocBuilder<LeadsCubit, LeadsState>(
                    buildWhen: (previous, current) =>
                        current is LeadsLoadingMore || current is LeadsSuccess,
                    builder: (context, state) {
                      if (state is LeadsLoadingMore) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      }
                      return SizedBox.fromSize();
                    },
                  )
                ],
              );
            },
          ),
          bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
            child: CustomButtonWidget(
              text: 'leads.addLead'.tr(),
              onPressed: () {
                context.pushNamed(Routes.addLeadScreen);
              },
            ),
          ),
        );
      },
    );
  }
}
