import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class LeadSkltonWidget extends StatelessWidget {
  const LeadSkltonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          title: const Text(
            'Leads',
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
          ),
        
        ),
        body: ListView.separated(
          itemCount: 10,
          separatorBuilder: (_, __) => const Divider(
            height: 1,
            color: AppColors.neutralColor600,
          ),
          itemBuilder: (context, index) {
            // final lead = leadsCubit.leadResponse!.data.data[index];
            return InkWell(
              onTap: () {
                context.pushNamed(Routes.leadDetails);
              },
              child: Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
                child: Row(
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.circular(8.sp),
                        child: Image.network(
                          'https://i.pravatar.cc/150?img=3',
                          height: 50.sp,
                          width: 50.sp,
                        )),
                    SizedBox(width: 12.sp),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "leadname",
                            style: Styles.highlightEmphasis,
                          ),
                          SizedBox(height: 4.sp),
                          Text(
                            "lead.email",
                            style: Styles.contentEmphasis
                                .copyWith(color: AppColors.neutralColor600),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 10.sp),
                    // Container(
                    //   padding: EdgeInsets.symmetric(
                    //       horizontal: 8.sp, vertical: 3.sp),
                    //   decoration: BoxDecoration(
                    //     color: Colors.red,
                    //     borderRadius: BorderRadius.circular(4.r),
                    //   ),
                    //   child: Text("success",
                    //       style: Styles.captionEmphasis
                    //           .copyWith(color: Colors.red)),
                    // ),
                    const Icon(Icons.chevron_right, color: Colors.grey),
                  ],
                ),
              ),
            );
          },
        ),
        bottomNavigationBar: SafeArea(
            minimum: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
            child: CustomButtonWidget(
              text: "Add Lead",
              onPressed: () {
                context.pushNamed(Routes.addLeadScreen);
              },
            )),
      ),
    );
  }
}
