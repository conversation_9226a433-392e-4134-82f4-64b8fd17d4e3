abstract class LeadDetailsState {}

class LeadDetailsInitial extends LeadDetailsState {}

class LeadDetailsLoading extends LeadDetailsState {}

class LeadDetailsSuccess extends LeadDetailsState {}

class LeadDetailsFailure extends LeadDetailsState {}

class GetLeadDetailsFailure extends LeadDetailsState {}

class UpdateClientLoading extends LeadDetailsState {}

class UpdateClientSuccess extends LeadDetailsState {}

class UpdateClientFailure extends LeadDetailsState {}

class DeleteClientLoading extends LeadDetailsState {}

class DeleteClientSuccess extends LeadDetailsState {}

class DeleteClientFailure extends LeadDetailsState {}

class PickCompanyImage extends LeadDetailsState {}

class PickClientImage extends LeadDetailsState {}

class UpdateAfterEditState extends LeadDetailsState {}

/// Quote
class AddQuoteLoading extends LeadDetailsState {}

class AddQuoteSuccess extends LeadDetailsState {}

class AddQuoteFailure extends LeadDetailsState {}

class PickquotePdf extends LeadDetailsState {}

/// get list projects
class GetListProjectSalesLoading extends LeadDetailsState {}

class GetListProjectSalesSuccess extends LeadDetailsState {}

class GetListProjectSalesError extends LeadDetailsState {}

class GetListProjectsSalesLoadingMore extends LeadDetailsState {}




class CreateProjectLoading extends LeadDetailsState {}

class CreateProjectSuccess extends LeadDetailsState {}

class CreateProjectFailure extends LeadDetailsState {}



class CreateProjectEmployeeSelected extends LeadDetailsState {}

class CreateProjectDepartmentSelected extends LeadDetailsState {}

class CreateProjectStartDateSet extends LeadDetailsState {}