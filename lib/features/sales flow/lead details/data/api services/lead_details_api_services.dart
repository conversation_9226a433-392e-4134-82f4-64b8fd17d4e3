import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/dio_helper/dio_helper.dart';
import 'package:erp/core/networks_helper/dio_helper/end_points.dart';

class LeadDetailsApiServices {
  final DioHelper _dioFactory;

  LeadDetailsApiServices(this._dioFactory);

  /// Get List of projects
  Future<Response?> getListProjects(
      {required int page, required List<int> clientId}) async {
    return _dioFactory.get(
      endPoint: EndPoints.getListProjects,
      data: {
        'page': page,
        'client_ids': clientId,
      },
    );
  }

  Future<Response?> getLeadDetails({required int leadId}) async {
    return _dioFactory.get(
      endPoint: "${EndPoints.getLeads}/$leadId",
    );
  }

  Future<Response?> updateClient({
    required int clientId,
    required data,
  }) async {
    return _dioFactory.post(
      endPoint: "${EndPoints.getLeads}/$clientId",
      data: data,
    );
  }

  Future<Response?> deleteClient({
    required int clientId,
  }) async {
    final data = FormData.fromMap({
      '_method': 'delete',
    });

    return _dioFactory.post(
      endPoint: "${EndPoints.getLeads}/$clientId",
      data: data,
    );
  }
    Future<Response?> createProjects({
    required String name,
    required int clientId,
    required String startdate,
    required String deadline,
    required List<int> employees,
    required List<int> departments,
    required String notes,
    required String details,
    required List<MultipartFile> files,
  }) async {
    final formData = FormData.fromMap({
      'name': name,
      'client_id': clientId,
      'start_date': startdate,
      'deadline': deadline,
      'notes': notes,
      'details': details,
      'employees[]': employees,
      'departments[]': departments,
      'files[]': files,
    });

    return _dioFactory.post(
      endPoint: EndPoints.createProject,
      data: formData,
    );
  }

}
