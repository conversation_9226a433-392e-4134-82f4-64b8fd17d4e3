import 'package:dio/dio.dart';
import 'package:erp/core/networks_helper/api_results/api_result.dart';
import 'package:erp/core/networks_helper/errors/exceptions.dart';
import 'package:erp/core/networks_helper/errors/failure.dart';
import 'package:erp/features/emplyee%20flow/projects/data/models/project_response_model.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/api%20services/lead_details_api_services.dart';
import 'package:erp/features/sales%20flow/lead%20details/data/model/lead_details_model.dart';

class LeadDetailsRepo {
  final LeadDetailsApiServices _leadDetailsApiServices;

  LeadDetailsRepo(this._leadDetailsApiServices);

  /// get list projects
  Future<ApiResult<ProjectResponse>> getListProjects(
      {required int page, required List<int> clientId}) async {
    final response = await _leadDetailsApiServices.getListProjects(
        page: page, clientId: clientId);

    try {
      if (response!.statusCode == 200 || response.statusCode == 201) {
        final model = ProjectResponse.fromJson(response.data);
        return ApiResult.success(model);
      } else {
        return ApiResult.failure(
          ServerException.fromResponse(response.statusCode, response),
        );
      }
    } on DioException catch (e) {
      try {
        handleDioException(e);
      } on ServerException catch (ex) {
        return ApiResult.failure(ex.errorModel.errorMessage);
      }
    }
    return ApiResult.failure(
      FailureException(errMessage: 'Unexpected error occurred'),
    );
  }

  Future<ApiResult<LeadDetailsModel>> getLeadDetails(
      {required int leadId}) async {
    final response =
        await _leadDetailsApiServices.getLeadDetails(leadId: leadId);
    if (response != null && response.statusCode == 200) {
      return ApiResult.success(LeadDetailsModel.fromJson(response.data));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }

  Future<ApiResult<LeadDetailsModel>> updateClient({
    required int clientId,
    required data,
  }) async {
    final response = await _leadDetailsApiServices.updateClient(
      clientId: clientId,
      data: data,
    );

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(LeadDetailsModel.fromJson(response.data));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }

  Future<ApiResult<String>> deleteClient({
    required int clientId,
  }) async {
    final response =
        await _leadDetailsApiServices.deleteClient(clientId: clientId);

    if (response != null && response.statusCode == 200) {
      return ApiResult.success("Client deleted successfully");
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }

  Future<ApiResult<ProjectModel>> createProject({
    required String name,
    required int clientId,
    required String startdate,
    required String deadline,
    required List<int> employees,
    required List<int> departments,
    required String notes,
    required String details,
    required List<MultipartFile> files,
  }) async {
    final response = await _leadDetailsApiServices.createProjects(
      name: name,
      clientId: clientId,
      startdate: startdate,
      deadline: deadline,
      employees: employees,
      departments: departments,
      notes: notes,
      details: details,
      files: files,
    );

    if (response != null && response.statusCode == 200) {
      return ApiResult.success(ProjectModel.fromJson(response.data["data"]));
    } else {
      return ApiResult.failure(
        ServerException.fromResponse(response?.statusCode, response),
      );
    }
  }
}
