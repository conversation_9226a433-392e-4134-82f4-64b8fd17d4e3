import 'package:json_annotation/json_annotation.dart';

part 'lead_details_model.g.dart';

@JsonSerializable()
class LeadDetailsModel {
  final LeadDetails data;
  final String status;
  final String error;
  final int code;

  LeadDetailsModel({
    required this.data,
    required this.status,
    required this.error,
    required this.code,
  });

  factory LeadDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$LeadDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$LeadDetailsModelToJson(this);
}

@JsonSerializable()
class LeadDetails {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String gender;
  final String country;
  final dynamic employee;
  final String type;

  @Json<PERSON><PERSON>(name: 'company_name')
  final String? companyName;

  @JsonKey(name: 'company_address')
  final String? companyAddress;

  final String image;

  @Json<PERSON>ey(name: 'company_logo')
  final String companyLogo;

  @JsonKey(name: 'company_phone')
  final String? companyPhone;

  final String? notes;

  @<PERSON>son<PERSON>ey(name: 'have_project')
  bool haveProject;
  @<PERSON>son<PERSON>ey(name: 'have_invoices')
  bool hasInvoice;
  @<PERSON>son<PERSON><PERSON>(name: 'have_quotes')
  bool haveQuotes;
  LeadDetails({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.gender,
    required this.country,
    required this.hasInvoice,
    required this.haveQuotes,
    this.employee,
    required this.type,
    this.companyName,
    this.companyAddress,
    required this.image,
    required this.companyLogo,
    this.companyPhone,
    this.notes,
    required this.haveProject,
  });

  factory LeadDetails.fromJson(Map<String, dynamic> json) =>
      _$LeadDetailsFromJson(json);

  Map<String, dynamic> toJson() => _$LeadDetailsToJson(this);
}
