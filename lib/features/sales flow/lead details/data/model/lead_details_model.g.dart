// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'lead_details_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LeadDetailsModel _$LeadDetailsModelFromJson(Map<String, dynamic> json) =>
    LeadDetailsModel(
      data: LeadDetails.fromJson(json['data'] as Map<String, dynamic>),
      status: json['status'] as String,
      error: json['error'] as String,
      code: (json['code'] as num).toInt(),
    );

Map<String, dynamic> _$LeadDetailsModelToJson(LeadDetailsModel instance) =>
    <String, dynamic>{
      'data': instance.data,
      'status': instance.status,
      'error': instance.error,
      'code': instance.code,
    };

LeadDetails _$LeadDetailsFromJson(Map<String, dynamic> json) => LeadDetails(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String,
      gender: json['gender'] as String,
      country: json['country'] as String,
      hasInvoice: json['have_invoices'] as bool,
      haveQuotes: json['have_quotes'] as bool,
      employee: json['employee'],
      type: json['type'] as String,
      companyName: json['company_name'] as String?,
      companyAddress: json['company_address'] as String?,
      image: json['image'] as String,
      companyLogo: json['company_logo'] as String,
      companyPhone: json['company_phone'] as String?,
      notes: json['notes'] as String?,
      haveProject: json['have_project'] as bool,
    );

Map<String, dynamic> _$LeadDetailsToJson(LeadDetails instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'phone': instance.phone,
      'gender': instance.gender,
      'country': instance.country,
      'employee': instance.employee,
      'type': instance.type,
      'company_name': instance.companyName,
      'company_address': instance.companyAddress,
      'image': instance.image,
      'company_logo': instance.companyLogo,
      'company_phone': instance.companyPhone,
      'notes': instance.notes,
      'have_project': instance.haveProject,
      'have_invoices': instance.hasInvoice,
      'have_quotes': instance.haveQuotes,
    };
