import 'package:cached_network_image/cached_network_image.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/features/emplyee%20flow/project%20details/presentation/widgets/custom_task_continer_widgt.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeadsWidget extends StatelessWidget {
  final List<Map<String, String>> projectInfo;
  final List<Map<String, String>> projectInfoDetails;

  final String note;

  final String companyLogo;
  const LeadsWidget(
      {super.key,
      required this.projectInfo,
      required this.note,
      required this.projectInfoDetails,
      required this.companyLogo});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 16.sp,
      children: [
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: projectInfo.map((info) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4.h),
                child: Row(
                  spacing: 10.sp,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(info["title"]!,
                          style: Styles.contentEmphasis.copyWith(
                            fontWeight: FontWeight.w600,
                          )),
                    ),
                    Expanded(
                      child: Text(info["value"]!,
                          textAlign: TextAlign.left,
                          style: Styles.contentEmphasis
                              .copyWith(fontWeight: FontWeight.w400)),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        CustomCard(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: projectInfoDetails.map((info) {
              return Padding(
                padding: EdgeInsets.symmetric(vertical: 4.h),
                child: Row(
                  spacing: 10.sp,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(info["title"]!,
                          style: Styles.contentEmphasis.copyWith(
                            fontWeight: FontWeight.w600,
                          )),
                    ),
                    Expanded(
                      child: Text(info["value"]!,
                          textAlign: TextAlign.left,
                          style: Styles.contentEmphasis
                              .copyWith(fontWeight: FontWeight.w400)),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
        CustomCard(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('clientsDetails.companyLogo'.tr(),
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                  )),
              CachedNetworkImage(
                imageUrl: companyLogo,
                height: 30.sp,
                width: 30.sp,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(),
                ),
                errorWidget: (context, url, error) =>
                    Center(child: const Icon(Icons.error)),
              ),
            ],
          ),
        ),
        CustomCard(
          child: Column(
            spacing: 12.sp,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('clientsDetails.notes'.tr(),
                  style: Styles.contentEmphasis.copyWith(
                    fontWeight: FontWeight.w600,
                  )),
              Text(
                note,
                style: Styles.contentEmphasis.copyWith(
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
