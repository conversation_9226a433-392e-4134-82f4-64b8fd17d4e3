import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/emplyee%20flow/home/<USER>/widgets/projects_item_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/quote_sales_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeadProjectsSection extends StatelessWidget {
  const LeadProjectsSection({super.key, required this.cubit});

  final LeadDetailsCubit cubit;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: BlocBuilder<LeadDetailsCubit, LeadDetailsState>(
            buildWhen: (previous, current) =>
                current is GetListProjectSalesSuccess ||
                current is CreateProjectSuccess,
            builder: (context, state) {
              final hasProjects =
                  cubit.leadDetailsModel?.data.haveProject ?? false;

              if (!hasProjects) {
                return NoProjectWidget(
                  title: 'No Projects Found',
                  description:
                      'To Add A Quote, Make Sure You’ve Created A Project First.',
                  buttomText: 'Add Project',
                  onPressed: () {
                    context.pushNamed(Routes.addProjectScreen,
                        arguments: cubit);
                  },
                );
              }

              final projects = cubit.getListProjectModel?.data.data ?? [];

              return ListView.separated(
                controller: cubit.scrollController,
                itemCount: projects.length,
                padding:
                    EdgeInsets.symmetric(vertical: 18.sp, horizontal: 16.sp),
                separatorBuilder: (context, index) => 16.sp.verticalSpace,
                itemBuilder: (context, index) {
                  final project = projects[index];
                  return ProjectsItemWidget(
                    onTap: () => context.pushNamed(
                      Routes.salesProjectDetails,
                      arguments: project.id,
                    ),
                    title: project.name,
                    description: project.deadline ?? '',
                  );
                },
              );
            },
          ),
        ),
        BlocBuilder<LeadDetailsCubit, LeadDetailsState>(
          buildWhen: (previous, current) =>
              current is GetListProjectsSalesLoadingMore ||
              current is GetListProjectSalesSuccess,
          builder: (context, state) {
            if (state is GetListProjectsSalesLoadingMore) {
              return const Center(
                child: CircularProgressIndicator(),
              );
            }
            return SizedBox.shrink();
          },
        ),
        10.verticalSpace,
        if (cubit.leadDetailsModel!.data.haveProject)
          CustomButtonWidget(
            text: 'lead.addProject'.tr(),
            onPressed: () => context.pushNamed(
              Routes.addProjectScreen,
              arguments: cubit,
            ),
          ),
        20.verticalSpace,
      ],
    );
  }
}
