import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class InvoiceWidgetSales extends StatelessWidget {
  const InvoiceWidgetSales(
      {super.key,
      required this.imagePath,
      required this.title,
      required this.description});
  final String imagePath;
  final String title;
  final String description;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.all(24.sp),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                imagePath,
                height: 200.h,
                fit: BoxFit.contain,
              ),
              32.verticalSpace,
              Text(
                title,
                textAlign: TextAlign.center,
                style: Styles.highlightBold.copyWith(
                  fontSize: 18.sp,
                  color: AppColors.neutralColor1200,
                ),
              ),
              12.verticalSpace,
              Text(
                description,
                textAlign: TextAlign.center,
                style: Styles.captionEmphasis.copyWith(
                  fontSize: 14.sp,
                  color: AppColors.neutralColor600,
                ),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        minimum: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 12.sp),
        child: CustomButtonWidget(
          text: "Add Project",
          onPressed: () {
            context.pushNamed(Routes.addProjectScreen,
                arguments: context.read<LeadDetailsCubit>());
          },
        ),
      ),
    );
  }
}
