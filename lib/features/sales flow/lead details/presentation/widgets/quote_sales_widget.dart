import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NoProjectWidget extends StatelessWidget {
  const NoProjectWidget({
    super.key,
    required this.title,
    required this.description,
    this.onPressed,
    required this.buttomText,
  });

  final String title;
  final String description;
  final VoidCallback? onPressed;
  final String buttomText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(24.sp),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Spacer(),
          Image.asset(
            "assets/images/pngs/no_project_found.png",
            height: 200.h,
            fit: BoxFit.contain,
          ),
          32.verticalSpace,
          Text(
            title,
            textAlign: TextAlign.center,
            style: Styles.highlightBold.copyWith(
              fontSize: 18.sp,
              color: AppColors.neutralColor1200,
            ),
          ),
          12.verticalSpace,
          Text(
            description,
            textAlign: TextAlign.center,
            style: Styles.captionEmphasis.copyWith(
              fontSize: 14.sp,
              color: AppColors.neutralColor600,
            ),
          ),
          const Spacer(),
          CustomButtonWidget(
            text: buttomText,
            onPressed: onPressed,
          ),
          12.verticalSpace,
        ],
      ),
    );
  }
}
