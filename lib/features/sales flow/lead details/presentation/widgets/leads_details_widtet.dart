import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/extensions/navigation_extension.dart';
import 'package:erp/core/helper_functions/flutter_toast.dart';
import 'package:erp/core/routing/routes_name.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/utils/easy_loading.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/leads_widge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeadDeatilsWidget extends StatelessWidget {
  const LeadDeatilsWidget({
    super.key,
    required this.cubit,
  });

  final LeadDetailsCubit cubit;

  @override
  Widget build(BuildContext context) {
    hideLoading();
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 18.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          LeadsWidget(
            projectInfo: [
              {
                "title": 'lead.email'.tr(),
                "value": cubit.leadDetailsModel!.data.email
              },
              {
                "title": 'lead.gender'.tr(),
                "value": cubit.leadDetailsModel!.data.gender
              },
              {
                "title": 'lead.phone'.tr(),
                "value": cubit.leadDetailsModel!.data.phone
              },
              {
                "title": 'lead.country'.tr(),
                "value": cubit.leadDetailsModel!.data.country
              },
            ],
            companyLogo: cubit.leadDetailsModel!.data.companyLogo,
            note: cubit.leadDetailsModel!.data.notes ?? "",
            projectInfoDetails: [
              {
                "title": 'lead.companyName'.tr(),
                "value": cubit.leadDetailsModel!.data.companyName ?? ""
              },
              {
                "title": 'lead.companyAddress'.tr(),
                "value": cubit.leadDetailsModel!.data.companyAddress ?? ""
              },
              {
                "title": 'lead.officePhoneNumber'.tr(),
                "value": cubit.leadDetailsModel!.data.companyPhone ?? ""
              },
            ],
          ),
          SizedBox(height: 50.sp),
          SafeArea(
            minimum: EdgeInsets.all(12.sp),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                BlocListener<LeadDetailsCubit, LeadDetailsState>(
                    listenWhen: (previous, current) =>
                        current is UpdateClientFailure ||
                        current is UpdateClientSuccess ||
                        current is UpdateClientLoading,
                    listener: (context, state) {
                      if (state is UpdateClientSuccess) {
                        Navigator.of(context).pop(1);
                      }
                    },
                    child: cubit.leadDetailsModel!.data.haveProject
                        ? CustomButtonWidget(
                            text: 'lead.changeToClient'.tr(),
                            onPressed: () {
                              if (cubit.leadDetailsModel!.data.haveQuotes) {
                                cubit.updateClient(type: "client");
                              } else {
                                customToast(
                                    msg: "pleaseaddQuotesToChaneTclient".tr(),
                                    color: AppColors.redColor200);
                              }
                            },
                          )
                        : CustomButtonWidget(
                            text: 'lead.addProject'.tr(),
                            onPressed: () {
                              context.pushNamed(Routes.addProjectScreen,
                                  arguments: cubit);
                            },
                          )),
                SizedBox(height: 10.sp),
                Row(
                  children: [
                    Expanded(
                      child: BlocListener<LeadDetailsCubit, LeadDetailsState>(
                        listenWhen: (previous, current) =>
                            current is DeleteClientFailure ||
                            current is DeleteClientSuccess ||
                            current is DeleteClientLoading,
                        listener: (context, state) {
                          if (state is DeleteClientSuccess) {
                            Navigator.of(context).pop(true);
                          }
                        },
                        child: CustomButtonWidget(
                          text: 'lead.delete'.tr(),
                          textColor: AppColors.primaryColor800,
                          color: Colors.white,
                          borderColor:
                              BorderSide(color: AppColors.primaryColor800),
                          onPressed: () {
                            cubit.deleteClient();
                          },
                        ),
                      ),
                    ),
                    SizedBox(width: 20.sp),
                    Expanded(
                      child: CustomButtonWidget(
                        text: 'lead.edit'.tr(),
                        color: Colors.white,
                        textColor: AppColors.primaryColor800,
                        borderColor:
                            BorderSide(color: AppColors.primaryColor800),
                        onPressed: () {
                          context
                              .pushNamed(Routes.editLeadScreen,
                                  arguments: cubit)
                              .then((v) {
                            if (v == true) {
                              cubit.emit(UpdateAfterEditState());
                            }
                          });
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
