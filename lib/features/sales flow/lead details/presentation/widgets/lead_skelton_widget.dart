import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/leads_widge.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class LeadSkeltonWidget extends StatelessWidget {
  const LeadSkeltonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Scaffold(
        appBar: AppBar(
          title: Text("ahmed hossam"),
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.pop(context),
          ),
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.symmetric(
            horizontal: 12.sp,
            vertical: 18.sp,
          ),
          child: LeadsWidget(
            projectInfo: [
              {"title": "Email", "value": "<EMAIL>"},
              {
                "title": "Gender",
                "value": "Male",
              },
              {"title": "Phone", "value": "0123456789"},
              {"title": "country", "value": "123 Street, City"},
            ],
            note: "Undergoing surgery that requires recovery time.",
            projectInfoDetails: [
              {"title": "company name", "value": "JEC"},
              {
                "title": "Company Address",
                "value": "Giza",
              },
              {"title": "Office Phone Number", "value": "01023359621"},
            ],
            companyLogo: '',
          ),
        ),
        bottomNavigationBar: SafeArea(
          minimum: EdgeInsets.all(12.sp),
          child: Column(
            spacing: 10.sp,
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomButtonWidget(
                text: "Change To Clinet",
                onPressed: () {},
              ),
              Row(
                spacing: 20.sp,
                children: [
                  Expanded(
                    child: CustomButtonWidget(
                      text: "delete",
                      textColor: AppColors.primaryColor800,
                      color: Colors.white,
                      borderColor: BorderSide(color: AppColors.primaryColor800),
                      onPressed: () {},
                    ),
                  ),
                  Expanded(
                    child: CustomButtonWidget(
                      text: "Edit",
                      color: Colors.white,
                      textColor: AppColors.primaryColor800,
                      borderColor: BorderSide(color: AppColors.primaryColor800),
                      onPressed: () {},
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
