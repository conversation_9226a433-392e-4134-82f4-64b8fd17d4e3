import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/helper_functions/validation.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/core/themes/text_colors.dart';
import 'package:erp/core/utils/app_constants.dart';
import 'package:erp/core/widgets/custom_button_widget.dart';
import 'package:erp/core/widgets/custom_drop_down_widget.dart';
import 'package:erp/core/widgets/custom_text_form_field_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class EditLeadScreen extends StatelessWidget {
  const EditLeadScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeadDetailsCubit>();
 

    return Scaffold(
      appBar: AppBar(
        title: Text("Edit Lead".tr()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 20.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            buildField("Name", cubit.nameController,
                validator: AppValidator.validateRequired),
            buildField("Email", cubit.emailController,
                keypordType: TextInputType.emailAddress,
                validator: AppValidator.validateEmail),
            buildField("Phone", cubit.phoneController,
                keypordType: TextInputType.phone,
                validator: AppValidator.validatePhoneNumber),
            Text("Gender", style: Styles.contentEmphasis),
            8.verticalSpace,
            CustomDropdownButton<String>(
              value: cubit.genderController.text,
              getItemText: (p0) {
                return p0;
              },
              isString: true,
              items: const ['male', 'female', 'other'],
              hint: "Select Gender",
              onChanged: (value) {
                cubit.genderController.text = value!;
              },
            ),
            16.verticalSpace,
            buildField("Country", cubit.countryController,
                validator: AppValidator.validateRequired),
            buildField("Company Name", cubit.companyNameController,
                validator: AppValidator.validateRequired),
            buildField("Company Address", cubit.companyAddressController,
                validator: AppValidator.validateRequired),
            buildField("Company Phone", cubit.companyPhoneController,
                keypordType: TextInputType.phone,
                validator: AppValidator.validatePhoneNumber),
            buildField("Notes", cubit.notesController,
                maxLines: 3, validator: AppValidator.validateRequired),
            20.verticalSpace,
            BlocBuilder<LeadDetailsCubit, LeadDetailsState>(
              buildWhen: (previous, current) => current is PickCompanyImage,
              builder: (context, state) {
                return Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => cubit.pickCompanyLogo(),
                        child: const Text("Pick Company Logo"),
                      ),
                    ),
                    if (cubit.companyLogo != null)
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Icon(Icons.check_circle, color: Colors.green),
                      ),
                  ],
                );
              },
            ),
            12.verticalSpace,
            BlocBuilder<LeadDetailsCubit, LeadDetailsState>(
              buildWhen: (previous, current) => current is PickClientImage,
              builder: (context, state) {
                return Row(
                  children: [
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => cubit.pickProfileImage(),
                        child: const Text("Pick Profile Image"),
                      ),
                    ),
                    if (cubit.profileImage != null)
                      const Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Icon(Icons.check_circle, color: Colors.green),
                      ),
                  ],
                );
              },
            ),
            30.verticalSpace,
            BlocListener<LeadDetailsCubit, LeadDetailsState>(
              listenWhen: (previous, current) =>
                  current is UpdateClientFailure ||
                  current is UpdateClientLoading ||
                  current is UpdateClientSuccess,
              listener: (context, state) {
                if (state is UpdateClientSuccess) {
                  Navigator.of(context).pop(true);
                }
              },
              child: CustomButtonWidget(
                text: "Update".tr(),
                onPressed: () => cubit.updateClient(),
                color: AppColors.primaryColor800,
                textColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildField(String label, TextEditingController controller,
      {String? Function(String?)? validator, int maxLines = 1, keypordType}) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label.tr(), style: Styles.contentEmphasis),
          8.verticalSpace,
          CustomTextFormFieldWidget(
            controller: controller,
            hintText: "Enter $label".tr(),
            validator: validator,
            keyboardType: keypordType ?? TextInputType.text,
            borderRadius: AppConstants.borderRadius,
            maxLines: maxLines,
          ),
        ],
      ),
    );
  }
}
