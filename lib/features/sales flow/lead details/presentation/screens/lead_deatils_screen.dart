import 'package:easy_localization/easy_localization.dart';
import 'package:erp/core/themes/app_colors.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_cubit.dart';
import 'package:erp/features/sales%20flow/lead%20details/bloc/cubit/lead_details_state.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/lead_skelton_widget.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/leads_details_widtet.dart';
import 'package:erp/features/sales%20flow/lead%20details/presentation/widgets/project_list_widgte.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeadDeatilsScreen extends StatelessWidget {
  const LeadDeatilsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final cubit = context.read<LeadDetailsCubit>();
    return BlocBuilder<LeadDetailsCubit, LeadDetailsState>(
      buildWhen: (previous, current) =>
          current is LeadDetailsLoading ||
          current is LeadDetailsSuccess ||
          current is UpdateAfterEditState ||
          current is LeadDetailsFailure,
      builder: (context, state) {
        if (state is LeadDetailsLoading) {
          return const LeadSkeltonWidget();
        }

        return WillPopScope(
          onWillPop: () async {
            Navigator.of(context).pop(cubit.leadDetailsModel);
            return true;
          },
          child: DefaultTabController(
            length: 2,
            child: Scaffold(
              appBar: AppBar(
                title: Text(cubit.leadDetailsModel!.data.name),
                leading: IconButton(
                  icon: const Icon(Icons.arrow_back),
                  onPressed: () =>
                      Navigator.of(context).pop(cubit.leadDetailsModel),
                ),
                bottom: TabBar(
                  dividerHeight: 0.h,
                  dividerColor: AppColors.neutralColor300,
                  labelColor: AppColors.primaryColor800,
                  unselectedLabelColor: Colors.grey,
                  indicatorColor: AppColors.primaryColor800,
                  tabs: [
                    Tab(text: 'lead.accountDetails'.tr()),
                    Tab(text: 'lead.projects'.tr()),
                  ],
                ),
              ),
              body: TabBarView(
                children: [
                  LeadDeatilsWidget(cubit: cubit),
                  LeadProjectsSection(cubit: cubit),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
